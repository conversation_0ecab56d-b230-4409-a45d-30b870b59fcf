<?php

return [
    // Page titles
    'index_title' => 'Requests - FPCT Church Management System (CMS)',
    'create_title' => 'Create New Request - FPCT Church Management System (CMS)',
    'edit_title' => 'Edit Request - FPCT Church Management System (CMS)',
    'show_title' => 'Request Details - FPCT Church Management System (CMS)',
    
    // Page headers
    'request_management' => 'Request Management',
    'create_new_request' => 'Create New Request',
    'edit_request' => 'Edit Request',
    'request_details' => 'Request Details',
    'my_requests' => 'My Requests',
    'all_requests' => 'All Requests',
    'pending_requests' => 'Pending Requests',
    'manage_requests_system' => 'Manage requests in the FPCT Church Management System (CMS)',
    
    // Request types
    'upgrade_branch_to_parish' => 'Upgrade Branch to Parish',
    'upgrade_parish_to_local' => 'Upgrade Parish to Local',
    'upgrade_local_to_diocese' => 'Upgrade Local to Diocese',
    'new_church_registration' => 'New Church Registration',
    'leadership_change' => 'Leadership Change',
    'church_relocation' => 'Church Relocation',
    'other_request' => 'Other Request',
    
    // Form fields
    'request_type' => 'Request Type',
    'church' => 'Church',
    'description' => 'Description',
    'justification' => 'Justification',
    'supporting_documents' => 'Supporting Documents',
    'priority' => 'Priority',
    'expected_completion' => 'Expected Completion Date',
    'additional_notes' => 'Additional Notes',
    
    // Placeholders
    'select_request_type' => 'Select Request Type',
    'select_church' => 'Select Church',
    'enter_description' => 'Enter request description',
    'enter_justification' => 'Enter justification for this request',
    'select_priority' => 'Select Priority',
    'enter_notes' => 'Enter any additional notes',
    'choose_files' => 'Choose files to upload',
    
    // Priority levels
    'low' => 'Low',
    'normal' => 'Normal',
    'high' => 'High',
    'urgent' => 'Urgent',
    
    // Status
    'pending' => 'Pending',
    'under_review' => 'Under Review',
    'approved' => 'Approved',
    'rejected' => 'Rejected',
    'completed' => 'Completed',
    'cancelled' => 'Cancelled',
    
    // Actions
    'create_request' => 'Create Request',
    'submit_request' => 'Submit Request',
    'save_request' => 'Save Request',
    'update_request' => 'Update Request',
    'approve_request' => 'Approve Request',
    'reject_request' => 'Reject Request',
    'cancel_request' => 'Cancel Request',
    'view_request' => 'View Request',
    'edit_request_action' => 'Edit Request',
    'delete_request' => 'Delete Request',
    
    // Table headers
    'type' => 'Type',
    'church_name' => 'Church',
    'requested_by' => 'Requested By',
    'status' => 'Status',
    'priority_header' => 'Priority',
    'created_at' => 'Created',
    'updated_at' => 'Updated',
    'actions' => 'Actions',
    
    // Statistics
    'total_requests' => 'Total Requests',
    'pending_approval' => 'Pending Approval',
    'pending_approvals' => 'Pending Approvals',
    'approved_requests' => 'Approved Requests',
    'rejected_requests' => 'Rejected Requests',
    'requests_by_type' => 'Requests by Type',
    'requests_by_status' => 'Requests by Status',
    'recent_requests' => 'Recent Requests',
    
    // Messages
    'request_created' => 'Request created successfully',
    'request_updated' => 'Request updated successfully',
    'request_submitted' => 'Request submitted successfully',
    'request_approved' => 'Request approved successfully',
    'request_rejected' => 'Request rejected successfully',
    'request_cancelled' => 'Request cancelled successfully',
    'request_deleted' => 'Request deleted successfully',
    'no_requests_found' => 'No requests found',
    'approval_required' => 'This request requires approval from higher authorities',
    
    // Confirmations
    'confirm_submit' => 'Are you sure you want to submit this request?',
    'confirm_approve' => 'Are you sure you want to approve this request?',
    'confirm_reject' => 'Are you sure you want to reject this request?',
    'confirm_cancel' => 'Are you sure you want to cancel this request?',
    'confirm_delete' => 'Are you sure you want to delete this request?',
    'submit_warning' => 'Once submitted, you may not be able to edit this request.',
    'approval_warning' => 'This action will approve the request and notify the requester.',
    'rejection_warning' => 'This action will reject the request. Please provide a reason.',
    
    // Filters
    'filter_by_type' => 'Filter by Type',
    'filter_by_status' => 'Filter by Status',
    'filter_by_church' => 'Filter by Church',
    'filter_by_priority' => 'Filter by Priority',
    'search_requests' => 'Search requests...',
    'all_types' => 'All Types',
    'all_statuses' => 'All Statuses',
    'all_churches' => 'All Churches',
    'all_priorities' => 'All Priorities',
    
    // Details
    'request_information' => 'Request Information',
    'church_information' => 'Church Information',
    'approval_information' => 'Approval Information',
    'timeline' => 'Timeline',
    'documents' => 'Documents',
    'comments' => 'Comments',
    'approval_history' => 'Approval History',
    'submitted_on' => 'Submitted on',
    'approved_by' => 'Approved by',
    'approved_on' => 'Approved on',
    'rejected_by' => 'Rejected by',
    'rejected_on' => 'Rejected on',
    'rejection_reason' => 'Rejection Reason',
    'no_documents' => 'No documents uploaded',
    'download_document' => 'Download Document',
    
    // Approval process
    'approval_progress' => 'Approval Progress',
    'approval_steps' => 'Approval Steps',
    'current_step' => 'Current Step',
    'next_approver' => 'Next Approver',
    'approval_required_from' => 'Approval required from',
    'approvals_completed' => 'approvals completed',
    'awaiting_approval' => 'Awaiting Approval',
    'approval_chain' => 'Approval Chain',
    
    // Validation
    'type_required' => 'Request type is required',
    'church_required' => 'Church selection is required',
    'description_required' => 'Description is required',
    'justification_required' => 'Justification is required',
    'priority_required' => 'Priority is required',
    'invalid_file_type' => 'Invalid file type. Only PDF, DOC, DOCX, and images are allowed',
    'file_too_large' => 'File size too large. Maximum 10MB allowed',
    'description_min' => 'Description must be at least 10 characters',
    'justification_min' => 'Justification must be at least 20 characters',
    
    // Breadcrumbs
    'requests' => 'Requests',
    'create' => 'Create',
    'edit_breadcrumb' => 'Edit',
    'details' => 'Details',
    'my_requests_breadcrumb' => 'My Requests',
];
