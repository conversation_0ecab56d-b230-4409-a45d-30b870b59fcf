<?php

namespace App\Http\Requests;

use App\Enums\ChurchLevel;
use App\Models\Church;
use App\Services\ChurchHierarchyService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateChurchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasPermissionTo('create-churches');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'level' => ['required', Rule::enum(ChurchLevel::class)],
            'location' => ['required', 'string', 'max:255'],
            'date_established' => ['nullable', 'date', 'before_or_equal:today'],
            'parent_church_id' => ['nullable', 'exists:churches,id'],
            'youth_count' => ['nullable', 'integer', 'min:0'],
            'young_adults_count' => ['nullable', 'integer', 'min:0'],
            'children_count' => ['nullable', 'integer', 'min:0'],
            'elders_count' => ['nullable', 'integer', 'min:0'],
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $this->validateHierarchy($validator);
            $this->validateUserPermissions($validator);
        });
    }

    /**
     * Validate church hierarchy rules.
     */
    protected function validateHierarchy($validator)
    {
        $level = ChurchLevel::tryFrom($this->input('level'));
        $parentId = $this->input('parent_church_id');

        if (!$level) {
            return;
        }

        // National level should not have a parent
        if ($level === ChurchLevel::NATIONAL && $parentId) {
            $validator->errors()->add('parent_church_id', 'National level church cannot have a parent church.');
            return;
        }

        // Other levels must have a parent
        if ($level !== ChurchLevel::NATIONAL && !$parentId) {
            $validator->errors()->add('parent_church_id', 'This church level requires a parent church.');
            return;
        }

        if ($parentId) {
            $parentChurch = Church::find($parentId);

            if ($parentChurch && !$parentChurch->canHaveChildLevel($level)) {
                $validator->errors()->add('level', "A {$parentChurch->level->value} church cannot have a {$level->value} child church.");
            }
        }
    }

    /**
     * Validate user permissions for creating church at this level.
     */
    protected function validateUserPermissions($validator)
    {
        $level = ChurchLevel::tryFrom($this->input('level'));
        $parentId = $this->input('parent_church_id');

        if (!$level) {
            return;
        }

        $hierarchyService = app(ChurchHierarchyService::class);
        $parentChurch = $parentId ? Church::find($parentId) : null;

        if (!$hierarchyService->canCreateChurchAtLevel($this->user(), $level, $parentChurch)) {
            $validator->errors()->add('level', 'You do not have permission to create a church at this level.');
        }
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Church name is required.',
            'level.required' => 'Church level is required.',
            'location.required' => 'Church location is required.',
            'date_established.date' => 'Please provide a valid date.',
            'date_established.before_or_equal' => 'Establishment date cannot be in the future.',
            'parent_church_id.exists' => 'Selected parent church does not exist.',
        ];
    }
}
