<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Models\AuditLog;

class PermissionController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'permission:manage-permissions']);
    }

    // List all permissions
    public function index(Request $request)
    {
        $query = Permission::with(['roles']);
        
        // Search functionality
        if ($request->search) {
            $query->where('name', 'like', "%{$request->search}%");
        }
        
        // Filter by role
        if ($request->role) {
            $query->whereHas('roles', function ($q) use ($request) {
                $q->where('name', $request->role);
            });
        }
        
        $permissions = $query->withCount('roles')
                            ->orderBy('name')
                            ->paginate(20);
        
        $roles = Role::orderBy('name')->get();
        $groupedPermissions = $this->groupPermissions($permissions->items());
        
        return view('permissions.index', compact('permissions', 'roles', 'groupedPermissions'));
    }

    // Show permission details
    public function show(Permission $permission)
    {
        $permission->load(['roles.users']);
        
        return view('permissions.show', compact('permission'));
    }

    // Show create permission form
    public function create()
    {
        $roles = Role::orderBy('name')->get();
        
        return view('permissions.create', compact('roles'));
    }

    // Store new permission
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:permissions,name',
            'roles' => 'array',
            'roles.*' => 'exists:roles,name'
        ]);

        DB::beginTransaction();
        try {
            $permission = Permission::create(['name' => $request->name]);
            
            if ($request->roles) {
                foreach ($request->roles as $roleName) {
                    $role = Role::findByName($roleName);
                    $role->givePermissionTo($permission);
                }
            }
            
            AuditLog::log(
                'permission_created',
                $permission,
                [],
                $permission->toArray(),
                "Permission '{$permission->name}' created by " . Auth::user()->full_name
            );
            
            DB::commit();
            
            return redirect()->route('permissions.index')
                           ->with('success', 'Permission created successfully.');
                           
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to create permission: ' . $e->getMessage()]);
        }
    }

    // Show edit permission form
    public function edit(Permission $permission)
    {
        $roles = Role::orderBy('name')->get();
        $permissionRoles = $permission->roles->pluck('name')->toArray();
        
        return view('permissions.edit', compact('permission', 'roles', 'permissionRoles'));
    }

    // Update permission
    public function update(Request $request, Permission $permission)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:permissions,name,' . $permission->id,
            'roles' => 'array',
            'roles.*' => 'exists:roles,name'
        ]);

        DB::beginTransaction();
        try {
            $oldData = $permission->toArray();
            $oldRoles = $permission->roles->pluck('name')->toArray();
            
            $permission->update(['name' => $request->name]);
            
            // Sync roles
            $newRoles = $request->roles ?? [];
            
            // Remove permission from all roles first
            foreach ($permission->roles as $role) {
                $role->revokePermissionTo($permission);
            }
            
            // Add permission to selected roles
            foreach ($newRoles as $roleName) {
                $role = Role::findByName($roleName);
                $role->givePermissionTo($permission);
            }
            
            AuditLog::log(
                'permission_updated',
                $permission,
                array_merge($oldData, ['roles' => $oldRoles]),
                array_merge($permission->fresh()->toArray(), ['roles' => $newRoles]),
                "Permission '{$permission->name}' updated by " . Auth::user()->full_name
            );
            
            DB::commit();
            
            return redirect()->route('permissions.index')
                           ->with('success', 'Permission updated successfully.');
                           
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to update permission: ' . $e->getMessage()]);
        }
    }

    // Delete permission
    public function destroy(Permission $permission)
    {
        DB::beginTransaction();
        try {
            $permissionName = $permission->name;
            $permissionData = $permission->toArray();
            
            $permission->delete();
            
            AuditLog::log(
                'permission_deleted',
                null,
                $permissionData,
                [],
                "Permission '{$permissionName}' deleted by " . Auth::user()->full_name
            );
            
            DB::commit();
            
            return redirect()->route('permissions.index')
                           ->with('success', 'Permission deleted successfully.');
                           
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to delete permission: ' . $e->getMessage()]);
        }
    }

    // Helper method to group permissions
    private function groupPermissions($permissions)
    {
        $grouped = [];
        
        foreach ($permissions as $permission) {
            $parts = explode('-', $permission->name);
            $action = $parts[0];
            $resource = isset($parts[1]) ? $parts[1] : 'general';
            
            if (!isset($grouped[$resource])) {
                $grouped[$resource] = [];
            }
            
            $grouped[$resource][] = $permission;
        }
        
        return $grouped;
    }
}
