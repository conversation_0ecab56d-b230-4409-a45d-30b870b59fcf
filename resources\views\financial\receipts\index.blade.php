@extends('layouts.app')

@section('title', __('Receipts'))

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ __('Transaction Receipts') }}</h1>
                    <p class="mt-2 text-gray-600">{{ __('Manage and track all transaction receipts') }}</p>
                </div>
                <div class="flex space-x-3">
                    @can('generate-receipts')
                        <a href="{{ route('receipts.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-plus mr-2"></i>{{ __('Generate Receipt') }}
                        </a>
                    @endcan
                    <div class="relative">
                        <button type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="toggleBulkDropdown()">
                            <i class="fas fa-tools mr-2"></i>{{ __('Bulk Actions') }}
                            <i class="fas fa-chevron-down ml-2"></i>
                        </button>
                        <div id="bulkDropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                            <div class="py-1">
                                <a href="#" onclick="bulkGenerate()" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-receipt mr-2"></i>{{ __('Bulk Generate') }}
                                </a>
                                <a href="#" onclick="bulkEmail()" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-envelope mr-2"></i>{{ __('Bulk Email') }}
                                </a>
                                <a href="#" onclick="bulkDownload()" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-download mr-2"></i>{{ __('Bulk Download') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Receipts -->
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-white bg-opacity-30 rounded-md flex items-center justify-center">
                                <i class="fas fa-receipt text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-blue-100 truncate uppercase tracking-wide">{{ __('Total Receipts') }}</dt>
                                <dd class="text-lg font-medium text-white">{{ $receipts->total() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Amount -->
            <div class="bg-gradient-to-r from-green-500 to-green-600 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-white bg-opacity-30 rounded-md flex items-center justify-center">
                                <i class="fas fa-coins text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-green-100 truncate uppercase tracking-wide">{{ __('Total Amount') }}</dt>
                                <dd class="text-lg font-medium text-white">{{ number_format($receipts->sum('amount'), 2) }} TZS</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Emailed Receipts -->
            <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-white bg-opacity-30 rounded-md flex items-center justify-center">
                                <i class="fas fa-envelope text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-indigo-100 truncate uppercase tracking-wide">{{ __('Emailed') }}</dt>
                                <dd class="text-lg font-medium text-white">{{ $receipts->where('is_emailed', true)->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- This Month -->
            <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-white bg-opacity-30 rounded-md flex items-center justify-center">
                                <i class="fas fa-calendar-alt text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-yellow-100 truncate uppercase tracking-wide">{{ __('This Month') }}</dt>
                                <dd class="text-lg font-medium text-white">{{ $receipts->where('created_at', '>=', now()->startOfMonth())->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Card -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-filter mr-2 text-blue-600"></i>
                    {{ __('Filters') }}
                </h3>
            </div>
            <div class="p-6">
                <form method="GET" action="{{ route('receipts.index') }}" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                            <label for="receipt_type" class="block text-sm font-medium text-gray-700 mb-2">{{ __('Receipt Type') }}</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="receipt_type" name="receipt_type">
                                <option value="">{{ __('All Types') }}</option>
                                <option value="payment" {{ request('receipt_type') == 'payment' ? 'selected' : '' }}>{{ __('Payment') }}</option>
                                <option value="contribution" {{ request('receipt_type') == 'contribution' ? 'selected' : '' }}>{{ __('Contribution') }}</option>
                                <option value="transfer" {{ request('receipt_type') == 'transfer' ? 'selected' : '' }}>{{ __('Transfer') }}</option>
                            </select>
                        </div>
                        <div>
                            <label for="date_from" class="block text-sm font-medium text-gray-700 mb-2">{{ __('From Date') }}</label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="date_from" name="date_from" value="{{ request('date_from') }}">
                        </div>
                        <div>
                            <label for="date_to" class="block text-sm font-medium text-gray-700 mb-2">{{ __('To Date') }}</label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="date_to" name="date_to" value="{{ request('date_to') }}">
                        </div>
                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700 mb-2">{{ __('Search') }}</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="search" name="search" value="{{ request('search') }}" placeholder="{{ __('Receipt number, description...') }}">
                        </div>
                    </div>
                    <div class="flex flex-wrap gap-3 pt-4">
                        <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            <i class="fas fa-search mr-2"></i>{{ __('Filter') }}
                        </button>
                        <a href="{{ route('receipts.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            <i class="fas fa-times mr-2"></i>{{ __('Clear') }}
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Receipts Table -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-receipt mr-2 text-blue-600"></i>
                    {{ __('Receipts List') }}
                </h3>
                <div class="flex items-center">
                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" id="selectAll" onchange="toggleSelectAll()">
                    <label class="ml-2 text-sm text-gray-700" for="selectAll">{{ __('Select All') }}</label>
                </div>
            </div>
            <div class="overflow-hidden">
                @if($receipts->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="w-12 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" id="selectAllHeader" onchange="toggleSelectAll()">
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Receipt Number') }}</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Date') }}</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Type') }}</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Amount') }}</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Issued To') }}</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Issued By') }}</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Status') }}</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Actions') }}</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($receipts as $receipt)
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded receipt-checkbox" value="{{ $receipt->id }}">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ $receipt->receipt_number }}</div>
                                            @if($receipt->transaction)
                                                <div class="text-sm text-gray-500">{{ $receipt->transaction->transaction_id }}</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $receipt->created_at->format('M j, Y H:i') }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">{{ $receipt->getReceiptTypeLabel() }}</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">{{ $receipt->getFormattedAmount() }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $receipt->issuedToChurch->name }}</div>
                                            <div class="text-xs text-gray-500">{{ ucfirst($receipt->issuedToChurch->level->value) }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $receipt->issuedByChurch->name }}</div>
                                            <div class="text-xs text-gray-500">{{ $receipt->issuedByUser->full_name }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @if($receipt->is_emailed)
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    <i class="fas fa-envelope mr-1"></i>{{ __('Emailed') }}
                                                </span>
                                                <div class="text-xs text-gray-500 mt-1">{{ $receipt->emailed_at->format('M j, H:i') }}</div>
                                            @else
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">{{ __('Not Emailed') }}</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <a href="{{ route('receipts.show', $receipt) }}" class="inline-flex items-center p-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200" title="{{ __('View Details') }}">
                                                    <i class="fas fa-eye text-sm"></i>
                                                </a>

                                                @if($receipt->pdf_path)
                                                    <a href="{{ route('receipts.pdf', $receipt) }}" class="inline-flex items-center p-2 border border-red-300 rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200" title="{{ __('Download PDF') }}">
                                                        <i class="fas fa-file-pdf text-sm"></i>
                                                    </a>
                                                @endif

                                                <button type="button" class="inline-flex items-center p-2 border border-blue-300 rounded-md text-blue-700 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200" onclick="emailReceipt({{ $receipt->id }})" title="{{ __('Email Receipt') }}">
                                                    <i class="fas fa-envelope text-sm"></i>
                                                </button>

                                                <a href="{{ route('receipts.preview', $receipt) }}" class="inline-flex items-center p-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200" title="{{ __('Preview') }}">
                                                    <i class="fas fa-search text-sm"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="bg-white px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            {{ __('Showing') }} {{ $receipts->firstItem() }} {{ __('to') }} {{ $receipts->lastItem() }} {{ __('of') }} {{ $receipts->total() }} {{ __('results') }}
                        </div>
                        <div>
                            {{ $receipts->appends(request()->query())->links() }}
                        </div>
                    </div>
                @else
                    <div class="text-center py-16">
                        <div class="mx-auto h-24 w-24 text-gray-300 mb-4">
                            <i class="fas fa-receipt text-6xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">{{ __('No receipts found') }}</h3>
                        <p class="text-gray-500 mb-6">{{ __('No receipts match your current filters.') }}</p>
                        @can('generate-receipts')
                            <a href="{{ route('receipts.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                                <i class="fas fa-plus mr-2"></i>{{ __('Generate First Receipt') }}
                            </a>
                        @endcan
                    </div>
                @endif
            </div>
        </div>
</div>

<!-- Email Receipt Modal -->
<div id="emailModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onclick="closeEmailModal()"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <form id="emailForm" method="POST">
                @csrf
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                            <i class="fas fa-envelope text-blue-600"></i>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">{{ __('Email Receipt') }}</h3>
                            <div class="mt-4">
                                <label for="email_addresses" class="block text-sm font-medium text-gray-700 mb-2">{{ __('Email Addresses') }} <span class="text-red-500">*</span></label>
                                <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="email_addresses" name="email_addresses[]" rows="3" required placeholder="{{ __('Enter email addresses separated by commas...') }}"></textarea>
                                <p class="mt-2 text-sm text-gray-500">{{ __('Separate multiple email addresses with commas') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                        <i class="fas fa-envelope mr-2"></i>{{ __('Send Receipt') }}
                    </button>
                    <button type="button" onclick="closeEmailModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        {{ __('Cancel') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Actions Modal -->
<div id="bulkModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="bulk-modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onclick="closeBulkModal()"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-tools text-blue-600"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900" id="bulkModalTitle">{{ __('Bulk Action') }}</h3>
                        <div class="mt-4" id="bulkContent">
                            <!-- Content will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="bulkActionBtn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                    {{ __('Execute') }}
                </button>
                <button type="button" onclick="closeBulkModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    {{ __('Cancel') }}
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function toggleBulkDropdown() {
    const dropdown = document.getElementById('bulkDropdown');
    dropdown.classList.toggle('hidden');
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const dropdown = document.getElementById('bulkDropdown');
    const button = event.target.closest('button');

    if (!button || !button.onclick || button.onclick.toString().indexOf('toggleBulkDropdown') === -1) {
        dropdown.classList.add('hidden');
    }
});

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll') || document.getElementById('selectAllHeader');
    const checkboxes = document.querySelectorAll('.receipt-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    // Sync both select all checkboxes
    document.getElementById('selectAll').checked = selectAll.checked;
    document.getElementById('selectAllHeader').checked = selectAll.checked;
}

function emailReceipt(receiptId) {
    const form = document.getElementById('emailForm');
    form.action = `/receipts/${receiptId}/email`;
    document.getElementById('emailModal').classList.remove('hidden');
}

function closeEmailModal() {
    document.getElementById('emailModal').classList.add('hidden');
}

function closeBulkModal() {
    document.getElementById('bulkModal').classList.add('hidden');
}

function getSelectedReceipts() {
    return Array.from(document.querySelectorAll('.receipt-checkbox:checked')).map(cb => cb.value);
}

function bulkGenerate() {
    const selected = getSelectedReceipts();
    if (selected.length === 0) {
        alert('{{ __("Please select at least one receipt") }}');
        return;
    }

    document.getElementById('bulkModalTitle').textContent = '{{ __("Bulk Generate Receipts") }}';
    document.getElementById('bulkContent').innerHTML = `
        <p class="text-sm text-gray-600 mb-4">{{ __("Generate receipts for") }} ${selected.length} {{ __("selected transactions?") }}</p>
        <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-blue-700">{{ __("This will create PDF receipts for all selected transactions that don't already have receipts.") }}</p>
                </div>
            </div>
        </div>
    `;

    document.getElementById('bulkActionBtn').onclick = function() {
        // In a real implementation, you would make an AJAX call here
        alert('{{ __("Bulk receipt generation functionality would be implemented here") }}');
        closeBulkModal();
    };

    document.getElementById('bulkModal').classList.remove('hidden');
}

function bulkEmail() {
    const selected = getSelectedReceipts();
    if (selected.length === 0) {
        alert('{{ __("Please select at least one receipt") }}');
        return;
    }

    document.getElementById('bulkModalTitle').textContent = '{{ __("Bulk Email Receipts") }}';
    document.getElementById('bulkContent').innerHTML = `
        <div class="mb-4">
            <label for="bulkEmails" class="block text-sm font-medium text-gray-700 mb-2">{{ __("Email Addresses") }}</label>
            <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="bulkEmails" rows="3" placeholder="{{ __("Enter email addresses separated by commas...") }}"></textarea>
        </div>
        <p class="text-sm text-gray-600">{{ __("Email") }} ${selected.length} {{ __("selected receipts") }}</p>
    `;

    document.getElementById('bulkActionBtn').onclick = function() {
        const emails = document.getElementById('bulkEmails').value;
        if (!emails.trim()) {
            alert('{{ __("Please enter at least one email address") }}');
            return;
        }

        // In a real implementation, you would make an AJAX call here
        alert('{{ __("Bulk email functionality would be implemented here") }}');
        closeBulkModal();
    };

    document.getElementById('bulkModal').classList.remove('hidden');
}

function bulkDownload() {
    const selected = getSelectedReceipts();
    if (selected.length === 0) {
        alert('{{ __("Please select at least one receipt") }}');
        return;
    }

    document.getElementById('bulkModalTitle').textContent = '{{ __("Bulk Download Receipts") }}';
    document.getElementById('bulkContent').innerHTML = `
        <p class="text-sm text-gray-600 mb-4">{{ __("Download") }} ${selected.length} {{ __("selected receipts as a ZIP file?") }}</p>
        <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-blue-700">{{ __("All selected receipt PDFs will be packaged into a single ZIP file for download.") }}</p>
                </div>
            </div>
        </div>
    `;

    document.getElementById('bulkActionBtn').onclick = function() {
        // In a real implementation, you would create a bulk download endpoint
        alert('{{ __("Bulk download functionality would be implemented here") }}');
        closeBulkModal();
    };

    document.getElementById('bulkModal').classList.remove('hidden');
}
</script>
@endpush
@endsection
