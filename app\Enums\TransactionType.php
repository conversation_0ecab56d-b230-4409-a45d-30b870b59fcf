<?php

namespace App\Enums;

enum TransactionType: string
{
    case REVENUE_COLLECTION = 'revenue_collection';
    case CONTRIBUTION = 'contribution';
    case TRANSFER = 'transfer';
    case REFUND = 'refund';

    public function getLabel(): string
    {
        return match ($this) {
            self::REVENUE_COLLECTION => 'Revenue Collection',
            self::CONTRIBUTION => 'Contribution',
            self::TRANSFER => 'Transfer',
            self::REFUND => 'Refund',
        };
    }

    public function getDescription(): string
    {
        return match ($this) {
            self::REVENUE_COLLECTION => 'Revenue sent from lower level to upper level church',
            self::CONTRIBUTION => 'Contribution payment for specific church initiative',
            self::TRANSFER => 'General transfer between church accounts',
            self::REFUND => 'Refund of previous transaction',
        };
    }
}
