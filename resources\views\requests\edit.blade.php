@extends('layouts.app')

@section('title', 'Edit Request')

@section('content')
    <div class="container mx-auto p-6">
        <h1 class="text-3xl font-bold mb-6">Edit Request</h1>
        <form method="POST" action="{{ route('requests.update', $request_model) }}" class="bg-white p-6 rounded-lg shadow-md">
            @csrf
            @method('PUT')
            <div class="mb-4">
                <label class="block text-sm font-medium">Church</label>
                <select name="church_id" class="border p-2 rounded w-full" required>
                    <option value="">Select Church</option>
                    @foreach ($churches as $church)
                        <option value="{{ $church->id }}" {{ $request_model->church_id == $church->id ? 'selected' : '' }}>
                            {{ $church->name }} ({{ $church->level }})
                        </option>
                    @endforeach
                </select>
                @error('church_id') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium">Request Type</label>
                <select name="type" class="border p-2 rounded w-full" required>
                    <option value="">Select Type</option>
                    @foreach ($request_types as $type)
                        <option value="{{ $type }}" {{ $request_model->type == $type ? 'selected' : '' }}>
                            {{ ucwords(str_replace('_', ' ', $type)) }}
                        </option>
                    @endforeach
                </select>
                @error('type') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium">Details (Optional)</label>
                <textarea name="details[description]" class="border p-2 rounded w-full">{{ $request_model->details['description'] ?? '' }}</textarea>
                @error('details') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
            </div>
            <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded">Update Request</button>
        </form>
    </div>
@endsection