@extends('layouts.app')

@section('title', __('Financial Dashboard') . ' - ' . __('common.fpct_system'))
@section('page-title', __('Financial Dashboard'))

@section('content')
<div class="space-y-6">
    <!-- Welcome Header -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-white">{{ __('common.welcome_back') }}, {{ Auth::user()?->full_name ?? 'User' }}!</h1>
                    <p class="mt-1 text-sm text-blue-100">
                        <i class="fas fa-church mr-2"></i>{{ $church->name }} • {{ ucfirst($church->level->value) }} {{ __('common.level') }}
                        @if(Auth::user() && count(Auth::user()->getRoleNames()) > 0)
                            • {{ Auth::user()->getRoleNames()[0] }}
                        @endif
                    </p>
                    @if($church->location || $church->district || $church->region)
                    <p class="mt-1 text-xs text-blue-200">
                        <i class="fas fa-map-marker-alt mr-1"></i>
                        {{ $church->location }}
                        @if($church->district || $church->region)
                            @if($church->location) • @endif
                            @if($church->district){{ $church->district }}@endif
                            @if($church->district && $church->region), @endif
                            @if($church->region){{ $church->region }}@endif
                        @endif
                    </p>
                    @endif
                </div>
                <div class="flex items-center space-x-4">
                    <!-- Period Selector -->
                    <div class="relative">
                        <select onchange="window.location.href='?period='+this.value" class="bg-white bg-opacity-20 text-white border border-white border-opacity-30 rounded-lg px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 appearance-none">
                            <option value="week" {{ $period === 'week' ? 'selected' : '' }} class="text-gray-900 bg-white">{{ __('This Week') }}</option>
                            <option value="month" {{ $period === 'month' ? 'selected' : '' }} class="text-gray-900 bg-white">{{ __('This Month') }}</option>
                            <option value="quarter" {{ $period === 'quarter' ? 'selected' : '' }} class="text-gray-900 bg-white">{{ __('This Quarter') }}</option>
                            <option value="year" {{ $period === 'year' ? 'selected' : '' }} class="text-gray-900 bg-white">{{ __('This Year') }}</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                            <i class="fas fa-chevron-down text-white text-xs"></i>
                        </div>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                            <i class="fas fa-coins text-2xl text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Balance Card -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                            <i class="fas fa-wallet text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-blue-100 uppercase tracking-wide">{{ __('Current Financial Balance') }}</h3>
                        <p class="text-3xl font-bold text-white">
                            {{ number_format((float) $financialBalance->available_balance, 2) }} <span class="text-lg">TZS</span>
                        </p>
                        <div class="flex items-center mt-2">
                            <span class="text-blue-100 text-sm mr-2">{{ __('Status') }}:</span>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white bg-opacity-20 text-white">
                                <span class="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                                {{ ucfirst($financialBalance->getBalanceStatus()) }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-3 gap-4">
                    <div class="text-center bg-white bg-opacity-10 rounded-lg p-3">
                        <div class="text-blue-100 text-xs uppercase tracking-wide mb-1">{{ __('Pending') }}</div>
                        <div class="text-white font-bold text-lg">{{ number_format((float) $financialBalance->pending_balance, 2) }}</div>
                    </div>
                    <div class="text-center bg-white bg-opacity-10 rounded-lg p-3">
                        <div class="text-blue-100 text-xs uppercase tracking-wide mb-1">{{ __('Reserved') }}</div>
                        <div class="text-white font-bold text-lg">{{ number_format((float) $financialBalance->reserved_balance, 2) }}</div>
                    </div>
                    <div class="text-center bg-white bg-opacity-10 rounded-lg p-3">
                        <div class="text-blue-100 text-xs uppercase tracking-wide mb-1">{{ __('Total') }}</div>
                        <div class="text-white font-bold text-lg">{{ number_format((float) $financialBalance->getTotalBalance(), 2) }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Incoming Revenue -->
        <div class="bg-gradient-to-r from-green-500 to-green-600 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-white bg-opacity-30 rounded-md flex items-center justify-center">
                            <i class="fas fa-arrow-down text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-green-100 truncate uppercase tracking-wide">{{ __('Incoming Revenue') }}</dt>
                            <dd class="text-lg font-medium text-white">{{ number_format((float) $statistics['incoming_revenue'], 2) }} TZS</dd>
                            @if(isset($statistics['growth_rates']['incoming']))
                                <dd class="text-sm text-green-100 flex items-center">
                                    <i class="fas fa-{{ $statistics['growth_rates']['incoming'] >= 0 ? 'arrow-up' : 'arrow-down' }} mr-1"></i>
                                    {{ $statistics['growth_rates']['incoming'] >= 0 ? '+' : '' }}{{ number_format($statistics['growth_rates']['incoming'], 1) }}%
                                </dd>
                            @endif
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Outgoing Revenue -->
        <div class="bg-gradient-to-r from-orange-500 to-red-500 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-white bg-opacity-30 rounded-md flex items-center justify-center">
                            <i class="fas fa-arrow-up text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-orange-100 truncate uppercase tracking-wide">{{ __('Outgoing Revenue') }}</dt>
                            <dd class="text-lg font-medium text-white">{{ number_format((float) $statistics['outgoing_revenue'], 2) }} TZS</dd>
                            @if(isset($statistics['growth_rates']['outgoing']))
                                <dd class="text-sm text-orange-100 flex items-center">
                                    <i class="fas fa-{{ $statistics['growth_rates']['outgoing'] >= 0 ? 'arrow-up' : 'arrow-down' }} mr-1"></i>
                                    {{ $statistics['growth_rates']['outgoing'] >= 0 ? '+' : '' }}{{ number_format($statistics['growth_rates']['outgoing'], 1) }}%
                                </dd>
                            @endif
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Net Revenue -->
        <div class="bg-gradient-to-r from-{{ $statistics['net_revenue'] >= 0 ? 'blue-500 to-blue-600' : 'red-500 to-red-600' }} overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-white bg-opacity-30 rounded-md flex items-center justify-center">
                            <i class="fas fa-{{ $statistics['net_revenue'] >= 0 ? 'chart-line' : 'chart-line-down' }} text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-{{ $statistics['net_revenue'] >= 0 ? 'blue' : 'red' }}-100 truncate uppercase tracking-wide">{{ __('Net Revenue') }}</dt>
                            <dd class="text-lg font-medium text-white">{{ number_format((float) $statistics['net_revenue'], 2) }} TZS</dd>
                            <dd class="text-sm text-{{ $statistics['net_revenue'] >= 0 ? 'blue' : 'red' }}-100">
                                {{ $statistics['net_revenue'] >= 0 ? 'Positive' : 'Negative' }} Flow
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Transactions -->
        <div class="bg-gradient-to-r from-purple-500 to-purple-600 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-white bg-opacity-30 rounded-md flex items-center justify-center">
                            <i class="fas fa-exchange-alt text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-purple-100 truncate uppercase tracking-wide">{{ __('Total Transactions') }}</dt>
                            <dd class="text-lg font-medium text-white">{{ number_format($statistics['total_transactions']) }}</dd>
                            <dd class="text-sm text-purple-100">{{ ucfirst($period) }} Period</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Pending Transactions -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Financial Chart -->
        <div class="lg:col-span-2">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 flex items-center">
                        <i class="fas fa-chart-area mr-2 text-blue-600"></i>
                        {{ __('Financial Flow Overview') }}
                    </h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">
                        {{ __('Revenue trends and patterns') }}
                    </p>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <div class="chart-area" style="height: 300px;">
                        <canvas id="financialChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Transactions -->
        <div class="lg:col-span-1">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg leading-6 font-medium text-gray-900 flex items-center">
                                <i class="fas fa-clock mr-2 text-yellow-600"></i>
                                {{ __('Pending Approvals') }}
                            </h3>
                            <p class="mt-1 text-sm text-gray-500">{{ __('Awaiting review') }}</p>
                        </div>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                            {{ count($pendingTransactions) }}
                        </span>
                    </div>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    @if(count($pendingTransactions) > 0)
                        <div class="space-y-4">
                            @foreach($pendingTransactions as $transaction)
                                <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-clock text-yellow-600 text-sm"></i>
                                        </div>
                                    </div>
                                    <div class="ml-3 flex-1">
                                        <div class="text-xs text-gray-500 mb-1">{{ $transaction->created_at->format('M j, Y') }}</div>
                                        <div class="font-medium text-gray-900">{{ number_format((float) $transaction->amount, 2) }} TZS</div>
                                        <div class="text-xs text-gray-500">{{ __('From') }}: {{ $transaction->fromChurch->name }}</div>
                                    </div>
                                    <div class="ml-3">
                                        <a href="{{ route('transactions.show', $transaction) }}" class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-yellow-700 bg-yellow-100 hover:bg-yellow-200">
                                            <i class="fas fa-eye mr-1"></i>{{ __('Review') }}
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-check-circle text-green-600 text-2xl"></i>
                            </div>
                            <h4 class="text-lg font-medium text-gray-900 mb-2">{{ __('All Clear!') }}</h4>
                            <p class="text-gray-500">{{ __('No pending transactions requiring approval') }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900">{{ __('Recent Transactions') }}</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">{{ __('Latest financial activities') }}</p>
                </div>
                <a href="{{ route('transactions.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-list mr-2"></i>{{ __('View All') }}
                </a>
            </div>
        </div>
        <div class="overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200"
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Date') }}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Transaction ID') }}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Type') }}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Amount') }}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('From/To') }}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Status') }}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Actions') }}</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($recentTransactions as $transaction)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ $transaction->created_at->format('M j, Y') }}</div>
                                    <div class="text-sm text-gray-500">{{ $transaction->created_at->format('g:i A') }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <code class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs">{{ $transaction->transaction_id }}</code>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ $transaction->type->getLabel() }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ number_format((float) $transaction->amount, 2) }} <span class="text-gray-500">TZS</span></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($transaction->from_church_id === $church->id)
                                        <div class="flex items-center">
                                            <i class="fas fa-arrow-up text-red-500 mr-2"></i>
                                            <div>
                                                <div class="text-sm font-medium text-red-600">{{ __('To') }}</div>
                                                <div class="text-sm text-gray-500">{{ $transaction->toChurch->name }}</div>
                                            </div>
                                        </div>
                                    @else
                                        <div class="flex items-center">
                                            <i class="fas fa-arrow-down text-green-500 mr-2"></i>
                                            <div>
                                                <div class="text-sm font-medium text-green-600">{{ __('From') }}</div>
                                                <div class="text-sm text-gray-500">{{ $transaction->fromChurch->name }}</div>
                                            </div>
                                        </div>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($transaction->status->value === 'completed') bg-green-100 text-green-800
                                        @elseif($transaction->status->value === 'pending') bg-yellow-100 text-yellow-800
                                        @elseif($transaction->status->value === 'failed') bg-red-100 text-red-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        {{ $transaction->status->getLabel() }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="{{ route('transactions.show', $transaction) }}" class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200">
                                        <i class="fas fa-eye mr-1"></i>{{ __('View') }}
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900 flex items-center">
                <i class="fas fa-bolt mr-2 text-blue-600"></i>
                {{ __('Quick Actions') }}
            </h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                {{ __('Frequently used financial operations') }}
            </p>
        </div>
        <div class="px-4 py-5 sm:p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="{{ route('transactions.create') }}" class="group relative bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-lg text-white hover:from-green-600 hover:to-green-700 transition-all duration-200 transform hover:scale-105">
                    <div class="flex flex-col items-center text-center">
                        <i class="fas fa-paper-plane text-3xl mb-3"></i>
                        <h4 class="font-bold text-lg">{{ __('Send Revenue') }}</h4>
                        <p class="text-green-100 text-sm mt-1">{{ __('Transfer funds upward') }}</p>
                    </div>
                </a>

                @can('create-contributions')
                    <a href="{{ route('contributions.create') }}" class="group relative bg-gradient-to-r from-blue-500 to-blue-600 p-6 rounded-lg text-white hover:from-blue-600 hover:to-blue-700 transition-all duration-200 transform hover:scale-105">
                        <div class="flex flex-col items-center text-center">
                            <i class="fas fa-hand-holding-heart text-3xl mb-3"></i>
                            <h4 class="font-bold text-lg">{{ __('Create Contribution') }}</h4>
                            <p class="text-blue-100 text-sm mt-1">{{ __('Start fundraising campaign') }}</p>
                        </div>
                    </a>
                @endcan

                <a href="{{ route('financial-reports.create') }}" class="group relative bg-gradient-to-r from-purple-500 to-purple-600 p-6 rounded-lg text-white hover:from-purple-600 hover:to-purple-700 transition-all duration-200 transform hover:scale-105">
                    <div class="flex flex-col items-center text-center">
                        <i class="fas fa-chart-line text-3xl mb-3"></i>
                        <h4 class="font-bold text-lg">{{ __('Generate Report') }}</h4>
                        <p class="text-purple-100 text-sm mt-1">{{ __('Financial analytics') }}</p>
                    </div>
                </a>

                <a href="{{ route('receipts.create') }}" class="group relative bg-gradient-to-r from-gray-500 to-gray-600 p-6 rounded-lg text-white hover:from-gray-600 hover:to-gray-700 transition-all duration-200 transform hover:scale-105">
                    <div class="flex flex-col items-center text-center">
                        <i class="fas fa-file-invoice text-3xl mb-3"></i>
                        <h4 class="font-bold text-lg">{{ __('Generate Receipt') }}</h4>
                        <p class="text-gray-100 text-sm mt-1">{{ __('Transaction receipts') }}</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Financial Chart
const ctx = document.getElementById('financialChart').getContext('2d');
const chartData = @json($chartData);

new Chart(ctx, {
    type: 'line',
    data: {
        labels: chartData.map(item => item.date),
        datasets: [{
            label: 'Incoming',
            data: chartData.map(item => item.incoming),
            borderColor: 'rgb(28, 200, 138)',
            backgroundColor: 'rgba(28, 200, 138, 0.1)',
            tension: 0.1
        }, {
            label: 'Outgoing',
            data: chartData.map(item => item.outgoing),
            borderColor: 'rgb(246, 194, 62)',
            backgroundColor: 'rgba(246, 194, 62, 0.1)',
            tension: 0.1
        }, {
            label: 'Net',
            data: chartData.map(item => item.net),
            borderColor: 'rgb(54, 185, 204)',
            backgroundColor: 'rgba(54, 185, 204, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' TZS';
                    }
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.dataset.label + ': ' + context.parsed.y.toLocaleString() + ' TZS';
                    }
                }
            }
        }
    }
});
</script>
@endpush
@endsection
