<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class SessionTimeout
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip timeout check for login/logout routes and AJAX session check
        if ($this->shouldSkipTimeoutCheck($request)) {
            return $next($request);
        }

        // Check if user is authenticated
        if (!Auth::check()) {
            return $this->handleUnauthenticated($request);
        }

        $sessionTimeout = config('session.timeout', 5); // 5 minutes default
        $lastActivity = Session::get('last_activity');
        $currentTime = time();

        // If no last activity recorded, set it now
        if (!$lastActivity) {
            Session::put('last_activity', $currentTime);
            return $next($request);
        }

        // Check if session has timed out
        $timeDifference = $currentTime - $lastActivity;
        if ($timeDifference > ($sessionTimeout * 60)) {
            return $this->handleSessionTimeout($request);
        }

        // Update last activity time for non-AJAX requests or important AJAX requests
        if (!$request->ajax() || $this->shouldUpdateActivity($request)) {
            Session::put('last_activity', $currentTime);
        }

        return $next($request);
    }

    /**
     * Check if timeout check should be skipped for this request
     */
    private function shouldSkipTimeoutCheck(Request $request): bool
    {
        $skipRoutes = [
            'login',
            'logout',
            'session.check',
            'session.extend',
            'language.switch',
            'language.current',
            'language.list'
        ];

        $routeName = $request->route()?->getName();
        
        return in_array($routeName, $skipRoutes) || 
               str_starts_with($request->path(), 'login') ||
               str_starts_with($request->path(), 'logout');
    }

    /**
     * Check if this request should update the last activity time
     */
    private function shouldUpdateActivity(Request $request): bool
    {
        // Update activity for important AJAX requests
        $updateRoutes = [
            'dashboard.index',
            'users.index',
            'churches.index',
            'requests.index',
            'messages.index'
        ];

        $routeName = $request->route()?->getName();
        return in_array($routeName, $updateRoutes);
    }

    /**
     * Handle unauthenticated user
     */
    private function handleUnauthenticated(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Unauthenticated',
                'redirect' => route('login')
            ], 401);
        }

        // Store the intended URL so user can be redirected back after login
        if ($request->method() === 'GET' && !$request->expectsJson()) {
            session(['url.intended' => $request->fullUrl()]);
        }

        return redirect()->route('login')->with('error', 'Please log in to access this page.');
    }

    /**
     * Handle session timeout
     */
    private function handleSessionTimeout(Request $request): Response
    {
        // Store the intended URL before logging out (for GET requests only)
        if ($request->method() === 'GET' && !$request->expectsJson()) {
            session(['url.intended' => $request->fullUrl()]);
        }

        // Log the user out
        Auth::logout();
        Session::invalidate();
        Session::regenerateToken();

        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Session expired',
                'message' => 'Your session has expired due to inactivity. Please log in again.',
                'redirect' => route('login'),
                'timeout' => true
            ], 401);
        }

        return redirect()->route('login')
            ->with('error', 'Your session has expired due to inactivity. Please log in again.')
            ->with('session_expired', true);
    }
}
