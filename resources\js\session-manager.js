/**
 * Session Management System
 * Handles automatic logout, session warnings, and prevents back button access after logout
 */

class SessionManager {
    constructor() {
        this.config = {
            timeout: 5 * 60, // 5 minutes in seconds
            warningTime: 60, // warn when 1 minute left
            checkInterval: 30, // check every 30 seconds
        };
        
        this.timers = {
            checkTimer: null,
            warningTimer: null,
            logoutTimer: null
        };
        
        this.isWarningShown = false;
        this.isSessionExpired = false;
        this.lastActivity = Date.now();
        
        this.init();
    }

    async init() {
        try {
            // Get session configuration from server
            const response = await fetch('/session/config');
            if (response.ok) {
                const serverConfig = await response.json();
                this.config = { ...this.config, ...serverConfig };
            }
        } catch (error) {
            console.warn('Could not load session config, using defaults');
        }

        this.setupEventListeners();
        this.startSessionMonitoring();
        this.preventBackButtonAccess();
    }

    setupEventListeners() {
        // Track user activity
        const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        
        activityEvents.forEach(event => {
            document.addEventListener(event, () => {
                this.updateActivity();
            }, { passive: true });
        });

        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.checkSession();
            }
        });

        // Handle beforeunload to clear timers
        window.addEventListener('beforeunload', () => {
            this.clearAllTimers();
        });
    }

    updateActivity() {
        this.lastActivity = Date.now();
        
        // Hide warning if user becomes active
        if (this.isWarningShown) {
            this.hideSessionWarning();
        }
    }

    startSessionMonitoring() {
        // Check session status periodically
        this.timers.checkTimer = setInterval(() => {
            this.checkSession();
        }, this.config.checkInterval * 1000);

        // Initial check
        this.checkSession();
    }

    async checkSession() {
        try {
            const response = await fetch('/session/check', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });

            const data = await response.json();

            if (!response.ok || !data.authenticated) {
                this.handleSessionExpired(data.message || 'Session expired');
                return;
            }

            // Handle session warning
            if (data.timeout_warning && !this.isWarningShown) {
                this.showSessionWarning(data.remaining_time);
            } else if (!data.timeout_warning && this.isWarningShown) {
                this.hideSessionWarning();
            }

        } catch (error) {
            console.error('Session check failed:', error);
            // Don't logout on network errors, just log
        }
    }

    showSessionWarning(remainingTime) {
        this.isWarningShown = true;
        
        // Create warning modal
        const modal = this.createWarningModal(remainingTime);
        document.body.appendChild(modal);

        // Auto-hide after remaining time
        this.timers.warningTimer = setTimeout(() => {
            this.handleSessionExpired('Session expired due to inactivity');
        }, remainingTime * 1000);
    }

    hideSessionWarning() {
        this.isWarningShown = false;
        
        const modal = document.getElementById('session-warning-modal');
        if (modal) {
            modal.remove();
        }

        if (this.timers.warningTimer) {
            clearTimeout(this.timers.warningTimer);
            this.timers.warningTimer = null;
        }
    }

    createWarningModal(remainingTime) {
        const modal = document.createElement('div');
        modal.id = 'session-warning-modal';
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        
        const minutes = Math.floor(remainingTime / 60);
        const seconds = remainingTime % 60;
        const timeText = minutes > 0 ? `${minutes}:${seconds.toString().padStart(2, '0')}` : `${seconds} seconds`;

        modal.innerHTML = `
            <div class="bg-white rounded-lg shadow-xl p-6 max-w-md mx-4">
                <div class="flex items-center mb-4">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-500 text-2xl"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-gray-900">Session Expiring Soon</h3>
                    </div>
                </div>
                
                <div class="mb-4">
                    <p class="text-sm text-gray-600">
                        Your session will expire in <span class="font-semibold text-red-600">${timeText}</span> due to inactivity.
                    </p>
                    <p class="text-sm text-gray-600 mt-2">
                        Click "Stay Logged In" to extend your session, or you will be automatically logged out.
                    </p>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button id="logout-now-btn" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        Logout Now
                    </button>
                    <button id="extend-session-btn" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        Stay Logged In
                    </button>
                </div>
            </div>
        `;

        // Add event listeners
        modal.querySelector('#extend-session-btn').addEventListener('click', () => {
            this.extendSession();
        });

        modal.querySelector('#logout-now-btn').addEventListener('click', () => {
            this.logout();
        });

        return modal;
    }

    async extendSession() {
        try {
            const response = await fetch('/session/extend', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });

            if (response.ok) {
                this.hideSessionWarning();
                this.updateActivity();
                
                // Show success message
                this.showNotification('Session extended successfully', 'success');
            } else {
                throw new Error('Failed to extend session');
            }
        } catch (error) {
            console.error('Failed to extend session:', error);
            this.handleSessionExpired('Failed to extend session');
        }
    }

    handleSessionExpired(message = 'Session expired') {
        this.isSessionExpired = true;
        this.clearAllTimers();
        
        // Clear any existing modals
        this.hideSessionWarning();
        
        // Show expiration message
        this.showNotification(message, 'error');
        
        // Redirect to login after a short delay
        setTimeout(() => {
            this.logout();
        }, 2000);
    }

    logout() {
        // Clear session data
        this.clearAllTimers();
        
        // Create and submit logout form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/logout';
        
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = '_token';
            csrfInput.value = csrfToken;
            form.appendChild(csrfInput);
        }
        
        document.body.appendChild(form);
        form.submit();
    }

    preventBackButtonAccess() {
        // Add state to history to detect back button usage
        window.history.pushState(null, null, window.location.href);
        
        window.addEventListener('popstate', (event) => {
            // Check if user is logged out (no auth token or session expired)
            if (this.isSessionExpired || !this.isAuthenticated()) {
                // Prevent going back
                window.history.pushState(null, null, window.location.href);
                
                // Show message and redirect to login
                this.showNotification('Please log in to access this page', 'warning');
                setTimeout(() => {
                    window.location.href = '/login';
                }, 1500);
            }
        });
    }

    isAuthenticated() {
        // Check if there's a valid session
        return document.querySelector('meta[name="csrf-token"]') !== null;
    }

    showNotification(message, type = 'info') {
        // Remove existing notifications
        const existing = document.querySelector('.session-notification');
        if (existing) {
            existing.remove();
        }

        const notification = document.createElement('div');
        notification.className = `session-notification fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${this.getNotificationClasses(type)}`;
        
        notification.innerHTML = `
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas ${this.getNotificationIcon(type)}"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium">${message}</p>
                </div>
                <div class="ml-auto pl-3">
                    <button class="text-current opacity-70 hover:opacity-100" onclick="this.parentElement.parentElement.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    getNotificationClasses(type) {
        const classes = {
            success: 'bg-green-100 border border-green-400 text-green-700',
            error: 'bg-red-100 border border-red-400 text-red-700',
            warning: 'bg-yellow-100 border border-yellow-400 text-yellow-700',
            info: 'bg-blue-100 border border-blue-400 text-blue-700'
        };
        return classes[type] || classes.info;
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    clearAllTimers() {
        Object.values(this.timers).forEach(timer => {
            if (timer) {
                clearInterval(timer);
                clearTimeout(timer);
            }
        });
        
        this.timers = {
            checkTimer: null,
            warningTimer: null,
            logoutTimer: null
        };
    }

    destroy() {
        this.clearAllTimers();
        this.hideSessionWarning();
    }
}

// Initialize session manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize on authenticated pages
    if (document.querySelector('meta[name="csrf-token"]')) {
        window.sessionManager = new SessionManager();
    }
});

// Export for use in other modules
export default SessionManager;
