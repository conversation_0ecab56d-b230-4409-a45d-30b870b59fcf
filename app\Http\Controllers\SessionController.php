<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class SessionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Check session status
     */
    public function check(Request $request)
    {
        if (!Auth::check()) {
            return response()->json([
                'authenticated' => false,
                'redirect' => route('login')
            ], 401);
        }

        $sessionTimeout = config('session.timeout', 5); // 5 minutes
        $lastActivity = Session::get('last_activity');
        $currentTime = time();

        if (!$lastActivity) {
            Session::put('last_activity', $currentTime);
            return response()->json([
                'authenticated' => true,
                'remaining_time' => $sessionTimeout * 60,
                'timeout_warning' => false
            ]);
        }

        $timeDifference = $currentTime - $lastActivity;
        $remainingTime = ($sessionTimeout * 60) - $timeDifference;

        // If session has expired
        if ($remainingTime <= 0) {
            Auth::logout();
            Session::invalidate();
            Session::regenerateToken();

            return response()->json([
                'authenticated' => false,
                'expired' => true,
                'message' => 'Your session has expired due to inactivity.',
                'redirect' => route('login')
            ], 401);
        }

        // Warning if less than 1 minute remaining
        $timeoutWarning = $remainingTime <= 60;

        return response()->json([
            'authenticated' => true,
            'remaining_time' => $remainingTime,
            'timeout_warning' => $timeoutWarning,
            'warning_message' => $timeoutWarning ? 'Your session will expire in less than 1 minute.' : null
        ]);
    }

    /**
     * Extend session
     */
    public function extend(Request $request)
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Not authenticated',
                'redirect' => route('login')
            ], 401);
        }

        // Update last activity time
        Session::put('last_activity', time());

        $sessionTimeout = config('session.timeout', 5);

        return response()->json([
            'success' => true,
            'message' => 'Session extended successfully',
            'remaining_time' => $sessionTimeout * 60
        ]);
    }

    /**
     * Get session configuration
     */
    public function config()
    {
        return response()->json([
            'timeout' => config('session.timeout', 5) * 60, // in seconds
            'warning_time' => 60, // warn when 1 minute left
            'check_interval' => 30 // check every 30 seconds
        ]);
    }
}
