@extends('layouts.app')

@section('title', 'Role Details - ' . $role->name)

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ $role->name }}</h1>
                    <p class="mt-2 text-sm text-gray-600">Role details and assigned users</p>
                </div>
                <div class="flex items-center space-x-3">
                    @can('manage-permissions')
                    <a href="{{ route('roles.edit', $role) }}" 
                       class="inline-flex items-center px-4 py-2 border border-blue-300 rounded-lg text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Role
                    </a>
                    @endcan
                    <a href="{{ route('roles.index') }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Roles
                    </a>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Role Information -->
            <div class="lg:col-span-1">
                <div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-user-shield mr-3 text-blue-600"></i>
                            Role Information
                        </h3>
                    </div>
                    
                    <div class="p-6 space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Role Name</label>
                            <p class="mt-1 text-lg font-semibold text-gray-900">{{ $role->name }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Total Permissions</label>
                            <p class="mt-1 text-lg font-semibold text-gray-900">{{ $role->permissions->count() }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Assigned Users</label>
                            <p class="mt-1 text-lg font-semibold text-gray-900">{{ $role->users->count() }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Created</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $role->created_at->format('M d, Y \a\t g:i A') }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Last Updated</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $role->updated_at->format('M d, Y \a\t g:i A') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                @can('manage-permissions')
                <div class="mt-6 bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-bolt mr-3 text-yellow-600"></i>
                            Quick Actions
                        </h3>
                    </div>
                    
                    <div class="p-6 space-y-3">
                        <a href="{{ route('roles.edit', $role) }}" 
                           class="w-full inline-flex items-center justify-center px-4 py-2 border border-blue-300 rounded-lg text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Role
                        </a>
                        
                        @if($role->users->count() == 0)
                        <form action="{{ route('roles.destroy', $role) }}" 
                              method="POST" 
                              onsubmit="return confirm('Are you sure you want to delete this role?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" 
                                    class="w-full inline-flex items-center justify-center px-4 py-2 border border-red-300 rounded-lg text-sm font-medium text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                                <i class="fas fa-trash mr-2"></i>
                                Delete Role
                            </button>
                        </form>
                        @else
                        <div class="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <p class="text-sm text-yellow-800">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                Cannot delete role with assigned users
                            </p>
                        </div>
                        @endif
                    </div>
                </div>
                @endcan
            </div>

            <!-- Permissions and Users -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Permissions -->
                <div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-key mr-3 text-green-600"></i>
                            Permissions ({{ $role->permissions->count() }})
                        </h3>
                    </div>
                    
                    <div class="p-6">
                        @if($role->permissions->count() > 0)
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                @foreach($role->permissions as $permission)
                                    <div class="flex items-center p-3 border border-gray-200 rounded-lg bg-gray-50">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-check-circle text-green-500"></i>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900">{{ $permission->name }}</p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-8">
                                <i class="fas fa-key text-4xl text-gray-400 mb-4"></i>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No permissions assigned</h3>
                                <p class="text-gray-600 mb-4">This role doesn't have any permissions yet.</p>
                                @can('manage-permissions')
                                <a href="{{ route('roles.edit', $role) }}" 
                                   class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-plus mr-2"></i>
                                    Add Permissions
                                </a>
                                @endcan
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Assigned Users -->
                <div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-users mr-3 text-purple-600"></i>
                            Assigned Users ({{ $role->users->count() }})
                        </h3>
                    </div>
                    
                    <div class="p-6">
                        @if($role->users->count() > 0)
                            <div class="space-y-4">
                                @foreach($role->users as $user)
                                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                                    <span class="text-sm font-medium text-blue-600">
                                                        {{ substr($user->full_name, 0, 1) }}
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <p class="text-sm font-medium text-gray-900">{{ $user->full_name }}</p>
                                                <p class="text-sm text-gray-600">{{ $user->email }}</p>
                                                @if($user->church)
                                                    <p class="text-xs text-gray-500">{{ $user->church->name }}</p>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $user->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                {{ $user->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                            @can('manage-users')
                                            <a href="{{ route('users.show', $user) }}" 
                                               class="text-blue-600 hover:text-blue-800 transition-colors duration-200"
                                               title="View User">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @endcan
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-8">
                                <i class="fas fa-users text-4xl text-gray-400 mb-4"></i>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No users assigned</h3>
                                <p class="text-gray-600 mb-4">No users have been assigned to this role yet.</p>
                                @can('manage-users')
                                <a href="{{ route('users.index') }}" 
                                   class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                                    <i class="fas fa-user-plus mr-2"></i>
                                    Assign Users
                                </a>
                                @endcan
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
