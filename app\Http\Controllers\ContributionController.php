<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Contribution;
use App\Models\Church;
use App\Models\Transaction;
use App\Services\ChurchHierarchyService;
use App\Enums\ContributionStatus;
use App\Enums\ChurchLevel;

class ContributionController extends Controller
{
    protected ChurchHierarchyService $hierarchyService;

    public function __construct(ChurchHierarchyService $hierarchyService)
    {
        $this->hierarchyService = $hierarchyService;
        $this->middleware('auth');
        $this->middleware('permission:view-contributions', ['only' => ['index', 'show']]);
        $this->middleware('permission:create-contributions', ['only' => ['create', 'store']]);
        $this->middleware('permission:edit-contributions', ['only' => ['edit', 'update']]);
        $this->middleware('permission:manage-contributions', ['only' => ['destroy', 'updateStatus']]);
    }

    /**
     * Display a listing of contributions
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $church = $user->church;

        $query = Contribution::query();

        // Show contributions created by this church or targeting this church level
        if ($user->can('create-contributions')) {
            $query->where('created_by_church_id', $church->id);
        } else {
            $query->where('collection_scope', $church->level->value);
        }

        $query->with(['createdByChurch', 'createdByUser']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $contributions = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('financial.contributions.index', compact('contributions', 'church'));
    }

    /**
     * Show the form for creating a new contribution
     */
    public function create()
    {
        $user = Auth::user();
        $church = $user->church;

        if (!$church->canCreateContribution()) {
            return redirect()->back()->with('error', 'Your church level cannot create contributions.');
        }

        $targetLevels = $church->getContributionTargetLevels();
        $targetChurches = $this->getTargetChurches($church);

        return view('financial.contributions.create', compact('church', 'targetLevels', 'targetChurches'));
    }

    /**
     * Store a newly created contribution
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'target_amount' => 'nullable|numeric|min:1',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'nullable|date|after:start_date',
            'type' => 'required|in:general,special,emergency,project',
            'collection_scope' => 'required|string',
            'target_churches' => 'nullable|array',
            'target_churches.*' => 'exists:churches,id',
            'is_mandatory' => 'boolean',
            'instructions' => 'nullable|string',
        ]);

        $user = Auth::user();
        $church = $user->church;

        if (!$church->canCreateContribution()) {
            return redirect()->back()->with('error', 'Your church level cannot create contributions.');
        }

        $contribution = Contribution::create([
            'name' => $request->name,
            'description' => $request->description,
            'created_by_church_id' => $church->id,
            'created_by_user_id' => $user->id,
            'target_amount' => $request->target_amount,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'type' => $request->type,
            'collection_scope' => $request->collection_scope,
            'target_churches' => $request->target_churches,
            'is_mandatory' => $request->boolean('is_mandatory'),
            'instructions' => $request->instructions,
            'status' => ContributionStatus::ACTIVE,
        ]);

        return redirect()->route('contributions.show', $contribution)
            ->with('success', 'Contribution created successfully.');
    }

    /**
     * Display the specified contribution
     */
    public function show(Contribution $contribution)
    {
        $user = Auth::user();
        
        if (!$this->canViewContribution($user, $contribution)) {
            abort(403, 'Unauthorized to view this contribution.');
        }

        $contribution->load(['createdByChurch', 'createdByUser']);

        // Get contribution transactions
        $transactions = Transaction::where('contribution_id', $contribution->id)
            ->with(['fromChurch', 'toChurch', 'initiatedByUser'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Get contribution statistics
        $statistics = $this->getContributionStatistics($contribution);

        return view('financial.contributions.show', compact('contribution', 'transactions', 'statistics'));
    }

    /**
     * Show the form for editing the specified contribution
     */
    public function edit(Contribution $contribution)
    {
        $user = Auth::user();

        if ($contribution->created_by_church_id !== $user->church_id) {
            abort(403, 'Unauthorized to edit this contribution.');
        }

        $church = $user->church;
        $targetLevels = $church->getContributionTargetLevels();
        $targetChurches = $this->getTargetChurches($church);

        return view('financial.contributions.edit', compact('contribution', 'church', 'targetLevels', 'targetChurches'));
    }

    /**
     * Update the specified contribution
     */
    public function update(Request $request, Contribution $contribution)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'target_amount' => 'nullable|numeric|min:1',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after:start_date',
            'type' => 'required|in:general,special,emergency,project',
            'collection_scope' => 'required|string',
            'target_churches' => 'nullable|array',
            'target_churches.*' => 'exists:churches,id',
            'is_mandatory' => 'boolean',
            'instructions' => 'nullable|string',
        ]);

        $user = Auth::user();

        if ($contribution->created_by_church_id !== $user->church_id) {
            abort(403, 'Unauthorized to edit this contribution.');
        }

        $contribution->update([
            'name' => $request->name,
            'description' => $request->description,
            'target_amount' => $request->target_amount,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'type' => $request->type,
            'collection_scope' => $request->collection_scope,
            'target_churches' => $request->target_churches,
            'is_mandatory' => $request->boolean('is_mandatory'),
            'instructions' => $request->instructions,
        ]);

        return redirect()->route('contributions.show', $contribution)
            ->with('success', 'Contribution updated successfully.');
    }

    /**
     * Update contribution status
     */
    public function updateStatus(Request $request, Contribution $contribution)
    {
        $request->validate([
            'status' => 'required|in:active,completed,cancelled,expired',
        ]);

        $user = Auth::user();

        if ($contribution->created_by_church_id !== $user->church_id) {
            abort(403, 'Unauthorized to update this contribution status.');
        }

        $contribution->update(['status' => ContributionStatus::from($request->status)]);

        return redirect()->back()->with('success', 'Contribution status updated successfully.');
    }

    /**
     * Remove the specified contribution
     */
    public function destroy(Contribution $contribution)
    {
        $user = Auth::user();

        if ($contribution->created_by_church_id !== $user->church_id) {
            abort(403, 'Unauthorized to delete this contribution.');
        }

        // Check if contribution has transactions
        if ($contribution->transactions()->exists()) {
            return redirect()->back()->with('error', 'Cannot delete contribution with existing transactions.');
        }

        $contribution->delete();

        return redirect()->route('contributions.index')
            ->with('success', 'Contribution deleted successfully.');
    }

    /**
     * Get target churches for contribution creation
     */
    protected function getTargetChurches(Church $church): array
    {
        $targetLevels = $church->getContributionTargetLevels();
        $churches = [];

        foreach ($targetLevels as $level) {
            $levelChurches = Church::where('level', $level)->get(['id', 'name', 'level', 'location']);
            $churches[$level->value] = $levelChurches;
        }

        return $churches;
    }

    /**
     * Check if user can view a contribution
     */
    protected function canViewContribution($user, Contribution $contribution): bool
    {
        // Can view if created by user's church
        if ($contribution->created_by_church_id === $user->church_id) {
            return true;
        }

        // Can view if contribution targets user's church level
        if ($contribution->collection_scope === $user->church->level->value) {
            return true;
        }

        // Can view if user can manage the creating church
        return $this->hierarchyService->getChurchesUserCanManage($user)
            ->contains('id', $contribution->created_by_church_id);
    }

    /**
     * Get contribution statistics
     */
    protected function getContributionStatistics(Contribution $contribution): array
    {
        $totalTransactions = $contribution->transactions()->count();
        $completedTransactions = $contribution->transactions()->completed()->count();
        $pendingTransactions = $contribution->transactions()->pending()->count();
        
        $participatingChurches = $contribution->transactions()
            ->distinct('from_church_id')
            ->count('from_church_id');

        $targetChurches = count($contribution->getTargetChurches());

        return [
            'total_transactions' => $totalTransactions,
            'completed_transactions' => $completedTransactions,
            'pending_transactions' => $pendingTransactions,
            'participating_churches' => $participatingChurches,
            'target_churches' => $targetChurches,
            'participation_rate' => $targetChurches > 0 ? ($participatingChurches / $targetChurches) * 100 : 0,
            'progress_percentage' => $contribution->getProgressPercentage(),
            'remaining_amount' => $contribution->getRemainingAmount(),
        ];
    }
}
