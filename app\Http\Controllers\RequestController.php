<?php

namespace App\Http\Controllers;

use App\Models\Request as RequestModel;
use App\Models\Church;
use App\Models\ChurchLeader;
use App\Enums\ChurchLevel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class RequestController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('permission:view-requests', ['only' => ['index', 'show']]);
        $this->middleware('permission:create-requests', ['only' => ['create', 'store']]);
        $this->middleware('permission:manage-requests', ['only' => ['edit', 'update', 'destroy']]);
        $this->middleware('permission:approve-requests', ['only' => ['approve', 'reject']]);
    }

    // List all requests
    public function index(Request $request)
    {
        $user = Auth::user();
        $query = RequestModel::with('church', 'user', 'approver');

        // Filter requests based on user role and church hierarchy
        if (!$user->hasRole('Super Admin')) {
            $userChurch = $user->church;

            if ($userChurch) {
                // Users can see requests from churches they can approve for
                if ($user->hasPermissionTo('approve-requests')) {
                    // Get all churches this user can approve requests for
                    $approvableChurchIds = Church::all()->filter(function ($church) use ($userChurch) {
                        return $userChurch->canApproveRequestsFor($church);
                    })->pluck('id');

                    if ($approvableChurchIds->isNotEmpty()) {
                        $query->whereIn('church_id', $approvableChurchIds);
                    } else {
                        // If no approvable churches, show own requests only
                        $query->where('user_id', $user->id);
                    }
                } else {
                    // Regular users see only their own requests
                    $query->where('user_id', $user->id);
                }
            } else {
                // Users without church assignment see only their own requests
                $query->where('user_id', $user->id);
            }
        }

        $requests = $query->when($request->search, function ($query, $search) {
                return $query->whereHas('church', function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%");
                })->orWhereHas('user', function ($q) use ($search) {
                    $q->where('full_name', 'like', "%{$search}%");
                })->orWhere('type', 'like', "%{$search}%");
            })
            ->when($request->status, function ($query, $status) {
                return $query->where('status', $status);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('requests.index', compact('requests'));
    }

    // Show create request form
    public function create()
    {
        $user = Auth::user();

        // Check if user has permission to create requests
        if (!$user->hasPermissionTo('create-requests')) {
            return redirect()->route('requests.index')
                ->with('error', 'You do not have permission to create requests. Please contact your administrator.');
        }

        // Get churches that user can create requests for based on hierarchy
        $userChurch = $user->church;
        if (!$userChurch) {
            return redirect()->route('requests.index')
                ->with('error', 'You must be assigned to a church to create requests.');
        }

        // Filter churches based on user's authority
        $churches = $userChurch->getChurchesForRequests();

        $request_types = [
            'upgrade_branch_to_parish',
            'upgrade_parish_to_local',
            'upgrade_local_to_regional',
            'other',
        ];

        return view('requests.create', compact('churches', 'request_types'));
    }

    // Store new request
    public function store(Request $request)
    {
        $user = Auth::user();

        // Check if user has permission to create requests
        if (!$user->hasPermissionTo('create-requests')) {
            return redirect()->route('requests.index')
                ->with('error', 'You do not have permission to create requests. Please contact your administrator.');
        }

        $request->validate([
            'church_id' => 'required|exists:churches,id',
            'type' => 'required|in:upgrade_branch_to_parish,upgrade_parish_to_local,upgrade_local_to_regional,other',
            'details' => 'nullable|array',
        ]);

        $targetChurch = Church::find($request->church_id);
        $userChurch = $user->church;

        // Hierarchical validation: User can only create requests for their level or lower
        if (!$userChurch || !$userChurch->level->canCreateRequestsFor($targetChurch->level)) {
            return back()->withErrors([
                'church_id' => 'You can only create requests for churches at your level or lower in the hierarchy.'
            ]);
        }

        // Additional validation: User must have authority over the target church
        if (!$this->canUserCreateRequestForChurch($user, $targetChurch)) {
            return back()->withErrors([
                'church_id' => 'You do not have authority to create requests for this church.'
            ]);
        }

        // Validate church level for specific request types
        if ($request->type == 'upgrade_branch_to_parish' && $targetChurch->level->value != 'Branch') {
            return back()->withErrors(['type' => 'Only Branch churches can be upgraded to Parish.']);
        }
        if ($request->type == 'upgrade_parish_to_local' && $targetChurch->level->value != 'Parish') {
            return back()->withErrors(['type' => 'Only Parish churches can be upgraded to Local.']);
        }
        if ($request->type == 'upgrade_local_to_regional' && $targetChurch->level->value != 'Local') {
            return back()->withErrors(['type' => 'Only Local churches can be upgraded to Regional.']);
        }

        RequestModel::create([
            'church_id' => $request->church_id,
            'user_id' => $user->id,
            'type' => $request->type,
            'status' => 'pending',
            'details' => $request->details,
        ]);

        return redirect()->route('requests.index')->with('success', 'Request created successfully.');
    }

    // Show request details
    public function show(RequestModel $request)
    {
        $request->load('church', 'user', 'approver');
        $this->authorizeRequestAccess($request);
        return view('requests.show', ['request_model' => $request]);
    }

    // Show edit request form
    public function edit(RequestModel $request)
    {
        $this->authorizeRequestAccess($request);
        if ($request->status != 'pending') {
            return redirect()->route('requests.index')->with('error', 'Only pending requests can be edited.');
        }

        $churches = Church::all();
        $request_types = [
            'upgrade_branch_to_parish',
            'upgrade_parish_to_local',
            'upgrade_local_to_regional',
            'other',
        ];

        return view('requests.edit', ['request_model' => $request, 'churches' => $churches, 'request_types' => $request_types]);
    }

    // Update request
    public function update(Request $httpRequest, RequestModel $request)
    {
        $this->authorizeRequestAccess($request);
        if ($request->status != 'pending') {
            return redirect()->route('requests.index')->with('error', 'Only pending requests can be edited.');
        }

        $httpRequest->validate([
            'church_id' => 'required|exists:churches,id',
            'type' => 'required|in:upgrade_branch_to_parish,upgrade_parish_to_local,upgrade_local_to_regional,other',
            'details' => 'nullable|array',
        ]);

        // Validate church level for specific request types
        $church = Church::find($httpRequest->church_id);
        if ($httpRequest->type == 'upgrade_branch_to_parish' && $church->level->value != 'Branch') {
            return back()->withErrors(['type' => 'Only Branch churches can be upgraded to Parish.']);
        }
        if ($httpRequest->type == 'upgrade_parish_to_local' && $church->level->value != 'Parish') {
            return back()->withErrors(['type' => 'Only Parish churches can be upgraded to Local.']);
        }
        if ($httpRequest->type == 'upgrade_local_to_regional' && $church->level->value != 'Local') {
            return back()->withErrors(['type' => 'Only Local churches can be upgraded to Regional.']);
        }

        $request->update([
            'church_id' => $httpRequest->church_id,
            'type' => $httpRequest->type,
            'details' => $httpRequest->details,
        ]);

        return redirect()->route('requests.index')->with('success', 'Request updated successfully.');
    }

    // Delete request
    public function destroy(RequestModel $request)
    {
        $this->authorizeRequestAccess($request);
        $request->delete();
        return redirect()->route('requests.index')->with('success', 'Request deleted successfully.');
    }

    // Approve request
    public function approve(RequestModel $request)
    {
        $user = Auth::user();
        $userChurch = $user->church;
        $requestChurch = $request->church;

        // Check basic permission
        if (!$user->hasPermissionTo('approve-requests')) {
            return redirect()->route('requests.index')
                ->with('error', 'You do not have permission to approve requests.');
        }

        // Hierarchical validation: User must be from higher level than the requesting church
        if (!$userChurch || !$userChurch->level->canApproveRequestsFrom($requestChurch->level)) {
            return redirect()->route('requests.index')
                ->with('error', 'You can only approve requests from churches at lower levels than yours.');
        }

        // Additional validation: User must have authority over the requesting church
        if (!$userChurch->canApproveRequestsFor($requestChurch)) {
            return redirect()->route('requests.index')
                ->with('error', 'You do not have authority to approve requests for this church.');
        }

        if ($request->status != 'pending') {
            return redirect()->route('requests.index')->with('error', 'Only pending requests can be approved.');
        }

        // Check financial requirements for upgrade requests
        if (!$request->meetsFinancialRequirements()) {
            $financialDetails = $request->getFinancialRequirementDetails();
            return redirect()->route('requests.index')
                ->with('error', 'Request cannot be approved: ' . $financialDetails['message']);
        }

        try {
            \DB::transaction(function () use ($request, $user) {
                // Update church level and upgrade users if applicable
                $church = $request->church;
                if ($request->type == 'upgrade_branch_to_parish') {
                    $this->upgradeChurch($church, ChurchLevel::PARISH);
                } elseif ($request->type == 'upgrade_parish_to_local') {
                    $this->upgradeChurch($church, ChurchLevel::LOCAL);
                } elseif ($request->type == 'upgrade_local_to_regional') {
                    $this->upgradeChurch($church, ChurchLevel::REGIONAL);
                }

                $request->update([
                    'status' => 'approved',
                    'approved_by' => $user->id,
                    'approved_at' => now(),
                ]);
            });

            return redirect()->route('requests.index')->with('success', 'Request approved successfully. Church and users have been upgraded.');
        } catch (\Exception $e) {
            return redirect()->route('requests.index')->with('error', 'Failed to approve request: ' . $e->getMessage());
        }
    }

    // Reject request
    public function reject(Request $httpRequest, RequestModel $request)
    {
        $this->authorizeRequestAccess($request, 'approve');
        if ($request->status != 'pending') {
            return redirect()->route('requests.index')->with('error', 'Only pending requests can be rejected.');
        }

        $httpRequest->validate([
            'rejection_reason' => 'required|string|max:1000',
        ]);

        $request->update([
            'status' => 'rejected',
            'approved_by' => Auth::user()->id,
            'approved_at' => now(),
            'details' => array_merge($request->details ?? [], ['rejection_reason' => $httpRequest->rejection_reason]),
        ]);

        return redirect()->route('requests.index')->with('success', 'Request rejected successfully.');
    }

    // Helper method to authorize request access
    private function authorizeRequestAccess(RequestModel $request, $action = 'view')
    {
        $user = Auth::user();

        // Super Admin can do anything
        if ($user->hasRole('Super Admin')) {
            return true;
        }

        if ($action == 'view' || $action == 'edit') {
            // Users can view/edit their own requests
            if ($request->user_id == $user->id) {
                return true;
            }

            // Users with view-requests permission can view requests
            if ($action == 'view' && $user->hasPermissionTo('view-requests')) {
                return true;
            }
        }

        if ($action == 'approve') {
            // Use the Request model's canBeApprovedBy method for approval authorization
            if ($request->canBeApprovedBy($user)) {
                return true;
            }
        }

        abort(403, 'Unauthorized action.');
    }

    /**
     * Check if user can create requests for a specific church
     */
    private function canUserCreateRequestForChurch($user, $targetChurch): bool
    {
        $userChurch = $user->church;

        if (!$userChurch) {
            return false;
        }

        // Super Admin can create requests for any church
        if ($user->hasRole('Super Admin')) {
            return true;
        }

        // Same church
        if ($userChurch->id === $targetChurch->id) {
            return true;
        }

        // User's church is ancestor of target church (can manage lower levels)
        if ($userChurch->isAncestorOf($targetChurch)) {
            return true;
        }

        // Same parent church (siblings)
        if ($userChurch->parent_church_id &&
            $userChurch->parent_church_id === $targetChurch->parent_church_id) {
            return true;
        }

        return false;
    }

    /**
     * Upgrade church to new level with proper parent relationship
     */
    private function upgradeChurch(Church $church, ChurchLevel $newLevel): void
    {
        // Determine the correct parent for the new level
        $newParentId = $this->getCorrectParentForLevel($church, $newLevel);

        // Temporarily disable hierarchy validation
        $church->timestamps = false;

        // Update both level and parent in a single query to avoid validation issues
        DB::table('churches')
            ->where('id', $church->id)
            ->update([
                'level' => $newLevel,
                'parent_church_id' => $newParentId,
                'updated_at' => now(),
            ]);

        // Refresh the model to get updated values
        $church->refresh();

        // Upgrade users to appropriate roles
        $church->upgradeUsersToNextLevel();
    }

    /**
     * Get the correct parent church ID for the new level
     */
    private function getCorrectParentForLevel(Church $church, ChurchLevel $newLevel): ?int
    {
        return match ($newLevel) {
            ChurchLevel::NATIONAL => null, // National has no parent
            ChurchLevel::REGIONAL => $this->getNationalChurch()->id, // Regional parent is National
            ChurchLevel::LOCAL => $church->parentChurch->id, // Local keeps current parent (Regional)
            ChurchLevel::PARISH => $church->parentChurch->id, // Parish keeps current parent (Local)
            ChurchLevel::BRANCH => $church->parentChurch->id, // Branch keeps current parent (Parish)
        };
    }

    /**
     * Get the National church
     */
    private function getNationalChurch(): Church
    {
        return Church::where('level', ChurchLevel::NATIONAL)->first();
    }
}