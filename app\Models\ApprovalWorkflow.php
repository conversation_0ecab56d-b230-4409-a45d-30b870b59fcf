<?php

namespace App\Models;

use App\Enums\ChurchLevel;
use Illuminate\Database\Eloquent\Model;

class ApprovalWorkflow extends Model
{
    protected $fillable = [
        'request_id',
        'approver_church_id',
        'approver_user_id',
        'step_order',
        'status',
        'approved_at',
        'rejected_at',
        'comments',
        'required_level',
    ];

    protected $casts = [
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
        'required_level' => ChurchLevel::class,
    ];

    public function request()
    {
        return $this->belongsTo(Request::class);
    }

    public function approverChurch()
    {
        return $this->belongsTo(Church::class, 'approver_church_id');
    }

    public function approverUser()
    {
        return $this->belongsTo(User::class, 'approver_user_id');
    }

    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    public function approve(User $approver, string $comments = null): void
    {
        $this->update([
            'status' => 'approved',
            'approver_user_id' => $approver->id,
            'approved_at' => now(),
            'comments' => $comments,
        ]);
    }

    public function reject(User $approver, string $comments = null): void
    {
        $this->update([
            'status' => 'rejected',
            'approver_user_id' => $approver->id,
            'rejected_at' => now(),
            'comments' => $comments,
        ]);
    }
}
