@extends('layouts.app')

@section('title', 'Messages - FPCT Church Management System (CMS)')
@section('page-title', 'Messages')

@section('breadcrumbs')
    <li>
        <span class="mx-2">/</span>
        <span class="font-medium text-gray-900">Messages</span>
    </li>
@endsection

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
            <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                <i class="fas fa-envelope mr-2 text-blue-600"></i>
                Messages
            </h2>
            <p class="mt-1 text-sm text-gray-500">
                Communicate with your church community
            </p>
        </div>
        <div class="mt-4 flex space-x-3 md:mt-0 md:ml-4">
            @can('send-messages')
            <div class="relative inline-block text-left" x-data="{ open: false }">
                <button @click="open = !open"
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-plus mr-2"></i>
                    New Message
                    <i class="fas fa-chevron-down ml-2"></i>
                </button>

                <div x-show="open"
                     @click.away="open = false"
                     x-transition:enter="transition ease-out duration-100"
                     x-transition:enter-start="transform opacity-0 scale-95"
                     x-transition:enter-end="transform opacity-100 scale-100"
                     x-transition:leave="transition ease-in duration-75"
                     x-transition:leave-start="transform opacity-100 scale-100"
                     x-transition:leave-end="transform opacity-0 scale-95"
                     class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                    <div class="py-1">
                        <a href="{{ route('messages.create') }}"
                           class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-user mr-3 text-green-500"></i>
                            Direct Message
                        </a>
                        <a href="{{ route('messages.create') }}?type=group"
                           class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-users mr-3 text-blue-500"></i>
                            Group Message
                        </a>
                        @can('send-announcements')
                        <a href="{{ route('messages.create') }}?type=announcement"
                           class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-bullhorn mr-3 text-purple-500"></i>
                            Announcement
                        </a>
                        @endcan
                    </div>
                </div>
            </div>
            @endcan
        </div>
    </div>

    <!-- Message Tabs -->
    <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8">
            <a href="{{ route('messages.index') }}"
               class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm
                      {{ !request()->has('filter') ? 'border-blue-500 text-blue-600' : '' }}">
                All Messages
            </a>
            <a href="{{ route('messages.index') }}?filter=unread"
               class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm
                      {{ request('filter') == 'unread' ? 'border-blue-500 text-blue-600' : '' }}">
                Unread
                @if($unreadCount ?? 0 > 0)
                    <span class="bg-red-100 text-red-800 text-xs font-medium ml-2 px-2.5 py-0.5 rounded-full">
                        {{ $unreadCount }}
                    </span>
                @endif
            </a>
            <a href="{{ route('messages.index') }}?filter=sent"
               class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm
                      {{ request('filter') == 'sent' ? 'border-blue-500 text-blue-600' : '' }}">
                Sent
            </a>
            <a href="{{ route('messages.index') }}?filter=announcements"
               class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm
                      {{ request('filter') == 'announcements' ? 'border-blue-500 text-blue-600' : '' }}">
                Announcements
            </a>
        </nav>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <form method="GET" action="{{ route('messages.index') }}" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
                <input type="hidden" name="filter" value="{{ request('filter') }}">

                <!-- Search -->
                <div class="flex-1">
                    <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                    <div class="mt-1 relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text"
                               name="search"
                               id="search"
                               value="{{ request('search') }}"
                               placeholder="Search messages..."
                               class="focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md">
                    </div>
                </div>

                <!-- Date Range -->
                <div class="min-w-0 flex-1 md:max-w-xs">
                    <label for="date_from" class="block text-sm font-medium text-gray-700">From Date</label>
                    <input type="date"
                           name="date_from"
                           id="date_from"
                           value="{{ request('date_from') }}"
                           class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                </div>

                <div class="min-w-0 flex-1 md:max-w-xs">
                    <label for="date_to" class="block text-sm font-medium text-gray-700">To Date</label>
                    <input type="date"
                           name="date_to"
                           id="date_to"
                           value="{{ request('date_to') }}"
                           class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                </div>

                <!-- Buttons -->
                <div class="flex space-x-2">
                    <button type="submit"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-search mr-2"></i>
                        Search
                    </button>
                    <a href="{{ route('messages.index') }}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-times mr-2"></i>
                        Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Messages List -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    @if(request('filter') == 'unread')
                        Unread Messages
                    @elseif(request('filter') == 'sent')
                        Sent Messages
                    @elseif(request('filter') == 'announcements')
                        Announcements
                    @else
                        All Messages
                    @endif
                </h3>
                <p class="text-sm text-gray-500">
                    {{ $messages->total() }} messages
                </p>
            </div>
        </div>

        @if($messages->count() > 0)
        <ul class="divide-y divide-gray-200">
            @foreach($messages as $message)
            @php
                $isOutgoing = $message->sender_id === auth()->id();
                $isIncoming = !$isOutgoing;
            @endphp
            <li class="hover:bg-gray-50 {{ $isOutgoing ? 'bg-blue-25' : '' }}">
                <div class="px-4 py-4 flex items-center justify-between">
                    <!-- Direction Indicator -->
                    <div class="flex-shrink-0 mr-3">
                        @if($isOutgoing)
                            <div class="flex items-center justify-center w-8 h-8 rounded-full bg-green-100">
                                <i class="fas fa-arrow-right text-green-600 text-sm"></i>
                            </div>
                        @else
                            <div class="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100">
                                <i class="fas fa-arrow-left text-blue-600 text-sm"></i>
                            </div>
                        @endif
                    </div>

                    <div class="flex items-center flex-1 min-w-0">
                        <!-- Sender Avatar -->
                        <div class="flex-shrink-0">
                            <div class="h-10 w-10 rounded-full {{ $isOutgoing ? 'bg-green-100' : 'bg-blue-100' }} flex items-center justify-center">
                                <span class="text-sm font-medium {{ $isOutgoing ? 'text-green-600' : 'text-blue-600' }}">
                                    {{ substr($message->sender->full_name, 0, 1) }}
                                </span>
                            </div>
                        </div>

                        <!-- Message Content -->
                        <div class="ml-4 flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <div class="text-sm font-medium text-gray-900 truncate">
                                    @if($isOutgoing)
                                        <span class="text-green-600 font-semibold">You</span>
                                        <span class="text-gray-500 font-normal text-xs ml-1">(sent to {{ $message->recipients_count }} recipient{{ $message->recipients_count > 1 ? 's' : '' }})</span>
                                    @else
                                        {{ $message->sender->full_name }}
                                        @if($message->sender->church)
                                            <span class="text-gray-500 font-normal">
                                                • {{ $message->sender->church->name }}
                                            </span>
                                        @endif
                                    @endif
                                </div>
                                <div class="flex items-center space-x-2">
                                    <!-- Incoming/Outgoing Badge -->
                                    @if($isOutgoing)
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-paper-plane mr-1"></i>
                                            Sent
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <i class="fas fa-inbox mr-1"></i>
                                            Received
                                        </span>
                                    @endif
                                    <!-- Message Type Badge -->
                                    @if($message->is_announcement)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-bullhorn mr-1"></i>
                                            Announcement
                                        </span>
                                    @elseif($message->is_group_message)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <i class="fas fa-users mr-1"></i>
                                            Group
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-user mr-1"></i>
                                            Direct
                                        </span>
                                    @endif

                                    <!-- Priority Badge -->
                                    @if($message->priority === 'high')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                            <i class="fas fa-exclamation mr-1"></i>
                                            High Priority
                                        </span>
                                    @endif
                                </div>
                            </div>

                            <div class="mt-1">
                                <p class="text-sm text-gray-600 line-clamp-2">
                                    {{ Str::limit($message->content, 120) }}
                                </p>
                            </div>

                            <div class="mt-2 flex items-center justify-between">
                                <div class="text-xs text-gray-500">
                                    {{ $message->created_at ? $message->created_at->diffForHumans() : 'Unknown' }}
                                </div>

                                @if($isIncoming && $message->recipients_count > 1)
                                <div class="text-xs text-gray-500">
                                    <i class="fas fa-users mr-1"></i>
                                    {{ $message->recipients_count }} recipients
                                </div>
                                @endif
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="ml-4 flex-shrink-0 flex items-center space-x-2">
                            @if($isIncoming && $message->isUnreadForUser(auth()->user()))
                                <div class="w-3 h-3 bg-blue-500 rounded-full animate-pulse" title="Unread Message"></div>
                            @elseif($isOutgoing)
                                <div class="text-xs text-green-600" title="Message Sent">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                            @endif

                            <a href="{{ route('messages.show', $message) }}"
                               class="text-blue-600 hover:text-blue-900"
                               title="View Message">
                                <i class="fas fa-eye"></i>
                            </a>

                            @can('send-messages')
                            <a href="{{ route('messages.create') }}?reply_to={{ $message->id }}"
                               class="text-green-600 hover:text-green-900"
                               title="Reply">
                                <i class="fas fa-reply"></i>
                            </a>
                            @endcan

                            @if($isOutgoing || auth()->user()->hasPermissionTo('delete-messages') || auth()->user()->hasRole('National IT'))
                            <form action="{{ route('messages.destroy', $message) }}"
                                  method="POST"
                                  class="inline"
                                  onsubmit="return confirm('Are you sure you want to delete this {{ $message->is_announcement ? 'announcement' : ($message->is_group_message ? 'group message' : 'message') }}? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit"
                                        class="text-red-600 hover:text-red-900"
                                        title="Delete Message">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                            @endif
                        </div>
                    </div>
                </div>
            </li>
            @endforeach
        </ul>

        <!-- Pagination -->
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            {{ $messages->appends(request()->query())->links() }}
        </div>
        @else
        <div class="text-center py-12">
            <i class="fas fa-envelope text-gray-400 text-4xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No messages found</h3>
            <p class="text-gray-500 mb-4">
                @if(request()->hasAny(['search', 'filter', 'date_from', 'date_to']))
                    No messages match your current filters.
                @else
                    Start communicating with your church community.
                @endif
            </p>
            @can('send-messages')
            <a href="{{ route('messages.create') }}"
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <i class="fas fa-plus mr-2"></i>
                Send Message
            </a>
            @endcan
        </div>
        @endif
    </div>
</div>

@push('styles')
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endpush
@endsection