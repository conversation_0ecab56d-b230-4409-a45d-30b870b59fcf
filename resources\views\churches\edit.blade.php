@extends('layouts.app')

@section('title', __('churches.edit_church'))
@section('page-title', __('churches.edit_church'))

@section('breadcrumbs')
    <li>
        <span class="mx-2">/</span>
        <a href="{{ route('churches.index') }}" class="hover:text-gray-700">{{ __('churches.churches') }}</a>
    </li>
    <li>
        <span class="mx-2">/</span>
        <a href="{{ route('churches.show', $church) }}" class="hover:text-gray-700">{{ $church->name }}</a>
    </li>
    <li>
        <span class="mx-2">/</span>
        <span class="font-medium text-gray-900">{{ __('common.edit') }}</span>
    </li>
@endsection

@section('page-actions')
    <div class="flex items-center space-x-3">
        <!-- Language Switcher -->
        <x-language-switcher position="bottom-right" size="normal" />

        <a href="{{ route('churches.show', $church) }}"
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <i class="fas fa-eye mr-2"></i>
            {{ __('common.view') }}
        </a>
        <a href="{{ route('churches.index') }}"
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <i class="fas fa-arrow-left mr-2"></i>
            {{ __('common.back') }}
        </a>
    </div>
@endsection

@section('content')
    <div class="max-w-6xl mx-auto space-y-6">
        <!-- Quick Language Switcher -->
        <div class="flex justify-end">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-2">
                <div class="flex items-center space-x-2 text-sm">
                    <i class="fas fa-globe text-gray-400"></i>
                    <span class="text-gray-600">{{ __('common.language') }}:</span>
                    <div class="flex space-x-1">
                        @foreach(config('app.locale_names') as $locale => $info)
                            <button onclick="switchLanguage('{{ $locale }}')"
                                    class="px-2 py-1 rounded text-xs font-medium transition-colors duration-200 {{ app()->getLocale() === $locale ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100' }}">
                                {{ $info['flag'] }} {{ $info['native'] }}
                            </button>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
        <!-- Church Header -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="bg-gradient-to-r from-green-600 to-green-700 px-6 py-4">
                <div class="flex items-center space-x-4">
                    <div class="flex-shrink-0">
                        <div class="h-16 w-16 bg-green-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-church text-white text-2xl"></i>
                        </div>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-white">{{ __('churches.editing_church') }}: {{ $church->name }}</h1>
                        <p class="text-green-100">{{ __('common.' . strtolower($church->level->value)) }} - {{ $church->location }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-users text-blue-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">{{ __('churches.total_members') }}</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $stats['total_users'] }}</p>
                        <p class="text-xs text-gray-500">{{ $stats['active_users'] }} {{ __('common.active') }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-crown text-green-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">{{ __('churches.leaders') }}</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $stats['total_leaders'] }}</p>
                        <p class="text-xs text-gray-500">{{ __('churches.leadership_positions') }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-sitemap text-purple-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">{{ __('churches.child_churches') }}</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $stats['child_churches'] }}</p>
                        <p class="text-xs text-gray-500">{{ __('churches.under_supervision') }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-calendar-alt text-orange-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">{{ __('churches.established') }}</p>
                        <p class="text-lg font-bold text-gray-900">
                            {{ $church->date_established ? $church->date_established->format('M Y') : __('common.unknown') }}
                        </p>
                        <p class="text-xs text-gray-500">
                            {{ $church->date_established ? $church->date_established->diffForHumans() : '' }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <form method="POST" action="{{ route('churches.update', $church) }}" class="space-y-6">
            @csrf
            @method('PUT')

            <!-- Basic Information -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-info-circle mr-2 text-blue-600"></i>
                        {{ __('churches.basic_information') }}
                    </h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.church_name') }} <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-church text-gray-400"></i>
                                </div>
                                <input type="text"
                                       id="name"
                                       name="name"
                                       value="{{ old('name', $church->name) }}"
                                       required
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('name') border-red-300 @enderror"
                                       placeholder="{{ __('churches.enter_church_name') }}">
                            </div>
                            @error('name')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div>
                            <label for="level" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.church_level') }} <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-layer-group text-gray-400"></i>
                                </div>
                                <select name="level"
                                        id="level"
                                        required
                                        class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('level') border-red-300 @enderror">
                                    <option value="">{{ __('churches.select_level') }}</option>
                                    @foreach ($levels as $level)
                                        <option value="{{ $level }}" {{ old('level', $church->level->value) == $level ? 'selected' : '' }}>
                                            {{ __('common.' . strtolower($level)) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            @error('level')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div>
                            <label for="location" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.location') }} <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-map-marker-alt text-gray-400"></i>
                                </div>
                                <input type="text"
                                       id="location"
                                       name="location"
                                       value="{{ old('location', $church->location) }}"
                                       required
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('location') border-red-300 @enderror"
                                       placeholder="{{ __('churches.enter_location') }}">
                            </div>
                            @error('location')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div>
                            <label for="phone_number" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.phone_number') }}
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-phone text-gray-400"></i>
                                </div>
                                <input type="tel"
                                       id="phone_number"
                                       name="phone_number"
                                       value="{{ old('phone_number', $church->phone_number) }}"
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('phone_number') border-red-300 @enderror"
                                       placeholder="{{ __('churches.enter_phone_number') }}">
                            </div>
                            @error('phone_number')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.email') }}
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-envelope text-gray-400"></i>
                                </div>
                                <input type="email"
                                       id="email"
                                       name="email"
                                       value="{{ old('email', $church->email) }}"
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('email') border-red-300 @enderror"
                                       placeholder="{{ __('churches.enter_email') }}">
                            </div>
                            @error('email')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div class="lg:col-span-2">
                            <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.address') }}
                            </label>
                            <div class="relative">
                                <div class="absolute top-3 left-3 pointer-events-none">
                                    <i class="fas fa-map-marked-alt text-gray-400"></i>
                                </div>
                                <textarea id="address"
                                          name="address"
                                          rows="3"
                                          class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('address') border-red-300 @enderror"
                                          placeholder="{{ __('churches.enter_address') }}">{{ old('address', $church->address) }}</textarea>
                            </div>
                            @error('address')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div>
                            <label for="district" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.district') }}
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-map text-gray-400"></i>
                                </div>
                                <input type="text"
                                       id="district"
                                       name="district"
                                       value="{{ old('district', $church->district) }}"
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('district') border-red-300 @enderror"
                                       placeholder="{{ __('churches.enter_district') }}">
                            </div>
                            @error('district')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div>
                            <label for="region" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.region') }}
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-globe-africa text-gray-400"></i>
                                </div>
                                <input type="text"
                                       id="region"
                                       name="region"
                                       value="{{ old('region', $church->region) }}"
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('region') border-red-300 @enderror"
                                       placeholder="{{ __('churches.enter_region') }}">
                            </div>
                            @error('region')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div>
                            <label for="date_established" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.date_established') }}
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-calendar text-gray-400"></i>
                                </div>
                                <input type="date"
                                       id="date_established"
                                       name="date_established"
                                       value="{{ old('date_established', $church->date_established?->format('Y-m-d')) }}"
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('date_established') border-red-300 @enderror">
                            </div>
                            @error('date_established')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div class="lg:col-span-2">
                            <label for="parent_church_id" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.parent_church') }}
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-sitemap text-gray-400"></i>
                                </div>
                                <select name="parent_church_id"
                                        id="parent_church_id"
                                        class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('parent_church_id') border-red-300 @enderror">
                                    <option value="">{{ __('churches.no_parent_church') }}</option>
                                    @foreach ($parentChurches as $parent)
                                        <option value="{{ $parent->id }}" {{ old('parent_church_id', $church->parent_church_id) == $parent->id ? 'selected' : '' }}>
                                            {{ $parent->name }} ({{ __('common.' . strtolower($parent->level->value)) }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            @error('parent_church_id')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Leaders Management -->
            <!-- Leaders Section -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-crown mr-2 text-green-600"></i>
                        {{ __('churches.church_leaders') }}
                        <span class="ml-2 bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                            {{ $leaders->count() }}
                        </span>
                    </h3>
                    <p class="mt-1 text-sm text-gray-600">{{ __('churches.leaders_auto_assigned') }}</p>
                </div>
                <div class="p-6">
                    @if($leaders->count() > 0)
                        <div class="space-y-4">
                            @foreach($leaders as $leader)
                                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            @if($leader->user->profile_picture)
                                                <img class="h-12 w-12 rounded-full object-cover"
                                                     src="{{ asset('storage/' . $leader->user->profile_picture) }}"
                                                     alt="{{ $leader->user->full_name }}">
                                            @else
                                                <div class="h-12 w-12 rounded-full bg-gray-300 flex items-center justify-center">
                                                    <i class="fas fa-user text-gray-600 text-lg"></i>
                                                </div>
                                            @endif
                                        </div>
                                        <div class="ml-4">
                                            <h4 class="text-sm font-medium text-gray-900">{{ $leader->user->full_name }}</h4>
                                            <p class="text-sm text-gray-600">{{ $leader->position }}</p>
                                            @if($leader->user->email)
                                                <p class="text-xs text-gray-500">{{ $leader->user->email }}</p>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            {{ $leader->role }}
                                        </span>
                                        @if($leader->user->phone_number)
                                            <a href="tel:{{ $leader->user->phone_number }}"
                                               class="text-gray-400 hover:text-gray-600">
                                                <i class="fas fa-phone"></i>
                                            </a>
                                        @endif
                                        @if($leader->user->email)
                                            <a href="mailto:{{ $leader->user->email }}"
                                               class="text-gray-400 hover:text-gray-600">
                                                <i class="fas fa-envelope"></i>
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <i class="fas fa-crown text-gray-300 text-4xl mb-4"></i>
                            <p class="text-gray-500 text-lg">{{ __('churches.no_leaders_assigned') }}</p>
                            <p class="text-gray-400 text-sm">{{ __('churches.leaders_based_on_roles') }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Demographics Section -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-chart-pie mr-2 text-indigo-600"></i>
                        {{ __('churches.demographics') }}
                    </h3>
                    <p class="mt-1 text-sm text-gray-600">{{ __('churches.church_demographics_description') }}</p>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                        <div>
                            <label for="children_count" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.children_count') }}
                                <span class="text-xs text-gray-500">({{ __('churches.age_0_12') }})</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-child text-gray-400"></i>
                                </div>
                                <input type="number"
                                       id="children_count"
                                       name="children_count"
                                       value="{{ old('children_count', $church->children_count ?? 0) }}"
                                       min="0"
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 @error('children_count') border-red-300 @enderror"
                                       placeholder="0">
                            </div>
                            @error('children_count')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div>
                            <label for="youth_count" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.youth_count') }}
                                <span class="text-xs text-gray-500">({{ __('churches.age_13_17') }})</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-running text-gray-400"></i>
                                </div>
                                <input type="number"
                                       id="youth_count"
                                       name="youth_count"
                                       value="{{ old('youth_count', $church->youth_count ?? 0) }}"
                                       min="0"
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 @error('youth_count') border-red-300 @enderror"
                                       placeholder="0">
                            </div>
                            @error('youth_count')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div>
                            <label for="young_adults_count" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.young_adults_count') }}
                                <span class="text-xs text-gray-500">({{ __('churches.age_18_35') }})</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-user-friends text-gray-400"></i>
                                </div>
                                <input type="number"
                                       id="young_adults_count"
                                       name="young_adults_count"
                                       value="{{ old('young_adults_count', $church->young_adults_count ?? 0) }}"
                                       min="0"
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 @error('young_adults_count') border-red-300 @enderror"
                                       placeholder="0">
                            </div>
                            @error('young_adults_count')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div>
                            <label for="elders_count" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.elders_count') }}
                                <span class="text-xs text-gray-500">({{ __('churches.age_36_plus') }})</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-user-tie text-gray-400"></i>
                                </div>
                                <input type="number"
                                       id="elders_count"
                                       name="elders_count"
                                       value="{{ old('elders_count', $church->elders_count ?? 0) }}"
                                       min="0"
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 @error('elders_count') border-red-300 @enderror"
                                       placeholder="0">
                            </div>
                            @error('elders_count')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div class="flex items-start">
                            <i class="fas fa-info-circle text-blue-500 mt-0.5 mr-3"></i>
                            <div class="text-sm text-blue-700">
                                <p class="font-medium">{{ __('churches.demographic_note_title') }}</p>
                                <p class="mt-1">{{ __('churches.demographic_note_description') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex justify-end space-x-4">
                    <a href="{{ route('churches.show', $church) }}"
                       class="inline-flex items-center px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                        <i class="fas fa-times mr-2"></i>
                        {{ __('common.cancel') }}
                    </a>
                    <button type="submit"
                            class="inline-flex items-center px-6 py-3 border border-transparent rounded-lg text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i>
                        {{ __('churches.update_church') }}
                    </button>
                </div>
            </div>
        </form>
    </div>


@endsection