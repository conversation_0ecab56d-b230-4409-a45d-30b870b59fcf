<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get the locale from session, URL parameter, or default
        $locale = $this->getLocale($request);
        
        // Validate the locale
        if (!in_array($locale, config('app.supported_locales', ['en', 'sw']))) {
            $locale = config('app.locale', 'en');
        }
        
        // Set the application locale
        App::setLocale($locale);
        
        // Store in session for persistence
        Session::put('locale', $locale);
        
        return $next($request);
    }
    
    /**
     * Get the locale from various sources
     */
    private function getLocale(Request $request): string
    {
        // 1. Check if locale is being changed via request
        if ($request->has('locale')) {
            return $request->get('locale');
        }
        
        // 2. Check session
        if (Session::has('locale')) {
            return Session::get('locale');
        }
        
        // 3. Check user preference (if authenticated)
        if (auth()->check() && auth()->user()->locale) {
            return auth()->user()->locale;
        }
        
        // 4. Check browser language preference
        $acceptLanguage = $request->header('Accept-Language');
        if ($acceptLanguage) {
            $preferredLanguages = $this->parseAcceptLanguage($acceptLanguage);
            foreach ($preferredLanguages as $lang) {
                if (in_array($lang, config('app.supported_locales', ['en', 'sw']))) {
                    return $lang;
                }
            }
        }
        
        // 5. Default to app locale
        return config('app.locale', 'en');
    }
    
    /**
     * Parse Accept-Language header
     */
    private function parseAcceptLanguage(string $acceptLanguage): array
    {
        $languages = [];
        $parts = explode(',', $acceptLanguage);
        
        foreach ($parts as $part) {
            $part = trim($part);
            if (strpos($part, ';') !== false) {
                $part = explode(';', $part)[0];
            }
            
            // Convert language codes
            $lang = strtolower(trim($part));
            if (strpos($lang, '-') !== false) {
                $lang = explode('-', $lang)[0];
            }
            
            // Map common language codes
            $langMap = [
                'sw' => 'sw',
                'swahili' => 'sw',
                'en' => 'en',
                'english' => 'en',
            ];
            
            if (isset($langMap[$lang])) {
                $languages[] = $langMap[$lang];
            }
        }
        
        return array_unique($languages);
    }
}
