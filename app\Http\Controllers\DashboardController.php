<?php

namespace App\Http\Controllers;

use App\Models\Church;
use App\Models\ChurchLeader;
use App\Models\Message;
use App\Models\Request;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $user = Auth::user();

        // Redirect financial users to financial dashboard
        if ($user->hasAnyRole(['Treasurer', 'National Treasurer', 'Regional Treasurer', 'Local Treasurer', 'Parish Treasurer', 'Branch Treasurer', 'Assistant Treasurer'])) {
            return redirect()->route('financial.dashboard');
        }

        $data = $this->getDashboardData($user);

        return view('dashboard.index', $data);
    }

    private function getDashboardData($user)
    {
        $data = [
            'user' => $user,
            'church' => $user->church,
            'pending_requests' => [],
            'recent_messages' => [],
            'notifications' => [],
            'statistics' => [],
        ];

        // Common data: Recent messages and notifications
        $data['recent_messages'] = Message::where('sender_id', $user->id)
            ->orWhereHas('recipients', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->with('sender', 'recipients')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        $data['notifications'] = $user->receivedMessages()
            ->wherePivot('read_at', null)
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Role-based data
        switch ($user->role) {
            case 'Super Admin':
                $data['statistics'] = [
                    'total_churches' => Church::count(),
                    'total_users' => User::count(),
                    'active_users' => User::where('is_active', true)->count(),
                    'pending_requests' => Request::where('status', 'pending')->count(),
                    'unread_messages' => $user->unreadMessages()->count(),
                    'total_messages' => Message::count(),
                ];
                $data['pending_requests'] = Request::where('status', 'pending')
                    ->with('church', 'user')
                    ->get();
                break;

            case 'Archbishop':
            case 'Assistant Archbishop':
            case 'General Secretary':
            case 'Assistant Secretary':
            case 'Treasurer':
            case 'IT':
            case 'HR':
                // National HQ roles can view all regional church requests
                $regional_churches = Church::where('level', \App\Enums\ChurchLevel::REGIONAL)->pluck('id');
                $data['pending_requests'] = Request::whereIn('church_id', $regional_churches)
                    ->where('status', 'pending')
                    ->with('church', 'user')
                    ->get();
                $data['statistics'] = [
                    'total_churches' => Church::count(),
                    'total_users' => User::count(),
                    'active_users' => User::where('is_active', true)->count(),
                    'regional_churches' => $regional_churches->count(),
                    'pending_requests' => Request::whereIn('church_id', $regional_churches)->where('status', 'pending')->count(),
                    'unread_messages' => $user->unreadMessages()->count(),
                ];
                break;

            case 'Bishop':
                // Regional Bishop can view requests from local churches under their region
                $church_leader = ChurchLeader::where('user_id', $user->id)->first();
                if ($church_leader && $user->church->level === \App\Enums\ChurchLevel::REGIONAL) {
                    $local_churches = Church::where('parent_church_id', $user->church_id)->pluck('id');
                    $data['pending_requests'] = Request::whereIn('church_id', $local_churches)
                        ->where('status', 'pending')
                        ->with('church', 'user')
                        ->get();
                    $data['statistics'] = [
                        'total_churches' => Church::count(),
                        'total_users' => User::where('church_id', $user->church_id)->count(),
                        'active_users' => User::where('church_id', $user->church_id)->where('is_active', true)->count(),
                        'local_churches' => $local_churches->count(),
                        'pending_requests' => Request::whereIn('church_id', $local_churches)->where('status', 'pending')->count(),
                        'unread_messages' => $user->unreadMessages()->count(),
                    ];
                }
                break;

            case 'Pastor':
                // Local Church Pastor can view requests from parishes and branches
                $church_leader = ChurchLeader::where('user_id', $user->id)->first();
                if ($church_leader && $user->church->level === \App\Enums\ChurchLevel::LOCAL) {
                    $sub_churches = Church::where('parent_church_id', $user->church_id)->pluck('id');
                    $data['pending_requests'] = Request::whereIn('church_id', $sub_churches)
                        ->where('status', 'pending')
                        ->with('church', 'user')
                        ->get();
                    $data['statistics'] = [
                        'total_churches' => Church::count(),
                        'total_users' => User::where('church_id', $user->church_id)->count(),
                        'active_users' => User::where('church_id', $user->church_id)->where('is_active', true)->count(),
                        'parishes_branches' => $sub_churches->count(),
                        'pending_requests' => Request::whereIn('church_id', $sub_churches)->where('status', 'pending')->count(),
                        'unread_messages' => $user->unreadMessages()->count(),
                    ];
                }
                break;

            case 'Secretary':
            case 'Local Treasurer':
            case 'Local IT':
                // Limited view: church details and messages only
                $data['statistics'] = [
                    'total_churches' => 1, // Only their church
                    'total_users' => User::where('church_id', $user->church_id)->count(),
                    'active_users' => User::where('church_id', $user->church_id)->where('is_active', true)->count(),
                    'pending_requests' => Request::where('church_id', $user->church_id)->where('status', 'pending')->count(),
                    'unread_messages' => $user->unreadMessages()->count(),
                    'church_name' => $user->church->name ?? 'N/A',
                    'church_level' => $user->church->level->value ?? 'N/A',
                ];
                break;

            default:
                // Default for other users (e.g., members)
                $data['statistics'] = [
                    'total_churches' => 1, // Only their church
                    'total_users' => User::where('church_id', $user->church_id)->count(),
                    'active_users' => User::where('church_id', $user->church_id)->where('is_active', true)->count(),
                    'pending_requests' => 0, // Regular members can't see requests
                    'unread_messages' => $user->unreadMessages()->count(),
                    'church_name' => $user->church->name ?? 'N/A',
                    'church_level' => $user->church->level->value ?? 'N/A',
                ];
                break;
        }

        return $data;
    }
}