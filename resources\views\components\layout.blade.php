<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ $title ?? 'FPCT Church Management System (CMS)' }}</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Alpine.js for interactive components -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    @vite(['resources/js/app.js'])

    <script>
        window.Laravel = {
            userId: {{ Auth::id() ?? 'null' }},
            csrfToken: '{{ csrf_token() }}'
        };
    </script>

    @stack('styles')
</head>

<body class="bg-gray-50 font-sans antialiased">
    @auth
        <!-- Authenticated layout with sidebar -->
        <div class="flex h-screen bg-gray-100" x-data="{ sidebarOpen: false }">
            <!-- Sidebar for desktop -->
            <div class="hidden lg:flex lg:flex-shrink-0">
                <div class="flex flex-col w-64">
                    <div class="flex flex-col flex-grow bg-gradient-to-b from-blue-600 to-blue-800 pt-5 pb-4 overflow-y-auto">
                        <!-- Logo -->
                        <div class="flex items-center flex-shrink-0 px-4">
                            <a href="{{ route('dashboard.index') }}" class="flex items-center text-white font-bold text-xl">
                                <i class="fas fa-church mr-3"></i>
                                FPCT Church Management System (CMS)
                            </a>
                        </div>

                        <!-- Navigation Links -->
                        <nav class="mt-8 flex-1 px-2 space-y-1">
                            <!-- Dashboard -->
                            <a href="{{ route('dashboard.index') }}"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 {{ request()->routeIs('dashboard.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                <i class="fas fa-tachometer-alt mr-3"></i>
                                {{ __('common.dashboard') }}
                            </a>

                            @can('manage-users')
                            <!-- Users -->
                            <a href="{{ route('users.index') }}"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 {{ request()->routeIs('users.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                <i class="fas fa-users mr-3"></i>
                                {{ __('common.users') }}
                            </a>
                            @endcan

                            @can('manage-churches')
                            <!-- Churches -->
                            <a href="{{ route('churches.index') }}"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 {{ request()->routeIs('churches.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                <i class="fas fa-church mr-3"></i>
                                {{ __('common.churches') }}
                            </a>
                            @endcan

                            <!-- Messages -->
                            <a href="{{ route('messages.index') }}"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 {{ request()->routeIs('messages.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                <i class="fas fa-envelope mr-3"></i>
                                {{ __('common.messages') }}
                            </a>

                            @can('manage-requests')
                            <!-- Requests -->
                            <a href="{{ route('requests.index') }}"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 {{ request()->routeIs('requests.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                <i class="fas fa-clipboard-list mr-3"></i>
                                {{ __('common.requests') }}
                            </a>
                            @endcan

                            @can('manage-permissions')
                            <!-- Roles & Permissions -->
                            <div class="space-y-1">
                                <a href="{{ route('roles.index') }}"
                                   class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 {{ request()->routeIs('roles.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                    <i class="fas fa-user-tag mr-3"></i>
                                    {{ __('common.roles') }}
                                </a>
                                <a href="{{ route('permissions.index') }}"
                                   class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 {{ request()->routeIs('permissions.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                    <i class="fas fa-key mr-3"></i>
                                    {{ __('common.permissions') }}
                                </a>
                            </div>
                            @endcan
                        </nav>

                        <!-- User Menu -->
                        <div class="flex-shrink-0 flex border-t border-blue-700 p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    @if(Auth::user()->profile_picture)
                                        <img class="h-8 w-8 rounded-full" src="{{ asset('storage/' . Auth::user()->profile_picture) }}" alt="{{ Auth::user()->full_name }}">
                                    @else
                                        <div class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                                            <span class="text-sm font-medium text-white">{{ substr(Auth::user()->full_name, 0, 1) }}</span>
                                        </div>
                                    @endif
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-white">{{ Auth::user()->full_name }}</p>
                                    <div class="flex space-x-2">
                                        <a href="{{ route('user.profile') }}" class="text-xs text-blue-200 hover:text-white">{{ __('common.profile') }}</a>
                                        <form method="POST" action="{{ route('logout') }}" class="inline">
                                            @csrf
                                            <button type="submit" class="text-xs text-blue-200 hover:text-white">{{ __('common.logout') }}</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile sidebar -->
            <div x-show="sidebarOpen" class="fixed inset-0 flex z-40 lg:hidden" x-cloak>
                <div x-show="sidebarOpen" x-transition:enter="transition-opacity ease-linear duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="transition-opacity ease-linear duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 bg-gray-600 bg-opacity-75" @click="sidebarOpen = false"></div>
                
                <div x-show="sidebarOpen" x-transition:enter="transition ease-in-out duration-300 transform" x-transition:enter-start="-translate-x-full" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out duration-300 transform" x-transition:leave-start="translate-x-0" x-transition:leave-end="-translate-x-full" class="relative flex-1 flex flex-col max-w-xs w-full bg-gradient-to-b from-blue-600 to-blue-800">
                    <!-- Mobile sidebar content (same as desktop) -->
                    <div class="flex flex-col flex-grow pt-5 pb-4 overflow-y-auto">
                        <div class="flex items-center flex-shrink-0 px-4">
                            <a href="{{ route('dashboard.index') }}" class="flex items-center text-white font-bold text-xl">
                                <i class="fas fa-church mr-3"></i>
                                FPCT Church Management System (CMS)
                            </a>
                        </div>
                        <!-- Same navigation as desktop -->
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <div class="flex flex-col w-0 flex-1 overflow-hidden">
                <!-- Top navigation -->
                <div class="relative z-10 flex-shrink-0 flex h-16 bg-white shadow">
                    <button @click="sidebarOpen = true" class="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 lg:hidden">
                        <i class="fas fa-bars"></i>
                    </button>
                    
                    <div class="flex-1 px-4 flex justify-between">
                        <div class="flex-1 flex">
                            <!-- Breadcrumbs or page title can go here -->
                        </div>
                        <div class="ml-4 flex items-center md:ml-6">
                            <!-- Language switcher -->
                            <x-language-switcher />
                        </div>
                    </div>
                </div>

                <!-- Page content -->
                <main class="flex-1 relative overflow-y-auto focus:outline-none">
                    <div class="py-6">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
                            {{ $slot }}
                        </div>
                    </div>
                </main>
            </div>
        </div>
    @else
        <!-- Guest layout (for login, register, password reset, etc.) -->
        <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div class="max-w-md w-full space-y-8">
                <div>
                    <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
                        <i class="fas fa-church text-blue-600 text-xl"></i>
                    </div>
                    <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                        FPCT Church Management System (CMS)
                    </h2>
                </div>
                <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                    {{ $slot }}
                </div>
            </div>
        </div>
    @endauth

    @stack('scripts')
</body>

</html>
