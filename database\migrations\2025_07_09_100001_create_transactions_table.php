<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->string('transaction_id')->unique(); // Internal transaction ID
            $table->string('reference_number')->unique(); // External reference (AzamPay, etc.)
            
            // Transaction parties
            $table->foreignId('from_church_id')->constrained('churches')->onDelete('cascade');
            $table->foreignId('to_church_id')->constrained('churches')->onDelete('cascade');
            $table->foreignId('initiated_by_user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('approved_by_user_id')->nullable()->constrained('users')->onDelete('set null');
            
            // Transaction details
            $table->decimal('amount', 15, 2);
            $table->string('currency', 3)->default('TZS');
            $table->string('type'); // revenue_collection, contribution, transfer, refund
            $table->string('status')->default('pending'); // pending, processing, completed, failed, cancelled
            $table->text('description')->nullable();
            $table->text('notes')->nullable();
            
            // Contribution reference
            $table->foreignId('contribution_id')->nullable()->constrained('contributions')->onDelete('set null');
            
            // Payment details
            $table->string('payment_method'); // bank_transfer, mobile_money, cash
            $table->string('payment_provider')->nullable(); // azampay, manual, etc.
            $table->json('payment_details')->nullable(); // Provider-specific details
            $table->string('provider_transaction_id')->nullable();
            $table->json('provider_response')->nullable();
            
            // Timestamps
            $table->timestamp('initiated_at');
            $table->timestamp('approved_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('failed_at')->nullable();
            $table->timestamps();

            $table->index(['from_church_id', 'status']);
            $table->index(['to_church_id', 'status']);
            $table->index(['status', 'type']);
            $table->index(['contribution_id']);
            $table->index(['reference_number']);
            $table->index(['provider_transaction_id']);
            $table->index(['initiated_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
