@extends('layouts.app')

@section('title', __('Generate Financial Report'))

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ __('Generate Financial Report') }}</h1>
                    <p class="mt-2 text-gray-600">{{ __('Create comprehensive financial reports for analysis') }}</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('financial-reports.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-arrow-left mr-2"></i>{{ __('Back to Reports') }}
                    </a>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Report Generation Form -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                            <i class="fas fa-chart-bar mr-2 text-blue-600"></i>
                            {{ __('Report Configuration') }}
                        </h3>
                        <p class="mt-1 text-sm text-gray-500">{{ __('Configure your financial report parameters') }}</p>
                    </div>
                    <div class="px-6 py-6">
                        <form method="POST" action="{{ route('financial-reports.store') }}" id="reportForm">
                            @csrf
                            
                            <!-- Report Type -->
                            <div class="mb-6">
                                <label for="report_type" class="block text-sm font-medium text-gray-700 mb-2">{{ __('Report Type') }} <span class="text-red-500">*</span></label>
                                <select id="report_type" name="report_type" required class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('report_type') border-red-300 @enderror">
                                    <option value="">{{ __('Select report type') }}</option>
                                    <option value="monthly" {{ old('report_type') == 'monthly' ? 'selected' : '' }}>{{ __('Monthly Report') }}</option>
                                    <option value="quarterly" {{ old('report_type') == 'quarterly' ? 'selected' : '' }}>{{ __('Quarterly Report') }}</option>
                                    <option value="annual" {{ old('report_type') == 'annual' ? 'selected' : '' }}>{{ __('Annual Report') }}</option>
                                    <option value="contribution_summary" {{ old('report_type') == 'contribution_summary' ? 'selected' : '' }}>{{ __('Contribution Summary') }}</option>
                                    <option value="custom" {{ old('report_type') == 'custom' ? 'selected' : '' }}>{{ __('Custom Period') }}</option>
                                </select>
                                @error('report_type')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Period Selection -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <label for="period_start" class="block text-sm font-medium text-gray-700 mb-2">{{ __('Start Date') }} <span class="text-red-500">*</span></label>
                                    <input type="date" id="period_start" name="period_start" value="{{ old('period_start') }}" required class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('period_start') border-red-300 @enderror">
                                    @error('period_start')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div>
                                    <label for="period_end" class="block text-sm font-medium text-gray-700 mb-2">{{ __('End Date') }} <span class="text-red-500">*</span></label>
                                    <input type="date" id="period_end" name="period_end" value="{{ old('period_end') }}" required class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('period_end') border-red-300 @enderror">
                                    @error('period_end')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <!-- Report Filters -->
                            <div class="mb-6">
                                <h4 class="text-sm font-medium text-gray-900 mb-4">{{ __('Report Filters (Optional)') }}</h4>
                                <div class="space-y-4">
                                    <!-- Include Contributions -->
                                    <div class="flex items-center">
                                        <input type="checkbox" id="include_contributions" name="filters[include_contributions]" value="1" {{ old('filters.include_contributions') ? 'checked' : '' }} class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <label for="include_contributions" class="ml-2 block text-sm text-gray-700">{{ __('Include contribution details') }}</label>
                                    </div>
                                    
                                    <!-- Include Transaction Details -->
                                    <div class="flex items-center">
                                        <input type="checkbox" id="include_transactions" name="filters[include_transactions]" value="1" {{ old('filters.include_transactions') ? 'checked' : '' }} class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <label for="include_transactions" class="ml-2 block text-sm text-gray-700">{{ __('Include detailed transaction list') }}</label>
                                    </div>
                                    
                                    <!-- Include Charts -->
                                    <div class="flex items-center">
                                        <input type="checkbox" id="include_charts" name="filters[include_charts]" value="1" {{ old('filters.include_charts') ? 'checked' : '' }} class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <label for="include_charts" class="ml-2 block text-sm text-gray-700">{{ __('Include charts and graphs') }}</label>
                                    </div>
                                    
                                    <!-- Include Comparisons -->
                                    <div class="flex items-center">
                                        <input type="checkbox" id="include_comparisons" name="filters[include_comparisons]" value="1" {{ old('filters.include_comparisons') ? 'checked' : '' }} class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <label for="include_comparisons" class="ml-2 block text-sm text-gray-700">{{ __('Include period comparisons') }}</label>
                                    </div>
                                </div>
                            </div>

                            <!-- Output Formats -->
                            <div class="mb-6">
                                <h4 class="text-sm font-medium text-gray-900 mb-4">{{ __('Output Formats') }}</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="flex items-center">
                                        <input type="checkbox" id="generate_pdf" name="filters[generate_pdf]" value="1" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <label for="generate_pdf" class="ml-2 block text-sm text-gray-700">{{ __('Generate PDF') }}</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="checkbox" id="generate_excel" name="filters[generate_excel]" value="1" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <label for="generate_excel" class="ml-2 block text-sm text-gray-700">{{ __('Generate Excel') }}</label>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex justify-end">
                                <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-chart-bar mr-2"></i>{{ __('Generate Report') }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Report Information -->
                <div class="bg-white shadow rounded-lg mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                            <i class="fas fa-info-circle mr-2 text-blue-600"></i>
                            {{ __('Report Information') }}
                        </h3>
                    </div>
                    <div class="px-6 py-6">
                        <div class="space-y-4">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">{{ __('Church') }}</h4>
                                <p class="text-sm text-gray-600 mt-1">{{ $church->name }}</p>
                                <p class="text-sm text-gray-500">{{ $church->level->value }} Level</p>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">{{ __('Report Types') }}</h4>
                                <ul class="text-sm text-gray-600 mt-1 space-y-1">
                                    <li>• {{ __('Monthly: Current month data') }}</li>
                                    <li>• {{ __('Quarterly: 3-month periods') }}</li>
                                    <li>• {{ __('Annual: Full year analysis') }}</li>
                                    <li>• {{ __('Custom: Your date range') }}</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">{{ __('Processing Time') }}</h4>
                                <p class="text-sm text-gray-600 mt-1">{{ __('Reports typically take 1-5 minutes to generate depending on data volume.') }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Reports -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                            <i class="fas fa-bolt mr-2 text-blue-600"></i>
                            {{ __('Quick Reports') }}
                        </h3>
                    </div>
                    <div class="px-6 py-6">
                        <div class="space-y-3">
                            <form method="POST" action="{{ route('financial-reports.quick-monthly') }}">
                                @csrf
                                <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                    <i class="fas fa-calendar-alt mr-2"></i>{{ __('This Month') }}
                                </button>
                            </form>
                            <form method="POST" action="{{ route('financial-reports.quick-quarterly') }}">
                                @csrf
                                <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                    <i class="fas fa-calendar mr-2"></i>{{ __('This Quarter') }}
                                </button>
                            </form>
                            <form method="POST" action="{{ route('financial-reports.quick-annual') }}">
                                @csrf
                                <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                    <i class="fas fa-calendar-year mr-2"></i>{{ __('This Year') }}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Auto-populate dates based on report type
document.getElementById('report_type').addEventListener('change', function() {
    const reportType = this.value;
    const startDate = document.getElementById('period_start');
    const endDate = document.getElementById('period_end');
    const today = new Date();
    
    if (reportType === 'monthly') {
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        startDate.value = firstDay.toISOString().split('T')[0];
        endDate.value = lastDay.toISOString().split('T')[0];
    } else if (reportType === 'quarterly') {
        const quarter = Math.floor(today.getMonth() / 3);
        const firstDay = new Date(today.getFullYear(), quarter * 3, 1);
        const lastDay = new Date(today.getFullYear(), quarter * 3 + 3, 0);
        startDate.value = firstDay.toISOString().split('T')[0];
        endDate.value = lastDay.toISOString().split('T')[0];
    } else if (reportType === 'annual') {
        const firstDay = new Date(today.getFullYear(), 0, 1);
        const lastDay = new Date(today.getFullYear(), 11, 31);
        startDate.value = firstDay.toISOString().split('T')[0];
        endDate.value = lastDay.toISOString().split('T')[0];
    }
});

// Form validation
document.getElementById('reportForm').addEventListener('submit', function(e) {
    const startDate = new Date(document.getElementById('period_start').value);
    const endDate = new Date(document.getElementById('period_end').value);
    
    if (startDate >= endDate) {
        e.preventDefault();
        alert('{{ __("End date must be after start date.") }}');
    }
});
</script>
@endpush
@endsection
