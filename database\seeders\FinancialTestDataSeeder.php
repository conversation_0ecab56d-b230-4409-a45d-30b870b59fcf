<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Church;
use App\Models\User;
use App\Models\FinancialBalance;
use App\Models\Contribution;
use App\Models\Transaction;
use App\Enums\ChurchLevel;
use App\Enums\ContributionStatus;
use App\Enums\TransactionStatus;
use App\Enums\TransactionType;
use App\Enums\PaymentMethod;

class FinancialTestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Update churches with financial information
        $this->updateChurchesWithFinancialInfo();
        
        // Create financial balances for all churches
        $this->createFinancialBalances();
        
        // Create sample contributions
        $this->createSampleContributions();
        
        // Create sample transactions
        $this->createSampleTransactions();
    }

    /**
     * Update churches with financial information
     */
    private function updateChurchesWithFinancialInfo(): void
    {
        $churches = Church::all();
        
        foreach ($churches as $church) {
            $church->update([
                'bank_name' => $this->getRandomBankName(),
                'bank_account_number' => $this->generateAccountNumber(),
                'bank_account_name' => $church->name . ' Account',
                'mobile_money_number' => $this->generatePhoneNumber(),
                'mobile_money_provider' => $this->getRandomMobileProvider(),
                'current_balance' => rand(100000, 5000000), // Random balance between 100K and 5M TZS
            ]);
        }
    }

    /**
     * Create financial balances for all churches
     */
    private function createFinancialBalances(): void
    {
        $churches = Church::all();
        
        foreach ($churches as $church) {
            $availableBalance = rand(50000, 2000000); // 50K to 2M TZS
            $pendingBalance = rand(0, 100000); // 0 to 100K TZS
            $reservedBalance = rand(0, 50000); // 0 to 50K TZS
            
            FinancialBalance::create([
                'church_id' => $church->id,
                'available_balance' => $availableBalance,
                'pending_balance' => $pendingBalance,
                'reserved_balance' => $reservedBalance,
                'total_received' => $availableBalance + rand(100000, 1000000),
                'total_sent' => rand(50000, 500000),
                'currency' => 'TZS',
                'last_updated_at' => now(),
            ]);
        }
    }

    /**
     * Create sample contributions
     */
    private function createSampleContributions(): void
    {
        $nationalChurch = Church::where('level', ChurchLevel::NATIONAL)->first();
        $dioceseChurches = Church::where('level', ChurchLevel::DIOCESE)->get();
        
        if (!$nationalChurch) {
            return;
        }

        $nationalUser = $nationalChurch->users()->first();
        if (!$nationalUser) {
            return;
        }

        // National level contributions
        $contributions = [
            [
                'name' => 'Church Building Fund 2025',
                'description' => 'Fundraising for new church buildings across Tanzania',
                'target_amount' => 50000000, // 50M TZS
                'type' => 'project',
                'collection_scope' => ChurchLevel::REGIONAL->value,
                'is_mandatory' => false,
            ],
            [
                'name' => 'Emergency Relief Fund',
                'description' => 'Emergency fund for disaster relief and community support',
                'target_amount' => 20000000, // 20M TZS
                'type' => 'emergency',
                'collection_scope' => ChurchLevel::LOCAL->value,
                'is_mandatory' => true,
            ],
            [
                'name' => 'Youth Ministry Development',
                'description' => 'Supporting youth programs and activities nationwide',
                'target_amount' => 15000000, // 15M TZS
                'type' => 'special',
                'collection_scope' => ChurchLevel::PARISH->value,
                'is_mandatory' => false,
            ],
        ];

        foreach ($contributions as $contributionData) {
            Contribution::create([
                'name' => $contributionData['name'],
                'description' => $contributionData['description'],
                'created_by_church_id' => $nationalChurch->id,
                'created_by_user_id' => $nationalUser->id,
                'target_amount' => $contributionData['target_amount'],
                'collected_amount' => rand(0, $contributionData['target_amount'] * 0.3), // 0-30% collected
                'start_date' => now()->subDays(rand(1, 30)),
                'end_date' => now()->addDays(rand(30, 180)),
                'status' => ContributionStatus::ACTIVE,
                'type' => $contributionData['type'],
                'collection_scope' => $contributionData['collection_scope'],
                'is_mandatory' => $contributionData['is_mandatory'],
                'instructions' => 'Please contribute according to your church capacity.',
            ]);
        }

        // Diocese level contributions
        foreach ($dioceseChurches->take(2) as $dioceseChurch) {
            $dioceseUser = $dioceseChurch->users()->first();
            if (!$dioceseUser) {
                continue;
            }

            Contribution::create([
                'name' => 'Diocese Development Fund - ' . $dioceseChurch->name,
                'description' => 'Development fund for churches in ' . $dioceseChurch->name . ' diocese',
                'created_by_church_id' => $dioceseChurch->id,
                'created_by_user_id' => $dioceseUser->id,
                'target_amount' => 10000000, // 10M TZS
                'collected_amount' => rand(0, 3000000), // 0-3M collected
                'start_date' => now()->subDays(rand(1, 15)),
                'end_date' => now()->addDays(rand(60, 120)),
                'status' => ContributionStatus::ACTIVE,
                'type' => 'general',
                'collection_scope' => ChurchLevel::LOCAL->value,
                'is_mandatory' => false,
                'instructions' => 'Monthly contribution for diocese development.',
            ]);
        }
    }

    /**
     * Create sample transactions
     */
    private function createSampleTransactions(): void
    {
        $churches = Church::all();
        $contributions = Contribution::all();
        
        // Create various types of transactions
        for ($i = 0; $i < 20; $i++) {
            $fromChurch = $churches->random();
            $toChurch = $fromChurch->getRevenueTargetChurch();
            
            if (!$toChurch) {
                continue;
            }

            $initiatedBy = $fromChurch->users()->first();
            if (!$initiatedBy) {
                continue;
            }

            $amount = rand(10000, 500000); // 10K to 500K TZS
            $contribution = $contributions->random();
            
            Transaction::create([
                'transaction_id' => Transaction::generateTransactionId(),
                'reference_number' => Transaction::generateReferenceNumber(),
                'from_church_id' => $fromChurch->id,
                'to_church_id' => $toChurch->id,
                'initiated_by_user_id' => $initiatedBy->id,
                'approved_by_user_id' => $toChurch->users()->first()?->id,
                'amount' => $amount,
                'currency' => 'TZS',
                'type' => rand(0, 1) ? TransactionType::REVENUE_COLLECTION : TransactionType::CONTRIBUTION,
                'status' => $this->getRandomTransactionStatus(),
                'description' => $this->getRandomTransactionDescription(),
                'contribution_id' => rand(0, 1) ? $contribution->id : null,
                'payment_method' => $this->getRandomPaymentMethod(),
                'payment_details' => $this->getRandomPaymentDetails(),
                'initiated_at' => now()->subDays(rand(0, 30)),
                'approved_at' => rand(0, 1) ? now()->subDays(rand(0, 25)) : null,
                'completed_at' => rand(0, 1) ? now()->subDays(rand(0, 20)) : null,
            ]);
        }
    }

    /**
     * Get random bank name
     */
    private function getRandomBankName(): string
    {
        $banks = ['CRDB', 'NMB', 'NBC', 'Stanbic', 'Equity', 'DTB', 'BOA'];
        return $banks[array_rand($banks)];
    }

    /**
     * Generate random account number
     */
    private function generateAccountNumber(): string
    {
        return str_pad(rand(**********, **********), 10, '0', STR_PAD_LEFT);
    }

    /**
     * Generate random phone number
     */
    private function generatePhoneNumber(): string
    {
        $prefixes = ['255754', '255755', '255756', '255757', '255678', '255679'];
        return $prefixes[array_rand($prefixes)] . str_pad(rand(100000, 999999), 6, '0', STR_PAD_LEFT);
    }

    /**
     * Get random mobile provider
     */
    private function getRandomMobileProvider(): string
    {
        $providers = ['Vodacom M-Pesa', 'Airtel Money', 'Tigo Pesa', 'Halotel'];
        return $providers[array_rand($providers)];
    }

    /**
     * Get random transaction status
     */
    private function getRandomTransactionStatus(): TransactionStatus
    {
        $statuses = [
            TransactionStatus::COMPLETED,
            TransactionStatus::COMPLETED,
            TransactionStatus::COMPLETED, // More completed transactions
            TransactionStatus::PENDING,
            TransactionStatus::PROCESSING,
        ];
        return $statuses[array_rand($statuses)];
    }

    /**
     * Get random payment method
     */
    private function getRandomPaymentMethod(): PaymentMethod
    {
        $methods = [PaymentMethod::MOBILE_MONEY, PaymentMethod::BANK_TRANSFER, PaymentMethod::CASH];
        return $methods[array_rand($methods)];
    }

    /**
     * Get random payment details
     */
    private function getRandomPaymentDetails(): array
    {
        return [
            'provider' => $this->getRandomMobileProvider(),
            'phone_number' => $this->generatePhoneNumber(),
            'bank_name' => $this->getRandomBankName(),
            'account_number' => $this->generateAccountNumber(),
        ];
    }

    /**
     * Get random transaction description
     */
    private function getRandomTransactionDescription(): string
    {
        $descriptions = [
            'Monthly revenue collection',
            'Quarterly contribution payment',
            'Special offering for church development',
            'Emergency fund contribution',
            'Youth ministry support',
            'Building fund payment',
            'General church revenue',
            'Diocese development contribution',
        ];
        return $descriptions[array_rand($descriptions)];
    }
}
