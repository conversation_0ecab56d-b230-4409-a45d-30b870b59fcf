<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Transaction;
use App\Models\Church;
use App\Models\Contribution;
use App\Services\RevenueService;
use App\Services\ChurchHierarchyService;
use App\Enums\TransactionStatus;
use App\Enums\TransactionType;
use App\Enums\PaymentMethod;

class TransactionController extends Controller
{
    protected RevenueService $revenueService;
    protected ChurchHierarchyService $hierarchyService;

    public function __construct(RevenueService $revenueService, ChurchHierarchyService $hierarchyService)
    {
        $this->revenueService = $revenueService;
        $this->hierarchyService = $hierarchyService;
        $this->middleware('auth');
        $this->middleware('permission:view-transactions', ['only' => ['index', 'show']]);
        $this->middleware('permission:create-transactions', ['only' => ['create', 'store']]);
        $this->middleware('permission:approve-transactions', ['only' => ['approve', 'reject']]);
    }

    /**
     * Display a listing of transactions
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $church = $user->church;

        $query = Transaction::forChurch($church->id)
            ->with(['fromChurch', 'toChurch', 'initiatedByUser', 'approvedByUser', 'contribution']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('transaction_id', 'like', "%{$search}%")
                  ->orWhere('reference_number', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $transactions = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('financial.transactions.index', compact('transactions', 'church'));
    }

    /**
     * Show the form for creating a new transaction
     */
    public function create(Request $request)
    {
        $user = Auth::user();
        $church = $user->church;

        // Get target church (where revenue will be sent)
        $targetChurch = $church->getRevenueTargetChurch();
        
        if (!$targetChurch) {
            return redirect()->back()->with('error', 'No target church found for revenue collection.');
        }

        // Get available contributions
        $contributions = Contribution::where('collection_scope', $church->level->value)
            ->active()
            ->currentlyActive()
            ->get();

        // Get payment methods available for target church
        $paymentMethods = $targetChurch->getPaymentMethods();

        return view('financial.transactions.create', compact(
            'church',
            'targetChurch',
            'contributions',
            'paymentMethods'
        ));
    }

    /**
     * Store a newly created transaction
     */
    public function store(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:1',
            'payment_method' => 'required|in:bank_transfer,mobile_money,cash',
            'description' => 'nullable|string|max:500',
            'contribution_id' => 'nullable|exists:contributions,id',
            'payment_details' => 'required|array',
        ]);

        $user = Auth::user();
        $church = $user->church;
        $targetChurch = $church->getRevenueTargetChurch();

        if (!$targetChurch) {
            return redirect()->back()->with('error', 'No target church found for revenue collection.');
        }

        $contribution = null;
        if ($request->contribution_id) {
            $contribution = Contribution::findOrFail($request->contribution_id);
        }

        $paymentMethod = PaymentMethod::from($request->payment_method);
        
        $result = $this->revenueService->initiateRevenueCollection(
            $church,
            $targetChurch,
            $user,
            $request->amount,
            $paymentMethod,
            $request->payment_details,
            $request->description,
            $contribution
        );

        if ($result['success']) {
            return redirect()->route('transactions.show', $result['transaction'])
                ->with('success', 'Transaction initiated successfully.');
        }

        return redirect()->back()
            ->withInput()
            ->with('error', $result['message']);
    }

    /**
     * Display the specified transaction
     */
    public function show(Transaction $transaction)
    {
        $user = Auth::user();
        
        // Check if user can view this transaction
        if (!$this->canViewTransaction($user, $transaction)) {
            abort(403, 'Unauthorized to view this transaction.');
        }

        $transaction->load([
            'fromChurch',
            'toChurch',
            'initiatedByUser',
            'approvedByUser',
            'contribution',
            'receipt'
        ]);

        return view('financial.transactions.show', compact('transaction'));
    }

    /**
     * Approve a pending transaction
     */
    public function approve(Request $request, Transaction $transaction)
    {
        $user = Auth::user();

        if (!$transaction->canBeApproved()) {
            return redirect()->back()->with('error', 'Transaction cannot be approved in its current state.');
        }

        if ($transaction->to_church_id !== $user->church_id) {
            abort(403, 'Unauthorized to approve this transaction.');
        }

        $success = $transaction->approve($user);

        if ($success) {
            return redirect()->back()->with('success', 'Transaction approved successfully.');
        }

        return redirect()->back()->with('error', 'Failed to approve transaction.');
    }

    /**
     * Reject a pending transaction
     */
    public function reject(Request $request, Transaction $transaction)
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        $user = Auth::user();

        if (!$transaction->canBeCancelled()) {
            return redirect()->back()->with('error', 'Transaction cannot be rejected in its current state.');
        }

        if ($transaction->to_church_id !== $user->church_id) {
            abort(403, 'Unauthorized to reject this transaction.');
        }

        $success = $transaction->fail($request->reason);

        if ($success) {
            return redirect()->back()->with('success', 'Transaction rejected successfully.');
        }

        return redirect()->back()->with('error', 'Failed to reject transaction.');
    }

    /**
     * Cancel a transaction
     */
    public function cancel(Transaction $transaction)
    {
        $user = Auth::user();

        if (!$transaction->canBeCancelled()) {
            return redirect()->back()->with('error', 'Transaction cannot be cancelled in its current state.');
        }

        if ($transaction->initiated_by_user_id !== $user->id) {
            abort(403, 'Unauthorized to cancel this transaction.');
        }

        $transaction->update([
            'status' => TransactionStatus::CANCELLED,
            'notes' => ($transaction->notes ?? '') . "\nCancelled by user on " . now()->format('Y-m-d H:i:s'),
        ]);

        return redirect()->back()->with('success', 'Transaction cancelled successfully.');
    }

    /**
     * Check if user can view a transaction
     */
    protected function canViewTransaction($user, Transaction $transaction): bool
    {
        $userChurchId = $user->church_id;
        
        return $transaction->from_church_id === $userChurchId ||
               $transaction->to_church_id === $userChurchId ||
               $this->hierarchyService->getChurchesUserCanManage($user)->contains('id', $transaction->from_church_id) ||
               $this->hierarchyService->getChurchesUserCanManage($user)->contains('id', $transaction->to_church_id);
    }

    /**
     * Get transaction statistics for API
     */
    public function getStatistics(Request $request)
    {
        $user = Auth::user();
        $church = $user->church;
        $period = $request->get('period', 'month');

        $statistics = $this->revenueService->getRevenueStatistics($church, $period);

        return response()->json($statistics);
    }
}
