<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Models\FinancialReport;
use App\Services\FinancialReportService;
use Carbon\Carbon;

class FinancialReportController extends Controller
{
    protected FinancialReportService $reportService;

    public function __construct(FinancialReportService $reportService)
    {
        $this->reportService = $reportService;
        $this->middleware('auth');
        $this->middleware('permission:view-financial-reports', ['only' => ['index', 'show']]);
        $this->middleware('permission:generate-financial-reports', ['only' => ['create', 'store']]);
        $this->middleware('permission:export-financial-reports', ['only' => ['downloadPdf', 'downloadExcel']]);
    }

    /**
     * Display a listing of financial reports
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $church = $user->church;

        $query = FinancialReport::where('church_id', $church->id)
            ->with(['generatedByUser']);

        // Apply filters
        if ($request->filled('report_type')) {
            $query->where('report_type', $request->report_type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $reports = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('financial.reports.index', compact('reports', 'church'));
    }

    /**
     * Show the form for creating a new report
     */
    public function create()
    {
        $user = Auth::user();
        $church = $user->church;

        $reportTypes = [
            'monthly' => 'Monthly Report',
            'quarterly' => 'Quarterly Report',
            'annual' => 'Annual Report',
            'contribution_summary' => 'Contribution Summary',
            'custom' => 'Custom Report',
        ];

        return view('financial.reports.create', compact('church', 'reportTypes'));
    }

    /**
     * Store a newly created report
     */
    public function store(Request $request)
    {
        $request->validate([
            'report_type' => 'required|in:monthly,quarterly,annual,contribution_summary,custom',
            'period_start' => 'required|date',
            'period_end' => 'required|date|after_or_equal:period_start',
            'filters' => 'nullable|array',
        ]);

        $user = Auth::user();
        $church = $user->church;

        $periodStart = Carbon::parse($request->period_start);
        $periodEnd = Carbon::parse($request->period_end);
        $filters = $request->filters ?? [];

        try {
            $report = $this->reportService->generateReport(
                $church,
                $user,
                $request->report_type,
                $periodStart,
                $periodEnd,
                $filters
            );

            return redirect()->route('financial-reports.show', $report)
                ->with('success', 'Financial report generated successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to generate report: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified report
     */
    public function show(FinancialReport $financialReport)
    {
        $user = Auth::user();

        if ($financialReport->church_id !== $user->church_id) {
            abort(403, 'Unauthorized to view this report.');
        }

        $financialReport->load(['church', 'generatedByUser']);

        return view('financial.reports.show', compact('financialReport'));
    }

    /**
     * Download report as PDF
     */
    public function downloadPdf(FinancialReport $financialReport)
    {
        $user = Auth::user();

        if ($financialReport->church_id !== $user->church_id) {
            abort(403, 'Unauthorized to download this report.');
        }

        if (!$financialReport->pdf_path || !Storage::disk('local')->exists($financialReport->pdf_path)) {
            return redirect()->back()->with('error', 'PDF file not available for this report.');
        }

        $filename = $financialReport->report_name . '.pdf';
        
        return Storage::disk('local')->download($financialReport->pdf_path, $filename);
    }

    /**
     * Download report as Excel
     */
    public function downloadExcel(FinancialReport $financialReport)
    {
        $user = Auth::user();

        if ($financialReport->church_id !== $user->church_id) {
            abort(403, 'Unauthorized to download this report.');
        }

        if (!$financialReport->excel_path || !Storage::disk('local')->exists($financialReport->excel_path)) {
            return redirect()->back()->with('error', 'Excel file not available for this report.');
        }

        $filename = $financialReport->report_name . '.xlsx';
        
        return Storage::disk('local')->download($financialReport->excel_path, $filename);
    }

    /**
     * Delete a report
     */
    public function destroy(FinancialReport $financialReport)
    {
        $user = Auth::user();

        if ($financialReport->church_id !== $user->church_id) {
            abort(403, 'Unauthorized to delete this report.');
        }

        // Delete associated files
        if ($financialReport->pdf_path && Storage::disk('local')->exists($financialReport->pdf_path)) {
            Storage::disk('local')->delete($financialReport->pdf_path);
        }

        if ($financialReport->excel_path && Storage::disk('local')->exists($financialReport->excel_path)) {
            Storage::disk('local')->delete($financialReport->excel_path);
        }

        $financialReport->delete();

        return redirect()->route('financial-reports.index')
            ->with('success', 'Report deleted successfully.');
    }

    /**
     * Generate quick monthly report
     */
    public function quickMonthly(Request $request)
    {
        $user = Auth::user();
        $church = $user->church;

        $month = $request->get('month', now()->format('Y-m'));
        $date = Carbon::createFromFormat('Y-m', $month);
        
        $periodStart = $date->copy()->startOfMonth();
        $periodEnd = $date->copy()->endOfMonth();

        try {
            $report = $this->reportService->generateReport(
                $church,
                $user,
                'monthly',
                $periodStart,
                $periodEnd
            );

            return redirect()->route('financial-reports.show', $report)
                ->with('success', 'Monthly report generated successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to generate monthly report: ' . $e->getMessage());
        }
    }

    /**
     * Generate quick quarterly report
     */
    public function quickQuarterly(Request $request)
    {
        $user = Auth::user();
        $church = $user->church;

        $quarter = $request->get('quarter', now()->quarter);
        $year = $request->get('year', now()->year);
        
        $periodStart = Carbon::createFromDate($year, ($quarter - 1) * 3 + 1, 1)->startOfQuarter();
        $periodEnd = $periodStart->copy()->endOfQuarter();

        try {
            $report = $this->reportService->generateReport(
                $church,
                $user,
                'quarterly',
                $periodStart,
                $periodEnd
            );

            return redirect()->route('financial-reports.show', $report)
                ->with('success', 'Quarterly report generated successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to generate quarterly report: ' . $e->getMessage());
        }
    }

    /**
     * Generate quick annual report
     */
    public function quickAnnual(Request $request)
    {
        $user = Auth::user();
        $church = $user->church;

        $year = $request->get('year', now()->year);
        
        $periodStart = Carbon::createFromDate($year, 1, 1)->startOfYear();
        $periodEnd = $periodStart->copy()->endOfYear();

        try {
            $report = $this->reportService->generateReport(
                $church,
                $user,
                'annual',
                $periodStart,
                $periodEnd
            );

            return redirect()->route('financial-reports.show', $report)
                ->with('success', 'Annual report generated successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to generate annual report: ' . $e->getMessage());
        }
    }

    /**
     * Get report data for API
     */
    public function getReportData(FinancialReport $financialReport)
    {
        $user = Auth::user();

        if ($financialReport->church_id !== $user->church_id) {
            abort(403, 'Unauthorized to access this report data.');
        }

        return response()->json([
            'report' => $financialReport,
            'data' => $financialReport->report_data,
        ]);
    }
}
