@extends('layouts.app')

@section('title', __('permissions.edit_permission'))
@section('page-title', __('permissions.edit_permission'))

@section('breadcrumbs')
    <li>
        <span class="mx-2">/</span>
        <a href="{{ route('permissions.index') }}" class="hover:text-gray-700">{{ __('permissions.permissions') }}</a>
    </li>
    <li>
        <span class="mx-2">/</span>
        <a href="{{ route('permissions.show', $permission) }}" class="hover:text-gray-700">{{ $permission->name }}</a>
    </li>
    <li>
        <span class="mx-2">/</span>
        <span class="font-medium text-gray-900">{{ __('common.edit') }}</span>
    </li>
@endsection

@section('page-actions')
    <div class="flex items-center space-x-3">
        <!-- Language Switcher -->
        <x-language-switcher position="bottom-right" size="normal" />
        
        <a href="{{ route('permissions.show', $permission) }}"
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <i class="fas fa-eye mr-2"></i>
            {{ __('common.view') }}
        </a>
        <a href="{{ route('permissions.index') }}"
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <i class="fas fa-arrow-left mr-2"></i>
            {{ __('common.back') }}
        </a>
    </div>
@endsection

@section('content')
    <div class="max-w-4xl mx-auto space-y-6">
        <!-- Quick Language Switcher -->
        <div class="flex justify-end">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-2">
                <div class="flex items-center space-x-2 text-sm">
                    <i class="fas fa-globe text-gray-400"></i>
                    <span class="text-gray-600">{{ __('common.language') }}:</span>
                    <div class="flex space-x-1">
                        @foreach(config('app.locale_names') as $locale => $info)
                            <button onclick="switchLanguage('{{ $locale }}')"
                                    class="px-2 py-1 rounded text-xs font-medium transition-colors duration-200 {{ app()->getLocale() === $locale ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100' }}">
                                {{ $info['flag'] }} {{ $info['native'] }}
                            </button>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <!-- Permission Header -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="bg-gradient-to-r from-purple-600 to-purple-700 px-6 py-4">
                <div class="flex items-center space-x-4">
                    <div class="flex-shrink-0">
                        <div class="h-16 w-16 bg-purple-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-key text-white text-2xl"></i>
                        </div>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-white">{{ __('permissions.editing_permission') }}: {{ $permission->name }}</h1>
                        <p class="text-purple-100">{{ __('permissions.guard') }}: {{ $permission->guard_name ?? __('common.default') }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-edit mr-2 text-blue-600"></i>
                    {{ __('permissions.permission_information') }}
                </h2>
            </div>

            <form method="POST" action="{{ route('permissions.update', $permission) }}" class="p-6">
                @csrf
                @method('PUT')

                <div class="space-y-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ __('permissions.permission_name') }} <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-key text-gray-400"></i>
                            </div>
                            <input type="text"
                                   id="name"
                                   name="name"
                                   value="{{ old('name', $permission->name) }}"
                                   required
                                   class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200 @error('name') border-red-300 @enderror"
                                   placeholder="{{ __('permissions.enter_permission_name') }}">
                        </div>
                        @error('name')
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <i class="fas fa-exclamation-circle mr-1"></i>
                                {{ $message }}
                            </p>
                        @enderror
                        <p class="mt-2 text-sm text-gray-500">
                            {{ __('permissions.permission_name_help') }}
                        </p>
                    </div>

                    <div>
                        <label for="guard_name" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ __('permissions.guard_name') }}
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-shield-alt text-gray-400"></i>
                            </div>
                            <select name="guard_name"
                                    id="guard_name"
                                    class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200 @error('guard_name') border-red-300 @enderror">
                                <option value="web" {{ old('guard_name', $permission->guard_name) === 'web' ? 'selected' : '' }}>
                                    {{ __('permissions.web_guard') }}
                                </option>
                                <option value="api" {{ old('guard_name', $permission->guard_name) === 'api' ? 'selected' : '' }}>
                                    {{ __('permissions.api_guard') }}
                                </option>
                            </select>
                        </div>
                        @error('guard_name')
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <i class="fas fa-exclamation-circle mr-1"></i>
                                {{ $message }}
                            </p>
                        @enderror
                        <p class="mt-2 text-sm text-gray-500">
                            {{ __('permissions.guard_name_help') }}
                        </p>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="mt-8 pt-6 border-t border-gray-200">
                    <div class="flex justify-end space-x-4">
                        <a href="{{ route('permissions.show', $permission) }}"
                           class="inline-flex items-center px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200">
                            <i class="fas fa-times mr-2"></i>
                            {{ __('common.cancel') }}
                        </a>
                        <button type="submit"
                                class="inline-flex items-center px-6 py-3 border border-transparent rounded-lg text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200">
                            <i class="fas fa-save mr-2"></i>
                            {{ __('permissions.update_permission') }}
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection
