<?php

namespace App\Traits;

use Illuminate\Support\Str;

trait HasCustomId
{
    /**
     * Boot the trait
     */
    protected static function bootHasCustomId()
    {
        static::creating(function ($model) {
            if (empty($model->custom_id)) {
                $model->custom_id = $model->generateCustomId();
            }
        });
    }

    /**
     * Generate a custom ID with the model's prefix
     */
    public function generateCustomId(): string
    {
        $prefix = $this->getCustomIdPrefix();
        $timestamp = now()->format('ymd');
        $random = strtoupper(Str::random(6));
        
        do {
            $customId = "{$prefix}-{$timestamp}-{$random}";
            $random = strtoupper(Str::random(6)); // Generate new random for next iteration if needed
        } while (static::where('custom_id', $customId)->exists());
        
        return $customId;
    }

    /**
     * Get the custom ID prefix for this model
     * This should be overridden in each model
     */
    protected function getCustomIdPrefix(): string
    {
        return 'GEN'; // Generic prefix, should be overridden
    }

    /**
     * Get route key name for route model binding
     */
    public function getRouteKeyName()
    {
        return 'custom_id';
    }

    /**
     * Scope to find by custom ID
     */
    public function scopeByCustomId($query, string $customId)
    {
        return $query->where('custom_id', $customId);
    }

    /**
     * Find model by custom ID
     */
    public static function findByCustomId(string $customId)
    {
        return static::where('custom_id', $customId)->first();
    }

    /**
     * Find model by custom ID or fail
     */
    public static function findByCustomIdOrFail(string $customId)
    {
        return static::where('custom_id', $customId)->firstOrFail();
    }
}
