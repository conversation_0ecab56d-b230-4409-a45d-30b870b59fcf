<?php

namespace App\Exports;

use App\Models\FinancialReport;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;

class FinancialReportExport implements WithMultipleSheets
{
    protected FinancialReport $report;

    public function __construct(FinancialReport $report)
    {
        $this->report = $report;
    }

    public function sheets(): array
    {
        $sheets = [];
        
        // Summary sheet
        $sheets[] = new FinancialReportSummarySheet($this->report);
        
        // Transactions sheet (if available)
        if (isset($this->report->report_data['transactions'])) {
            $sheets[] = new FinancialReportTransactionsSheet($this->report);
        }
        
        // Balance history sheet (if available)
        if (isset($this->report->report_data['balance_history'])) {
            $sheets[] = new FinancialReportBalanceHistorySheet($this->report);
        }
        
        return $sheets;
    }
}

class FinancialReportSummarySheet implements FromArray, WithHeadings, WithStyles, WithTitle
{
    protected FinancialReport $report;

    public function __construct(FinancialReport $report)
    {
        $this->report = $report;
    }

    public function array(): array
    {
        $data = [];
        $summary = $this->report->report_data['summary'] ?? [];
        
        // Report information
        $data[] = ['Report Information', ''];
        $data[] = ['Report Name', $this->report->report_name];
        $data[] = ['Report Type', ucfirst($this->report->report_type)];
        $data[] = ['Church', $this->report->church->name];
        $data[] = ['Period', $this->report->period_start->format('Y-m-d') . ' to ' . $this->report->period_end->format('Y-m-d')];
        $data[] = ['Generated By', $this->report->generatedByUser->full_name];
        $data[] = ['Generated At', $this->report->created_at->format('Y-m-d H:i:s')];
        $data[] = ['', ''];
        
        // Financial summary
        $data[] = ['Financial Summary', ''];
        $data[] = ['Incoming Revenue', number_format($summary['incoming_revenue'] ?? 0, 2) . ' TZS'];
        $data[] = ['Outgoing Revenue', number_format($summary['outgoing_revenue'] ?? 0, 2) . ' TZS'];
        $data[] = ['Net Revenue', number_format($summary['net_revenue'] ?? 0, 2) . ' TZS'];
        $data[] = ['Total Transactions', $summary['total_transactions'] ?? 0];
        $data[] = ['Current Balance', number_format($summary['current_balance'] ?? 0, 2) . ' TZS'];
        $data[] = ['Total Balance', number_format($summary['total_balance'] ?? 0, 2) . ' TZS'];
        
        return $data;
    }

    public function headings(): array
    {
        return ['Item', 'Value'];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true, 'size' => 14]],
            'A:A' => ['font' => ['bold' => true]],
            'A1:B1' => [
                'font' => ['bold' => true, 'size' => 16],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
            ],
        ];
    }

    public function title(): string
    {
        return 'Summary';
    }
}

class FinancialReportTransactionsSheet implements FromArray, WithHeadings, WithStyles, WithTitle
{
    protected FinancialReport $report;

    public function __construct(FinancialReport $report)
    {
        $this->report = $report;
    }

    public function array(): array
    {
        $transactions = $this->report->report_data['transactions'] ?? [];
        $data = [];
        
        foreach ($transactions as $transaction) {
            $data[] = [
                $transaction['transaction_id'] ?? '',
                $transaction['date'] ?? '',
                $transaction['from_church'] ?? '',
                $transaction['to_church'] ?? '',
                number_format($transaction['amount'] ?? 0, 2),
                $transaction['type'] ?? '',
                $transaction['status'] ?? '',
                $transaction['description'] ?? '',
                $transaction['contribution'] ?? '',
            ];
        }
        
        return $data;
    }

    public function headings(): array
    {
        return [
            'Transaction ID',
            'Date',
            'From Church',
            'To Church',
            'Amount (TZS)',
            'Type',
            'Status',
            'Description',
            'Contribution'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
            'E:E' => ['alignment' => ['horizontal' => Alignment::HORIZONTAL_RIGHT]],
        ];
    }

    public function title(): string
    {
        return 'Transactions';
    }
}

class FinancialReportBalanceHistorySheet implements FromArray, WithHeadings, WithStyles, WithTitle
{
    protected FinancialReport $report;

    public function __construct(FinancialReport $report)
    {
        $this->report = $report;
    }

    public function array(): array
    {
        $balanceHistory = $this->report->report_data['balance_history'] ?? [];
        $data = [];
        
        foreach ($balanceHistory as $entry) {
            $data[] = [
                $entry['date'] ?? '',
                number_format($entry['balance'] ?? 0, 2),
                number_format($entry['incoming'] ?? 0, 2),
                number_format($entry['outgoing'] ?? 0, 2),
                number_format($entry['net_change'] ?? 0, 2),
            ];
        }
        
        return $data;
    }

    public function headings(): array
    {
        return [
            'Date',
            'Balance (TZS)',
            'Incoming (TZS)',
            'Outgoing (TZS)',
            'Net Change (TZS)'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
            'B:E' => ['alignment' => ['horizontal' => Alignment::HORIZONTAL_RIGHT]],
        ];
    }

    public function title(): string
    {
        return 'Balance History';
    }
}
