@extends('layouts.app')

@section('title', 'Create Role')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Create New Role</h1>
                    <p class="mt-2 text-sm text-gray-600">Define a new role with specific permissions</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="{{ route('roles.index') }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Roles
                    </a>
                </div>
            </div>
        </div>

        <form method="POST" action="{{ route('roles.store') }}" class="space-y-8">
            @csrf
            
            <!-- Role Information -->
            <div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-user-shield mr-3 text-blue-600"></i>
                        Role Information
                    </h3>
                    <p class="mt-1 text-sm text-gray-600">Basic details about the role</p>
                </div>
                
                <div class="p-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            Role Name <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="text" 
                                   id="name"
                                   name="name" 
                                   value="{{ old('name') }}" 
                                   required
                                   class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 @error('name') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror"
                                   placeholder="Enter role name (e.g., Regional Manager)">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i class="fas fa-tag text-gray-400"></i>
                            </div>
                        </div>
                        @error('name') 
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <i class="fas fa-exclamation-circle mr-1"></i>
                                {{ $message }}
                            </p>
                        @enderror
                        <p class="mt-2 text-sm text-gray-600">Choose a descriptive name that clearly identifies the role's purpose</p>
                    </div>
                </div>
            </div>

            <!-- Permissions Selection -->
            <div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden" x-data="{ selectAll: false, selectedPermissions: [] }">
                <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                <i class="fas fa-key mr-3 text-green-600"></i>
                                Permissions
                            </h3>
                            <p class="mt-1 text-sm text-gray-600">Select the permissions for this role</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <label class="inline-flex items-center">
                                <input type="checkbox" 
                                       x-model="selectAll"
                                       @change="selectAll ? selectedPermissions = {{ $permissions->pluck('name')->toJson() }} : selectedPermissions = []"
                                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">Select All</span>
                            </label>
                            <span class="text-sm text-gray-500" x-text="`${selectedPermissions.length} selected`"></span>
                        </div>
                    </div>
                </div>
                
                <div class="p-6">
                    @if(isset($groupedPermissions) && count($groupedPermissions) > 0)
                        <div class="space-y-6">
                            @foreach($groupedPermissions as $resource => $resourcePermissions)
                                <div class="border border-gray-200 rounded-lg overflow-hidden">
                                    <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                                        <h4 class="text-sm font-medium text-gray-900 capitalize">
                                            <i class="fas fa-{{ $resource === 'users' ? 'users' : ($resource === 'churches' ? 'church' : ($resource === 'messages' ? 'envelope' : ($resource === 'requests' ? 'clipboard-list' : 'cog'))) }} mr-2 text-gray-600"></i>
                                            {{ ucfirst($resource) }} Permissions
                                        </h4>
                                    </div>
                                    <div class="p-4">
                                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                            @foreach($resourcePermissions as $permission)
                                                <label class="inline-flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors duration-200">
                                                    <input type="checkbox" 
                                                           name="permissions[]" 
                                                           value="{{ $permission->name }}"
                                                           x-model="selectedPermissions"
                                                           {{ in_array($permission->name, old('permissions', [])) ? 'checked' : '' }}
                                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                                    <span class="ml-3 text-sm text-gray-700">{{ $permission->name }}</span>
                                                </label>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            @foreach($permissions as $permission)
                                <label class="inline-flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors duration-200">
                                    <input type="checkbox" 
                                           name="permissions[]" 
                                           value="{{ $permission->name }}"
                                           x-model="selectedPermissions"
                                           {{ in_array($permission->name, old('permissions', [])) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <span class="ml-3 text-sm text-gray-700">{{ $permission->name }}</span>
                                </label>
                            @endforeach
                        </div>
                    @endif
                    
                    @error('permissions') 
                        <p class="mt-4 text-sm text-red-600 flex items-center">
                            <i class="fas fa-exclamation-circle mr-1"></i>
                            {{ $message }}
                        </p>
                    @enderror
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-between pt-6">
                <div class="flex items-center text-sm text-gray-500">
                    <i class="fas fa-info-circle mr-2"></i>
                    Fields marked with <span class="text-red-500 font-medium">*</span> are required
                </div>
                
                <div class="flex items-center space-x-4">
                    <a href="{{ route('roles.index') }}" 
                       class="inline-flex items-center px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <i class="fas fa-times mr-2"></i>
                        Cancel
                    </a>
                    <button type="submit" 
                            class="inline-flex items-center px-8 py-3 border border-transparent rounded-lg text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5">
                        <i class="fas fa-save mr-2"></i>
                        Create Role
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection
