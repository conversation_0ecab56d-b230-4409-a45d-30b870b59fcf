<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Church;
use App\Models\Transaction;
use App\Models\Contribution;
use App\Models\FinancialBalance;
use App\Services\RevenueService;
use App\Services\ChurchHierarchyService;
use App\Enums\TransactionStatus;
use App\Enums\TransactionType;
use Carbon\Carbon;

class FinancialDashboardController extends Controller
{
    protected RevenueService $revenueService;
    protected ChurchHierarchyService $hierarchyService;

    public function __construct(RevenueService $revenueService, ChurchHierarchyService $hierarchyService)
    {
        $this->revenueService = $revenueService;
        $this->hierarchyService = $hierarchyService;
        $this->middleware('auth');
        $this->middleware('permission:view-financial-dashboard');
    }

    /**
     * Display the financial dashboard
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $church = $user->church;
        
        // Get period from request (default to current month)
        $period = $request->get('period', 'month');
        
        // Get financial statistics
        $statistics = $this->getFinancialStatistics($church, $period);
        
        // Get recent transactions
        $recentTransactions = $this->getRecentTransactions($church, 10);
        
        // Get pending transactions requiring approval
        $pendingTransactions = $this->getPendingTransactions($church);
        
        // Get active contributions
        $activeContributions = $this->getActiveContributions($church);
        
        // Get financial balance
        $financialBalance = FinancialBalance::getOrCreateForChurch($church->id);
        
        // Get chart data
        $chartData = $this->getChartData($church, $period);
        
        return view('financial.dashboard', compact(
            'church',
            'statistics',
            'recentTransactions',
            'pendingTransactions',
            'activeContributions',
            'financialBalance',
            'chartData',
            'period'
        ));
    }

    /**
     * Get financial statistics for a church
     */
    protected function getFinancialStatistics(Church $church, string $period): array
    {
        $startDate = $this->getPeriodStartDate($period);
        $endDate = now();

        // Use hierarchical filtering for treasurer roles
        $baseQuery = Transaction::forTreasurerRole($church)
            ->where('status', TransactionStatus::COMPLETED)
            ->whereBetween('completed_at', [$startDate, $endDate]);

        $incomingRevenue = (clone $baseQuery)
            ->where('to_church_id', $church->id)
            ->where('type', TransactionType::REVENUE_COLLECTION)
            ->sum('amount');

        $outgoingRevenue = (clone $baseQuery)
            ->where('from_church_id', $church->id)
            ->where('type', TransactionType::REVENUE_COLLECTION)
            ->sum('amount');

        $contributionPayments = (clone $baseQuery)
            ->where('from_church_id', $church->id)
            ->where('type', TransactionType::CONTRIBUTION)
            ->sum('amount');

        $contributionReceived = (clone $baseQuery)
            ->where('to_church_id', $church->id)
            ->where('type', TransactionType::CONTRIBUTION)
            ->sum('amount');

        $totalTransactions = (clone $baseQuery)->count();

        // Get previous period for comparison
        $previousStartDate = $this->getPreviousPeriodStartDate($period, $startDate);
        $previousBaseQuery = Transaction::forTreasurerRole($church)
            ->where('status', TransactionStatus::COMPLETED)
            ->whereBetween('completed_at', [$previousStartDate, $startDate]);

        $previousIncoming = (clone $previousBaseQuery)
            ->where('to_church_id', $church->id)
            ->sum('amount');

        $previousOutgoing = (clone $previousBaseQuery)
            ->where('from_church_id', $church->id)
            ->sum('amount');

        return [
            'period' => $period,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'incoming_revenue' => $incomingRevenue,
            'outgoing_revenue' => $outgoingRevenue,
            'contribution_payments' => $contributionPayments,
            'contribution_received' => $contributionReceived,
            'net_revenue' => $incomingRevenue - $outgoingRevenue,
            'total_transactions' => $totalTransactions,
            'growth_rates' => [
                'incoming' => $this->calculateGrowthRate($previousIncoming, $incomingRevenue),
                'outgoing' => $this->calculateGrowthRate($previousOutgoing, $outgoingRevenue),
            ],
        ];
    }

    /**
     * Get recent transactions for a church
     */
    protected function getRecentTransactions(Church $church, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return Transaction::forTreasurerRole($church)
            ->with(['fromChurch', 'toChurch', 'initiatedByUser', 'contribution'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get pending transactions requiring approval
     */
    protected function getPendingTransactions(Church $church): \Illuminate\Database\Eloquent\Collection
    {
        // For pending transactions, only show those coming TO this church from their hierarchy
        $descendantIds = $church->getAllDescendants()->pluck('id')->toArray();

        return Transaction::where('to_church_id', $church->id)
            ->whereIn('from_church_id', array_merge([$church->id], $descendantIds))
            ->where('status', TransactionStatus::PENDING)
            ->with(['fromChurch', 'initiatedByUser', 'contribution'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get active contributions for a church
     */
    protected function getActiveContributions(Church $church): \Illuminate\Database\Eloquent\Collection
    {
        // Show contributions created by this church and its descendants
        $descendantIds = $church->getAllDescendants()->pluck('id')->toArray();
        $churchIds = array_merge([$church->id], $descendantIds);

        return Contribution::whereIn('created_by_church_id', $churchIds)
            ->active()
            ->with(['createdByUser'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get chart data for financial dashboard
     */
    protected function getChartData(Church $church, string $period): array
    {
        $days = match ($period) {
            'week' => 7,
            'month' => 30,
            'quarter' => 90,
            'year' => 365,
            default => 30,
        };

        $chartData = [];
        $startDate = now()->subDays($days);

        for ($i = 0; $i < $days; $i++) {
            $date = $startDate->copy()->addDays($i);

            $incoming = Transaction::forTreasurerRole($church)
                ->where('to_church_id', $church->id)
                ->where('status', TransactionStatus::COMPLETED)
                ->whereDate('completed_at', $date)
                ->sum('amount');

            $outgoing = Transaction::forTreasurerRole($church)
                ->where('from_church_id', $church->id)
                ->where('status', TransactionStatus::COMPLETED)
                ->whereDate('completed_at', $date)
                ->sum('amount');

            $chartData[] = [
                'date' => $date->format('Y-m-d'),
                'incoming' => $incoming,
                'outgoing' => $outgoing,
                'net' => $incoming - $outgoing,
            ];
        }

        return $chartData;
    }

    /**
     * Get period start date
     */
    protected function getPeriodStartDate(string $period): Carbon
    {
        return match ($period) {
            'week' => now()->startOfWeek(),
            'month' => now()->startOfMonth(),
            'quarter' => now()->startOfQuarter(),
            'year' => now()->startOfYear(),
            default => now()->startOfMonth(),
        };
    }

    /**
     * Get previous period start date
     */
    protected function getPreviousPeriodStartDate(string $period, Carbon $currentStart): Carbon
    {
        return match ($period) {
            'week' => $currentStart->copy()->subWeek(),
            'month' => $currentStart->copy()->subMonth(),
            'quarter' => $currentStart->copy()->subQuarter(),
            'year' => $currentStart->copy()->subYear(),
            default => $currentStart->copy()->subMonth(),
        };
    }

    /**
     * Calculate growth rate percentage
     */
    protected function calculateGrowthRate(float $previous, float $current): float
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return (($current - $previous) / $previous) * 100;
    }

    /**
     * Get financial summary for API
     */
    public function getSummary(Request $request)
    {
        $user = Auth::user();
        $church = $user->church;
        $period = $request->get('period', 'month');

        $statistics = $this->getFinancialStatistics($church, $period);
        $balance = FinancialBalance::getOrCreateForChurch($church->id);

        return response()->json([
            'statistics' => $statistics,
            'balance' => [
                'available' => $balance->available_balance,
                'pending' => $balance->pending_balance,
                'reserved' => $balance->reserved_balance,
                'total' => $balance->getTotalBalance(),
                'status' => $balance->getBalanceStatus(),
            ],
        ]);
    }
}
