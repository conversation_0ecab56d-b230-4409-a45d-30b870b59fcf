<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt {{ $receipt->receipt_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            color: white;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .header p {
            margin: 5px 0 0 0;
            font-size: 14px;
            opacity: 0.9;
        }
        .receipt-number {
            background: rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 8px;
            display: inline-block;
            margin-top: 15px;
        }
        .receipt-number strong {
            font-size: 18px;
        }
        .content {
            max-width: 800px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 30px;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #2563eb;
        }
        .section h3 {
            margin: 0 0 15px 0;
            color: #2563eb;
            font-size: 18px;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 8px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .info-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }
        .info-item label {
            font-weight: bold;
            color: #374151;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: block;
            margin-bottom: 5px;
        }
        .info-item .value {
            font-size: 14px;
            color: #111827;
        }
        .amount-section {
            background: #dcfce7;
            border: 2px solid #16a34a;
            padding: 25px;
            text-align: center;
            border-radius: 10px;
            margin: 30px 0;
        }
        .amount-section .amount {
            font-size: 36px;
            font-weight: bold;
            color: #15803d;
            margin: 10px 0;
        }
        .amount-section .currency {
            font-size: 14px;
            color: #166534;
            text-transform: uppercase;
        }
        .church-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .church-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 2px solid #e5e7eb;
        }
        .church-card.from {
            border-color: #ef4444;
        }
        .church-card.to {
            border-color: #16a34a;
        }
        .church-card h4 {
            margin: 0 0 15px 0;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .church-card.from h4 {
            color: #dc2626;
        }
        .church-card.to h4 {
            color: #16a34a;
        }
        .church-name {
            font-size: 18px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 5px;
        }
        .church-details {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.4;
        }
        .payment-details {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            overflow: hidden;
        }
        .payment-details table {
            width: 100%;
            border-collapse: collapse;
        }
        .payment-details th,
        .payment-details td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #f3f4f6;
        }
        .payment-details th {
            background: #f9fafb;
            font-weight: bold;
            color: #374151;
            font-size: 12px;
            text-transform: uppercase;
        }
        .payment-details td {
            color: #111827;
            font-size: 14px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #e5e7eb;
            text-align: center;
            color: #6b7280;
            font-size: 12px;
        }
        .signature-section {
            margin-top: 40px;
            text-align: right;
        }
        .signature-line {
            border-top: 2px solid #374151;
            width: 200px;
            margin: 30px 0 10px auto;
            padding-top: 10px;
        }
        .notes {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
        }
        .notes h4 {
            margin: 0 0 10px 0;
            color: #92400e;
            font-size: 14px;
        }
        .notes ul {
            margin: 0;
            padding-left: 20px;
            color: #92400e;
            font-size: 12px;
        }
        .notes li {
            margin-bottom: 5px;
        }
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            body {
                margin: 0;
                padding: 15px;
                font-size: 12px;
                background: white !important;
                color: black !important;
            }

            /* Ensure text is visible */
            p, div, span, td, th, h1, h2, h3, h4, h5, h6 {
                color: inherit !important;
            }

            .content {
                max-width: none;
                width: 100%;
                margin: 0;
                padding: 0;
            }

            .header {
                background: #2563eb !important;
                color: white !important;
                padding: 20px;
                margin-bottom: 20px;
                border-radius: 0;
                page-break-inside: avoid;
            }

            .header h1 {
                font-size: 24px;
                margin: 0;
                color: white !important;
            }

            .header p {
                font-size: 12px;
                margin: 5px 0;
                color: white !important;
            }

            .receipt-number {
                background: rgba(255, 255, 255, 0.2) !important;
                color: white !important;
                padding: 8px 15px;
                border-radius: 4px;
                margin-top: 10px;
            }

            .receipt-number strong {
                color: white !important;
            }

            .section {
                break-inside: avoid;
                page-break-inside: avoid;
                margin-bottom: 20px;
                background: #f8f9fa !important;
                padding: 15px;
                border: 1px solid #dee2e6;
            }

            .church-info {
                display: flex;
                justify-content: space-between;
                margin-bottom: 20px;
                page-break-inside: avoid;
            }

            .church-card {
                width: 48%;
                background: #f8f9fa !important;
                border: 1px solid #dee2e6;
                padding: 15px;
            }

            .transaction-details {
                background: white !important;
                border: 2px solid #2563eb;
                padding: 15px;
                margin: 20px 0;
                page-break-inside: avoid;
            }

            .amount {
                font-size: 18px !important;
                font-weight: bold;
                color: #2563eb !important;
            }

            .footer {
                margin-top: 30px;
                padding-top: 20px;
                border-top: 2px solid #dee2e6;
                page-break-inside: avoid;
            }

            .signatures {
                display: flex;
                justify-content: space-between;
                margin-top: 40px;
            }

            .signature-box {
                width: 45%;
                text-align: center;
                border-top: 1px solid #333;
                padding-top: 10px;
            }

            /* Hide elements that shouldn't print */
            .no-print {
                display: none !important;
            }

            /* Ensure tables print properly */
            table {
                width: 100%;
                border-collapse: collapse;
            }

            th, td {
                border: 1px solid #dee2e6;
                padding: 8px;
                text-align: left;
            }

            th {
                background: #f8f9fa !important;
                font-weight: bold;
            }
        }
    </style>
</head>
<body>
    <div class="content">
        <!-- Header -->
        <div class="header">
            <h1>OFFICIAL RECEIPT</h1>
            <p>Free Pentecostal Church of Tanzania</p>
            <div class="receipt-number">
                <strong>{{ $receipt->receipt_number }}</strong>
            </div>
        </div>

        <!-- Church Information -->
        <div class="church-info">
            <div class="church-card from">
                <h4>Issued To</h4>
                <div class="church-name">{{ $receipt->issuedToChurch->name }}</div>
                <div class="church-details">
                    {{ $receipt->issuedToChurch->level->value }} Level<br>
                    {{ $receipt->issuedToChurch->location }}<br>
                    @if($receipt->issuedToChurch->contact_email)
                        {{ $receipt->issuedToChurch->contact_email }}<br>
                    @endif
                    @if($receipt->issuedToChurch->contact_phone)
                        {{ $receipt->issuedToChurch->contact_phone }}
                    @endif
                </div>
            </div>
            <div class="church-card to">
                <h4>Issued By</h4>
                <div class="church-name">{{ $receipt->issuedByChurch->name }}</div>
                <div class="church-details">
                    {{ $receipt->issuedByChurch->level->value }} Level<br>
                    {{ $receipt->issuedByChurch->location }}<br>
                    <strong>Authorized by:</strong> {{ $receipt->issuedByUser->full_name }}<br>
                    <strong>Date:</strong> {{ $receipt->created_at->format('M j, Y H:i') }}
                </div>
            </div>
        </div>

        <!-- Amount Section -->
        <div class="amount-section">
            <div class="currency">Amount Received</div>
            <div class="amount">{{ number_format($receipt->amount, 2) }} {{ $receipt->currency }}</div>
            <div class="currency">Payment Status: Completed</div>
        </div>

        <!-- Transaction Details -->
        @if($transaction)
        <div class="section">
            <h3>Transaction Details</h3>
            <div class="info-grid">
                <div class="info-item">
                    <label>Transaction ID</label>
                    <div class="value">{{ $transaction->transaction_id }}</div>
                </div>
                <div class="info-item">
                    <label>Transaction Date</label>
                    <div class="value">{{ $transaction->created_at->format('M j, Y H:i') }}</div>
                </div>
                <div class="info-item">
                    <label>Payment Method</label>
                    <div class="value">{{ $transaction->payment_method->getLabel() }}</div>
                </div>
                <div class="info-item">
                    <label>Reference Number</label>
                    <div class="value">{{ $transaction->reference_number }}</div>
                </div>
            </div>
            
            @if($transaction->description)
            <div class="info-item">
                <label>Description</label>
                <div class="value">{{ $transaction->description }}</div>
            </div>
            @endif

            @if($transaction->contribution)
            <div class="info-item">
                <label>Contribution Campaign</label>
                <div class="value">
                    {{ $transaction->contribution->name }}<br>
                    <small>{{ $transaction->contribution->type }} - {{ $transaction->contribution->status->getLabel() }}</small>
                </div>
            </div>
            @endif
        </div>
        @endif

        <!-- Payment Details -->
        @if($transaction && $transaction->payment_details)
        <div class="section">
            <h3>Payment Details</h3>
            <div class="payment-details">
                <table>
                    <thead>
                        <tr>
                            <th>Detail</th>
                            <th>Value</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($transaction->payment_details as $key => $value)
                        <tr>
                            <td>{{ ucfirst(str_replace('_', ' ', $key)) }}</td>
                            <td>{{ $value }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
        @endif

        <!-- Additional Information -->
        @if($receipt->description)
        <div class="section">
            <h3>Additional Information</h3>
            <div class="info-item">
                <div class="value">{{ $receipt->description }}</div>
            </div>
        </div>
        @endif

        <!-- Important Notes -->
        <div class="notes">
            <h4>Important Notes</h4>
            <ul>
                <li>This is an official receipt issued by the Free Pentecostal Church of Tanzania</li>
                <li>Keep this receipt for your records and tax purposes</li>
                <li>For any inquiries regarding this receipt, contact the issuing church</li>
                <li>This receipt confirms the successful completion of the transaction</li>
            </ul>
        </div>

        <!-- Signature Section -->
        <div class="signature-section">
            <div class="signature-line">
                <strong>{{ $receipt->issuedByUser->full_name }}</strong><br>
                {{ $receipt->issuedByUser->role }}<br>
                {{ $receipt->issuedByChurch->name }}
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>Generated on {{ $receipt->created_at->format('M j, Y H:i') }} | Receipt ID: {{ $receipt->custom_id }}</p>
            <p>Free Pentecostal Church of Tanzania - Official Receipt</p>
        </div>
    </div>
</body>
</html>
