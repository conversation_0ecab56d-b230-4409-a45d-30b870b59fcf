<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('financial_balances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('church_id')->constrained('churches')->onDelete('cascade');
            $table->decimal('available_balance', 15, 2)->default(0.00);
            $table->decimal('pending_balance', 15, 2)->default(0.00); // Pending transactions
            $table->decimal('reserved_balance', 15, 2)->default(0.00); // Reserved for specific purposes
            $table->decimal('total_received', 15, 2)->default(0.00); // Lifetime received
            $table->decimal('total_sent', 15, 2)->default(0.00); // Lifetime sent
            $table->string('currency', 3)->default('TZS');
            $table->timestamp('last_updated_at');
            $table->timestamps();

            $table->unique('church_id');
            $table->index(['church_id', 'available_balance']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('financial_balances');
    }
};
