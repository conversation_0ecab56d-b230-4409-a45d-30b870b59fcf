<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;

class TestRolesCommand extends Command
{
    protected $signature = 'test:roles';
    protected $description = 'Test roles availability';

    public function handle()
    {
        $this->info('Testing roles...');
        
        $roles = Role::all();
        
        $this->info("Total roles found: " . $roles->count());
        
        if ($roles->count() > 0) {
            $this->info("Role names:");
            foreach ($roles as $role) {
                $this->line("- {$role->name}");
            }
        } else {
            $this->error("No roles found in database!");
        }
        
        return 0;
    }
}
