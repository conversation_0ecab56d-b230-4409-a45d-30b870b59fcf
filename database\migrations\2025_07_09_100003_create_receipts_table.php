<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('receipts', function (Blueprint $table) {
            $table->id();
            $table->string('receipt_number')->unique();
            $table->foreignId('transaction_id')->constrained('transactions')->onDelete('cascade');
            $table->foreignId('issued_to_church_id')->constrained('churches')->onDelete('cascade');
            $table->foreignId('issued_by_church_id')->constrained('churches')->onDelete('cascade');
            $table->foreignId('issued_by_user_id')->constrained('users')->onDelete('cascade');
            
            $table->decimal('amount', 15, 2);
            $table->string('currency', 3)->default('TZS');
            $table->text('description');
            $table->string('receipt_type'); // payment, contribution, transfer
            
            // File storage
            $table->string('pdf_path')->nullable();
            $table->boolean('is_emailed')->default(false);
            $table->timestamp('emailed_at')->nullable();
            
            $table->timestamps();

            $table->index(['transaction_id']);
            $table->index(['issued_to_church_id']);
            $table->index(['receipt_number']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('receipts');
    }
};
