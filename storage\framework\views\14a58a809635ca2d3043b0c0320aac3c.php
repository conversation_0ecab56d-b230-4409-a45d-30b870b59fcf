

<?php $__env->startSection('title', __('users.user_details')); ?>
<?php $__env->startSection('page-title', __('users.user_details')); ?>

<?php $__env->startSection('breadcrumbs'); ?>
    <li>
        <span class="mx-2">/</span>
        <a href="<?php echo e(route('users.index')); ?>" class="hover:text-gray-700"><?php echo e(__('users.users')); ?></a>
    </li>
    <li>
        <span class="mx-2">/</span>
        <span class="font-medium text-gray-900"><?php echo e($user->full_name); ?></span>
    </li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-actions'); ?>
    <div class="flex items-center space-x-3">
        <!-- Language Switcher -->
        <?php if (isset($component)) { $__componentOriginal8d3bff7d7383a45350f7495fc470d934 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8d3bff7d7383a45350f7495fc470d934 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.language-switcher','data' => ['position' => 'bottom-right','size' => 'normal']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('language-switcher'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['position' => 'bottom-right','size' => 'normal']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8d3bff7d7383a45350f7495fc470d934)): ?>
<?php $attributes = $__attributesOriginal8d3bff7d7383a45350f7495fc470d934; ?>
<?php unset($__attributesOriginal8d3bff7d7383a45350f7495fc470d934); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8d3bff7d7383a45350f7495fc470d934)): ?>
<?php $component = $__componentOriginal8d3bff7d7383a45350f7495fc470d934; ?>
<?php unset($__componentOriginal8d3bff7d7383a45350f7495fc470d934); ?>
<?php endif; ?>

        <a href="<?php echo e(route('users.edit', $user)); ?>"
           class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <i class="fas fa-edit mr-2"></i>
            <?php echo e(__('common.edit')); ?>

        </a>
        <a href="<?php echo e(route('users.index')); ?>"
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <i class="fas fa-arrow-left mr-2"></i>
            <?php echo e(__('common.back')); ?>

        </a>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="max-w-6xl mx-auto space-y-6">
        <!-- Quick Language Switcher -->
        <div class="flex justify-end">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-2">
                <div class="flex items-center space-x-2 text-sm">
                    <i class="fas fa-globe text-gray-400"></i>
                    <span class="text-gray-600"><?php echo e(__('common.language')); ?>:</span>
                    <div class="flex space-x-1">
                        <?php $__currentLoopData = config('app.locale_names'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $locale => $info): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <button onclick="switchLanguage('<?php echo e($locale); ?>')"
                                    class="px-2 py-1 rounded text-xs font-medium transition-colors duration-200 <?php echo e(app()->getLocale() === $locale ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'); ?>">
                                <?php echo e($info['flag']); ?> <?php echo e($info['native']); ?>

                            </button>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
        <!-- Profile Header -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-8">
                <div class="flex items-center space-x-6">
                    <!-- Profile Picture -->
                    <div class="flex-shrink-0">
                        <?php if($user->profile_picture): ?>
                            <img class="h-24 w-24 rounded-full border-4 border-white shadow-lg object-cover"
                                 src="<?php echo e(asset('storage/' . $user->profile_picture)); ?>"
                                 alt="<?php echo e($user->full_name); ?>">
                        <?php else: ?>
                            <div class="h-24 w-24 rounded-full border-4 border-white shadow-lg bg-gray-300 flex items-center justify-center">
                                <span class="text-2xl font-bold text-gray-600">
                                    <?php echo e(strtoupper(substr($user->full_name, 0, 1))); ?>

                                </span>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Profile Info -->
                    <div class="flex-1 min-w-0">
                        <h1 class="text-2xl font-bold text-white truncate"><?php echo e($user->full_name); ?></h1>
                        <p class="text-blue-100 text-lg"><?php echo e($user->email); ?></p>
                        <div class="flex items-center mt-2 space-x-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <?php echo e($user->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                <i class="fas fa-circle text-xs mr-2"></i>
                                <?php echo e($user->is_active ? __('common.active') : __('common.inactive')); ?>

                            </span>
                            <?php if($user->is_first_login): ?>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-exclamation-triangle text-xs mr-2"></i>
                                    <?php echo e(__('users.first_login_pending')); ?>

                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-church text-blue-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600"><?php echo e(__('users.church')); ?></p>
                        <p class="text-lg font-semibold text-gray-900"><?php echo e($user->church->name ?? __('common.not_assigned')); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-user-tag text-green-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600"><?php echo e(__('users.role')); ?></p>
                        <p class="text-lg font-semibold text-gray-900"><?php echo e($user->role); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-envelope text-purple-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600"><?php echo e(__('common.unread_messages')); ?></p>
                        <p class="text-lg font-semibold text-gray-900"><?php echo e($user->unread_messages_count ?? 0); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-clock text-orange-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600"><?php echo e(__('common.pending_approvals')); ?></p>
                        <p class="text-lg font-semibold text-gray-900"><?php echo e($user->pending_approvals_count ?? 0); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Information -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Personal Information -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-user mr-2 text-blue-600"></i>
                        <?php echo e(__('users.personal_information')); ?>

                    </h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-600"><?php echo e(__('users.full_name')); ?></label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo e($user->full_name); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600"><?php echo e(__('users.email')); ?></label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo e($user->email); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600"><?php echo e(__('users.phone_number')); ?></label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo e($user->phone_number ?? __('common.not_provided')); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600"><?php echo e(__('users.date_of_birth')); ?></label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo e($user->date_of_birth ? $user->date_of_birth->format('M d, Y') : __('common.not_provided')); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600"><?php echo e(__('users.gender')); ?></label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo e($user->gender ?? __('common.not_provided')); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600"><?php echo e(__('users.locale')); ?></label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo e($user->locale === 'sw' ? 'Kiswahili' : 'English'); ?></p>
                        </div>
                    </div>
                    <?php if($user->address): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-600"><?php echo e(__('users.address')); ?></label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo e($user->address); ?></p>
                        </div>
                    <?php endif; ?>
                    <?php if($user->bio): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-600"><?php echo e(__('users.bio')); ?></label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo e($user->bio); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Church & Role Information -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-church mr-2 text-green-600"></i>
                        <?php echo e(__('users.church_role_information')); ?>

                    </h3>
                </div>
                <div class="p-6 space-y-4">
                    <?php if($user->church): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-600"><?php echo e(__('users.church')); ?></label>
                            <div class="mt-1 flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-900"><?php echo e($user->church->name); ?></p>
                                    <p class="text-xs text-gray-500"><?php echo e(__('common.' . strtolower($user->church->level->value))); ?> - <?php echo e($user->church->location); ?></p>
                                </div>
                                <a href="<?php echo e(route('churches.show', $user->church)); ?>"
                                   class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    <?php echo e(__('common.view')); ?> →
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div>
                        <label class="block text-sm font-medium text-gray-600"><?php echo e(__('users.role')); ?></label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($user->role); ?></p>
                    </div>

                    <?php if($user->roles->count() > 0): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-600"><?php echo e(__('users.permissions')); ?></label>
                            <div class="mt-2 flex flex-wrap gap-2">
                                <?php $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <?php echo e($role->name); ?>

                                    </span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
                        <div>
                            <label class="block text-sm font-medium text-gray-600"><?php echo e(__('users.account_created')); ?></label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo e($user->created_at->format('M d, Y')); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600"><?php echo e(__('users.last_updated')); ?></label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo e($user->updated_at->format('M d, Y')); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Emergency Contact Information -->
        <?php if($user->emergency_contact_name || $user->emergency_contact_phone): ?>
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-phone-alt mr-2 text-red-600"></i>
                        <?php echo e(__('users.emergency_contact')); ?>

                    </h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <?php if($user->emergency_contact_name): ?>
                            <div>
                                <label class="block text-sm font-medium text-gray-600"><?php echo e(__('users.emergency_contact_name')); ?></label>
                                <p class="mt-1 text-sm text-gray-900"><?php echo e($user->emergency_contact_name); ?></p>
                            </div>
                        <?php endif; ?>
                        <?php if($user->emergency_contact_phone): ?>
                            <div>
                                <label class="block text-sm font-medium text-gray-600"><?php echo e(__('users.emergency_contact_phone')); ?></label>
                                <p class="mt-1 text-sm text-gray-900"><?php echo e($user->emergency_contact_phone); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\fpct-system\resources\views/users/show.blade.php ENDPATH**/ ?>