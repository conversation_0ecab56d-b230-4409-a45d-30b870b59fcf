<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Church;
use App\Models\NotificationPreference;
use App\Enums\ChurchLevel;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First, create the National Headquarters church if it doesn't exist
        $nationalChurch = Church::firstOrCreate(
            ['level' => ChurchLevel::NATIONAL],
            [
                'name' => 'FPCT National Headquarters',
                'level' => ChurchLevel::NATIONAL,
                'location' => 'Dar es Salaam, Tanzania',
                'date_established' => now()->subYears(50), // Assuming FPCT was established 50 years ago
                'parent_church_id' => null,
            ]
        );

        $this->command->info("✓ National Headquarters church created/found: {$nationalChurch->name}");

        // Create the super admin user with National IT role
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'full_name' => 'FPCT System Administrator',
                'email' => '<EMAIL>',
                'phone_number' => '+255712345678',
                'password' => Hash::make('admin123'),
                'church_id' => $nationalChurch->id,
                'role' => 'National IT',
                'is_active' => true,
                'is_first_login' => false, // Set to false so admin can login immediately
                'email_verified_at' => now(),
            ]
        );

        $this->command->info("✓ Super admin user created/found: {$superAdmin->email}");

        // Assign the National IT role and give it superadmin permissions
        $nationalITRole = Role::where('name', 'National IT')->first();
        if ($nationalITRole) {
            $superAdmin->assignRole($nationalITRole);

            // Give National IT role all permissions (superadmin access)
            $allPermissions = \Spatie\Permission\Models\Permission::all();
            $nationalITRole->syncPermissions($allPermissions);

            $this->command->info("✓ National IT role assigned to super admin with all permissions");
        } else {
            $this->command->warn("⚠ National IT role not found. Please run RolesAndPermissionsSeeder first.");
        }

        // Create notification preferences for the super admin
        NotificationPreference::firstOrCreate(
            ['user_id' => $superAdmin->id],
            NotificationPreference::getDefaultPreferences()
        );

        $this->command->info("✓ Notification preferences created for super admin");

        $this->command->info("\n🎉 Super Admin Setup Complete!");
        $this->command->info("📧 Email: <EMAIL>");
        $this->command->info("🔑 Password: admin123");
        $this->command->info("🏢 Church: {$nationalChurch->name}");
        $this->command->info("👑 Role: National IT (Superadmin - Full System Access)");
        $this->command->info("🔧 This user can create and manage all other users in the system");
        $this->command->info("\n⚠️  IMPORTANT: Change the password after first login!");
    }

}
