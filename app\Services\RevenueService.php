<?php

namespace App\Services;

use App\Models\Transaction;
use App\Models\Church;
use App\Models\User;
use App\Models\Contribution;
use App\Models\FinancialBalance;
use App\Enums\TransactionType;
use App\Enums\TransactionStatus;
use App\Enums\PaymentMethod;
use App\Services\ReceiptService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log as LaravelLog;

class RevenueService
{
    protected ?AzamPayService $azamPayService = null;
    protected ReceiptService $receiptService;

    public function __construct(ReceiptService $receiptService)
    {
        $this->receiptService = $receiptService;
    }

    /**
     * Get AzamPay service instance (lazy loading)
     */
    protected function getAzamPayService(): AzamPayService
    {
        if ($this->azamPayService === null) {
            $this->azamPayService = app(AzamPayService::class);
        }
        return $this->azamPayService;
    }

    /**
     * Initiate revenue collection from lower level to upper level
     */
    public function initiateRevenueCollection(
        Church $fromChurch,
        Church $toChurch,
        User $initiatedBy,
        float $amount,
        PaymentMethod $paymentMethod,
        array $paymentDetails,
        string $description = null,
        ?Contribution $contribution = null
    ): array {
        try {
            DB::beginTransaction();

            // Validate the revenue collection
            $validation = $this->validateRevenueCollection($fromChurch, $toChurch, $amount);
            if (!$validation['valid']) {
                return ['success' => false, 'message' => $validation['message']];
            }

            // Create transaction record
            $transaction = Transaction::create([
                'transaction_id' => Transaction::generateTransactionId(),
                'reference_number' => Transaction::generateReferenceNumber(),
                'from_church_id' => $fromChurch->id,
                'to_church_id' => $toChurch->id,
                'initiated_by_user_id' => $initiatedBy->id,
                'amount' => $amount,
                'currency' => 'TZS',
                'type' => $contribution ? TransactionType::CONTRIBUTION : TransactionType::REVENUE_COLLECTION,
                'status' => TransactionStatus::PENDING,
                'description' => $description ?? $this->generateDefaultDescription($fromChurch, $toChurch, $contribution),
                'contribution_id' => $contribution?->id,
                'payment_method' => $paymentMethod,
                'payment_details' => $paymentDetails,
                'initiated_at' => now(),
            ]);

            // Process payment based on method
            $paymentResult = $this->processPayment($transaction, $paymentMethod, $paymentDetails);

            if (!$paymentResult['success']) {
                DB::rollBack();
                return $paymentResult;
            }

            // Update transaction with payment provider details
            $transaction->update([
                'provider_transaction_id' => $paymentResult['transaction_id'] ?? null,
                'provider_response' => $paymentResult['data'] ?? null,
                'status' => $paymentMethod === PaymentMethod::CASH ? TransactionStatus::COMPLETED : TransactionStatus::PROCESSING,
            ]);

            // For cash transactions, complete immediately
            if ($paymentMethod === PaymentMethod::CASH) {
                $this->completeTransaction($transaction);
            }

            DB::commit();

            return [
                'success' => true,
                'transaction' => $transaction,
                'message' => 'Revenue collection initiated successfully',
                'payment_result' => $paymentResult
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            LaravelLog::error('Revenue collection initiation failed', [
                'from_church' => $fromChurch->id,
                'to_church' => $toChurch->id,
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Revenue collection failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Process payment based on method
     */
    protected function processPayment(Transaction $transaction, PaymentMethod $paymentMethod, array $paymentDetails): array
    {
        switch ($paymentMethod) {
            case PaymentMethod::MOBILE_MONEY:
                $azamPay = $this->getAzamPayService();
                if (!$azamPay->isConfigured()) {
                    return [
                        'success' => false,
                        'message' => 'AzamPay service not configured. Please contact administrator.'
                    ];
                }
                return $azamPay->initiateMobileMoneyPayment($transaction, $paymentDetails);

            case PaymentMethod::BANK_TRANSFER:
                $azamPay = $this->getAzamPayService();
                if (!$azamPay->isConfigured()) {
                    return [
                        'success' => false,
                        'message' => 'AzamPay service not configured. Please contact administrator.'
                    ];
                }
                return $azamPay->initiateBankTransfer($transaction, $paymentDetails);

            case PaymentMethod::CASH:
                return [
                    'success' => true,
                    'message' => 'Cash transaction recorded',
                    'transaction_id' => 'CASH-' . $transaction->transaction_id
                ];

            default:
                return ['success' => false, 'message' => 'Unsupported payment method'];
        }
    }

    /**
     * Complete a transaction and update balances
     */
    public function completeTransaction(Transaction $transaction): bool
    {
        try {
            DB::beginTransaction();

            // Update transaction status
            $transaction->update([
                'status' => TransactionStatus::COMPLETED,
                'completed_at' => now(),
            ]);

            // Update church balances
            $this->updateChurchBalances($transaction);

            // Update contribution if applicable
            if ($transaction->contribution_id) {
                $transaction->contribution->addContribution($transaction->amount);
            }

            // Generate receipt
            $this->receiptService->generateReceipt($transaction);

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            LaravelLog::error('Transaction completion failed', [
                'transaction_id' => $transaction->transaction_id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Update church financial balances
     */
    protected function updateChurchBalances(Transaction $transaction): void
    {
        // Get or create financial balances
        $fromBalance = FinancialBalance::getOrCreateForChurch($transaction->from_church_id);
        $toBalance = FinancialBalance::getOrCreateForChurch($transaction->to_church_id);

        // Validate sufficient balance before deduction
        if (!$fromBalance->deduct($transaction->amount)) {
            throw new \Exception('Insufficient balance for transaction completion. Available: ' .
                number_format((float)$fromBalance->available_balance, 2) . ' TZS, Required: ' .
                number_format((float)$transaction->amount, 2) . ' TZS');
        }

        // Add to receiver
        $toBalance->add($transaction->amount);

        LaravelLog::info('Church balances updated', [
            'transaction_id' => $transaction->transaction_id,
            'from_church' => $transaction->from_church_id,
            'to_church' => $transaction->to_church_id,
            'amount' => $transaction->amount,
            'from_balance_after' => $fromBalance->available_balance,
            'to_balance_after' => $toBalance->available_balance,
        ]);
    }

    /**
     * Validate revenue collection request
     */
    protected function validateRevenueCollection(Church $fromChurch, Church $toChurch, float $amount): array
    {
        // Check if churches are in valid hierarchy
        if (!$this->isValidHierarchy($fromChurch, $toChurch)) {
            return [
                'valid' => false,
                'message' => 'Invalid church hierarchy. Revenue can only flow upward in the hierarchy.'
            ];
        }

        // Check if amount is positive
        if ($amount <= 0) {
            return [
                'valid' => false,
                'message' => 'Amount must be greater than zero.'
            ];
        }

        // Check if from church has sufficient balance (for non-cash transactions)
        $fromBalance = FinancialBalance::getOrCreateForChurch($fromChurch->id);
        if ($fromBalance->available_balance < $amount) {
            return [
                'valid' => false,
                'message' => 'Insufficient balance for this transaction.'
            ];
        }

        // Check if target church has complete financial information
        if (!$toChurch->hasCompleteFinancialInfo()) {
            return [
                'valid' => false,
                'message' => 'Target church does not have complete financial information.'
            ];
        }

        return ['valid' => true];
    }

    /**
     * Check if revenue flow follows valid hierarchy
     */
    protected function isValidHierarchy(Church $fromChurch, Church $toChurch): bool
    {
        // Revenue flows upward: lower level to higher level
        return $fromChurch->level->getLevel() > $toChurch->level->getLevel() &&
               $fromChurch->getRevenueTargetChurch()?->id === $toChurch->id;
    }

    /**
     * Generate default transaction description
     */
    protected function generateDefaultDescription(Church $fromChurch, Church $toChurch, ?Contribution $contribution): string
    {
        if ($contribution) {
            return "Contribution payment for '{$contribution->name}' from {$fromChurch->name} to {$toChurch->name}";
        }

        return "Revenue collection from {$fromChurch->name} to {$toChurch->name}";
    }

    /**
     * Get revenue collection statistics for a church
     */
    public function getRevenueStatistics(Church $church, string $period = 'month'): array
    {
        $startDate = match ($period) {
            'week' => now()->startOfWeek(),
            'month' => now()->startOfMonth(),
            'quarter' => now()->startOfQuarter(),
            'year' => now()->startOfYear(),
            default => now()->startOfMonth(),
        };

        $endDate = now();

        $incomingRevenue = Transaction::where('to_church_id', $church->id)
            ->where('type', TransactionType::REVENUE_COLLECTION)
            ->where('status', TransactionStatus::COMPLETED)
            ->whereBetween('completed_at', [$startDate, $endDate])
            ->sum('amount');

        $outgoingRevenue = Transaction::where('from_church_id', $church->id)
            ->where('type', TransactionType::REVENUE_COLLECTION)
            ->where('status', TransactionStatus::COMPLETED)
            ->whereBetween('completed_at', [$startDate, $endDate])
            ->sum('amount');

        $contributionPayments = Transaction::where('from_church_id', $church->id)
            ->where('type', TransactionType::CONTRIBUTION)
            ->where('status', TransactionStatus::COMPLETED)
            ->whereBetween('completed_at', [$startDate, $endDate])
            ->sum('amount');

        $contributionReceived = Transaction::where('to_church_id', $church->id)
            ->where('type', TransactionType::CONTRIBUTION)
            ->where('status', TransactionStatus::COMPLETED)
            ->whereBetween('completed_at', [$startDate, $endDate])
            ->sum('amount');

        return [
            'period' => $period,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'incoming_revenue' => $incomingRevenue,
            'outgoing_revenue' => $outgoingRevenue,
            'contribution_payments' => $contributionPayments,
            'contribution_received' => $contributionReceived,
            'net_revenue' => $incomingRevenue - $outgoingRevenue,
            'total_transactions' => Transaction::forChurch($church->id)
                ->whereBetween('completed_at', [$startDate, $endDate])
                ->count(),
        ];
    }
}
