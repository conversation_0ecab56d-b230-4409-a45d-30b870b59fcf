@extends('layouts.app')

@section('title', __('Receipt Preview'))

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Success/Error Messages -->
        @if(session('success'))
            <div class="mb-6 no-print bg-green-50 border border-green-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                    </div>
                </div>
            </div>
        @endif

        @if(session('error'))
            <div class="mb-6 no-print bg-red-50 border border-red-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-circle text-red-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-red-800">{{ session('error') }}</p>
                    </div>
                </div>
            </div>
        @endif

        @if($errors->any())
            <div class="mb-6 no-print bg-red-50 border border-red-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-red-400"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">{{ __('Please correct the following errors:') }}</h3>
                        <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        @endif

        <!-- Page Header -->
        <div class="mb-8 no-print">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ __('Receipt Preview') }}</h1>
                    <p class="mt-2 text-gray-600">{{ $receipt->receipt_number }}</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('receipts.show', $receipt) }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-arrow-left mr-2"></i>{{ __('Back to Receipt') }}
                    </a>
                    @if($receipt->pdf_path)
                        <a href="{{ route('receipts.pdf', $receipt) }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            <i class="fas fa-file-pdf mr-2"></i>{{ __('Download PDF') }}
                        </a>
                    @endif
                    <button type="button" onclick="window.print()" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-print mr-2"></i>{{ __('Print') }}
                    </button>
                </div>
            </div>
        </div>

        <!-- Receipt Preview -->
        <div class="bg-white shadow-lg rounded-lg overflow-hidden" id="receipt-preview">
            <!-- Receipt Header -->
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-8 py-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-2xl font-bold">{{ __('OFFICIAL RECEIPT') }}</h2>
                        <p class="text-blue-100 mt-1">{{ __('Free Pentecostal Church of Tanzania') }}</p>
                    </div>
                    <div class="text-right">
                        <div class="bg-white bg-opacity-20 rounded-lg px-4 py-2">
                            <p class="text-sm font-medium">{{ __('Receipt No.') }}</p>
                            <p class="text-lg font-bold">{{ $receipt->receipt_number }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Receipt Body -->
            <div class="px-8 py-6">
                <!-- Church Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ __('Issued To') }}</h3>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="font-semibold text-gray-900">{{ $receipt->issuedToChurch->name }}</p>
                            <p class="text-sm text-gray-600">{{ $receipt->issuedToChurch->level->value }} Level</p>
                            <p class="text-sm text-gray-600">{{ $receipt->issuedToChurch->location }}</p>
                            @if($receipt->issuedToChurch->contact_email)
                                <p class="text-sm text-gray-600">{{ $receipt->issuedToChurch->contact_email }}</p>
                            @endif
                            @if($receipt->issuedToChurch->contact_phone)
                                <p class="text-sm text-gray-600">{{ $receipt->issuedToChurch->contact_phone }}</p>
                            @endif
                        </div>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ __('Issued By') }}</h3>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="font-semibold text-gray-900">{{ $receipt->issuedByChurch->name }}</p>
                            <p class="text-sm text-gray-600">{{ $receipt->issuedByChurch->level->value }} Level</p>
                            <p class="text-sm text-gray-600">{{ $receipt->issuedByChurch->location }}</p>
                            <p class="text-sm text-gray-600 mt-2">{{ __('Authorized by') }}: {{ $receipt->issuedByUser->full_name }}</p>
                            <p class="text-sm text-gray-500">{{ $receipt->created_at->format('M j, Y H:i') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Transaction Details -->
                <div class="border-t border-gray-200 pt-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ __('Transaction Details') }}</h3>
                    <div class="bg-gray-50 rounded-lg p-6">
                        @if($receipt->transaction)
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <p class="text-sm font-medium text-gray-700">{{ __('Transaction ID') }}</p>
                                    <p class="text-lg font-mono text-gray-900">{{ $receipt->transaction->transaction_id }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-700">{{ __('Transaction Date') }}</p>
                                    <p class="text-lg text-gray-900">{{ $receipt->transaction->created_at->format('M j, Y') }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-700">{{ __('Payment Method') }}</p>
                                    <p class="text-lg text-gray-900">{{ $receipt->transaction->payment_method->getLabel() }}</p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-700">{{ __('Reference Number') }}</p>
                                    <p class="text-lg font-mono text-gray-900">{{ $receipt->transaction->reference_number }}</p>
                                </div>
                            </div>
                            @if($receipt->transaction->description)
                                <div class="mt-4">
                                    <p class="text-sm font-medium text-gray-700">{{ __('Description') }}</p>
                                    <p class="text-gray-900">{{ $receipt->transaction->description }}</p>
                                </div>
                            @endif
                            @if($receipt->transaction->contribution)
                                <div class="mt-4">
                                    <p class="text-sm font-medium text-gray-700">{{ __('Contribution Campaign') }}</p>
                                    <p class="text-gray-900">{{ $receipt->transaction->contribution->name }}</p>
                                    <p class="text-sm text-gray-600">{{ $receipt->transaction->contribution->type }} - {{ $receipt->transaction->contribution->status->getLabel() }}</p>
                                </div>
                            @endif
                        @endif
                    </div>
                </div>

                <!-- Amount Section -->
                <div class="border-t border-gray-200 pt-6 mb-6">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-green-800">{{ __('Amount Received') }}</p>
                                <p class="text-3xl font-bold text-green-900">{{ $receipt->getFormattedAmount() }}</p>
                                <p class="text-sm text-green-700 mt-1">{{ __('Payment Status') }}: {{ __('Completed') }}</p>
                            </div>
                            <div class="text-right">
                                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-check text-green-600 text-2xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                @if($receipt->description)
                    <div class="border-t border-gray-200 pt-6 mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ __('Additional Information') }}</h3>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-gray-900">{{ $receipt->description }}</p>
                        </div>
                    </div>
                @endif

                <!-- Footer -->
                <div class="border-t border-gray-200 pt-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-2">{{ __('Important Notes') }}</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• {{ __('This is an official receipt issued by FPCT') }}</li>
                                <li>• {{ __('Keep this receipt for your records') }}</li>
                                <li>• {{ __('For inquiries, contact the issuing church') }}</li>
                                <li>• {{ __('This receipt is valid for tax purposes') }}</li>
                            </ul>
                        </div>
                        <div class="text-right">
                            <div class="border-t border-gray-300 pt-4 mt-8">
                                <p class="text-sm text-gray-600">{{ __('Authorized Signature') }}</p>
                                <p class="text-lg font-semibold text-gray-900 mt-2">{{ $receipt->issuedByUser->full_name }}</p>
                                <p class="text-sm text-gray-500">{{ $receipt->issuedByUser->role }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Receipt Footer -->
                <div class="border-t border-gray-200 pt-4 mt-6">
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <p>{{ __('Generated on') }}: {{ $receipt->created_at->format('M j, Y H:i') }}</p>
                        <p>{{ __('Receipt ID') }}: {{ $receipt->id }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="mt-8 flex justify-center space-x-4 no-print">
            <button type="button" onclick="window.print()" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-print mr-2"></i>{{ __('Print Receipt') }}
            </button>
            <a href="{{ route('receipts.pdf', $receipt) }}" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <i class="fas fa-download mr-2"></i>{{ __('Download PDF') }}
            </a>
            <button type="button" onclick="emailReceipt()" class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-envelope mr-2"></i>{{ __('Email Receipt') }}
            </button>
        </div>
    </div>
</div>

<!-- Email Receipt Modal -->
<div id="emailModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">{{ __('Email Receipt') }}</h3>
                <button type="button" onclick="closeEmailModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form method="POST" action="{{ route('receipts.email', $receipt) }}" id="emailForm">
                @csrf
                <div class="mb-4">
                    <label for="email_addresses" class="block text-sm font-medium text-gray-700 mb-2">{{ __('Email Addresses') }}</label>
                    <textarea id="email_addresses" name="email_addresses[]" rows="3" required class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="{{ __('Enter email addresses separated by commas...') }}"></textarea>
                    <p class="mt-1 text-sm text-gray-500">{{ __('Separate multiple email addresses with commas') }}</p>
                    <div id="email-error" class="hidden mt-2 text-sm text-red-600"></div>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeEmailModal()" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                        {{ __('Cancel') }}
                    </button>
                    <button type="submit" id="sendEmailBtn" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span id="sendEmailText">
                            <i class="fas fa-envelope mr-2"></i>{{ __('Send Receipt') }}
                        </span>
                        <span id="sendEmailLoading" class="hidden">
                            <i class="fas fa-spinner fa-spin mr-2"></i>{{ __('Sending...') }}
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('styles')
<style>
@media print {
    /* Force background colors and images to print */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    /* Page setup */
    @page {
        margin: 0.5in;
        size: A4;
    }

    body {
        margin: 0 !important;
        padding: 0 !important;
        background: white !important;
        font-size: 14px !important;
        color: black !important;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
        line-height: 1.5 !important;
    }

    /* Hide everything first */
    body * {
        visibility: hidden;
    }

    /* Show only the receipt and its children */
    #receipt-preview,
    #receipt-preview * {
        visibility: visible;
    }

    /* Position receipt for print */
    #receipt-preview {
        position: absolute !important;
        left: 0 !important;
        top: 0 !important;
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        box-shadow: none !important;
        border-radius: 0 !important;
        background: white !important;
        overflow: visible !important;
    }

    /* Hide non-printable elements */
    .no-print {
        display: none !important;
        visibility: hidden !important;
    }

    /* Header styling - preserve the gradient as solid blue */
    .bg-gradient-to-r {
        background: #2563eb !important;
        background-image: none !important;
        color: white !important;
        padding: 30px !important;
        margin: 0 !important;
        border-radius: 0 !important;
    }

    .bg-gradient-to-r h2,
    .bg-gradient-to-r p,
    .bg-gradient-to-r * {
        color: white !important;
    }

    .text-blue-100 {
        color: rgba(255, 255, 255, 0.9) !important;
    }

    .bg-white.bg-opacity-20 {
        background: rgba(255, 255, 255, 0.3) !important;
        color: white !important;
        padding: 10px 20px !important;
        border-radius: 8px !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
    }

    .bg-white.bg-opacity-20 p {
        color: white !important;
        margin: 0 !important;
    }

    /* Content area */
    .px-8 {
        padding-left: 30px !important;
        padding-right: 30px !important;
    }

    .py-6 {
        padding-top: 20px !important;
        padding-bottom: 20px !important;
    }

    /* Background colors for sections */
    .bg-gray-50 {
        background: #f8f9fa !important;
        padding: 20px !important;
        border-radius: 8px !important;
        border: 1px solid #e9ecef !important;
        margin: 10px 0 !important;
    }

    .bg-green-50 {
        background: #d4edda !important;
        border: 2px solid #c3e6cb !important;
        padding: 20px !important;
        border-radius: 8px !important;
        margin: 15px 0 !important;
    }

    .border-green-200 {
        border-color: #c3e6cb !important;
    }

    /* Text colors */
    .text-gray-900 {
        color: #212529 !important;
    }

    .text-gray-600 {
        color: #6c757d !important;
    }

    .text-gray-500 {
        color: #adb5bd !important;
    }

    .text-green-800 {
        color: #155724 !important;
    }

    .text-green-900 {
        color: #0f5132 !important;
    }

    .text-green-700 {
        color: #0a4622 !important;
    }

    .text-green-600 {
        color: #198754 !important;
    }

    .bg-green-100 {
        background: #c3e6cb !important;
        border-radius: 50% !important;
    }

    /* Grid layouts */
    .grid.grid-cols-1 {
        display: block !important;
    }

    .md\\:grid-cols-2 {
        display: flex !important;
        gap: 30px !important;
        justify-content: space-between !important;
    }

    .md\\:grid-cols-2 > div {
        flex: 1 !important;
        min-width: 0 !important;
    }

    /* Borders */
    .border-t {
        border-top: 2px solid #dee2e6 !important;
        padding-top: 20px !important;
        margin-top: 20px !important;
    }

    .border-gray-200 {
        border-color: #dee2e6 !important;
    }

    .border-gray-300 {
        border-color: #ced4da !important;
    }

    /* Typography */
    .font-semibold {
        font-weight: 600 !important;
    }

    .font-bold {
        font-weight: 700 !important;
    }

    .text-lg {
        font-size: 18px !important;
        line-height: 1.4 !important;
    }

    .text-xl {
        font-size: 20px !important;
        line-height: 1.4 !important;
    }

    .text-2xl {
        font-size: 24px !important;
        line-height: 1.3 !important;
    }

    .text-3xl {
        font-size: 28px !important;
        line-height: 1.2 !important;
    }

    .text-sm {
        font-size: 12px !important;
        line-height: 1.4 !important;
    }

    /* Spacing */
    .mb-4 {
        margin-bottom: 15px !important;
    }

    .mb-6 {
        margin-bottom: 20px !important;
    }

    .mb-8 {
        margin-bottom: 25px !important;
    }

    .mt-1 {
        margin-top: 5px !important;
    }

    .mt-2 {
        margin-top: 8px !important;
    }

    .mt-4 {
        margin-top: 15px !important;
    }

    .mt-6 {
        margin-top: 20px !important;
    }

    .mt-8 {
        margin-top: 25px !important;
    }

    .pt-4 {
        padding-top: 15px !important;
    }

    .pt-6 {
        padding-top: 20px !important;
    }

    .p-4 {
        padding: 15px !important;
    }

    .p-6 {
        padding: 20px !important;
    }

    /* Flexbox utilities */
    .flex {
        display: flex !important;
    }

    .items-center {
        align-items: center !important;
    }

    .justify-between {
        justify-content: space-between !important;
    }

    .text-right {
        text-align: right !important;
    }

    .text-center {
        text-align: center !important;
    }

    /* Icons */
    .fas {
        font-size: 18px !important;
    }

    /* Rounded elements */
    .rounded-lg {
        border-radius: 8px !important;
    }

    .rounded-full {
        border-radius: 50% !important;
    }

    /* Width and height */
    .w-16 {
        width: 4rem !important;
    }

    .h-16 {
        height: 4rem !important;
    }

    /* Ensure proper page breaks */
    .break-inside-avoid {
        page-break-inside: avoid !important;
    }

    /* Font mono for transaction IDs */
    .font-mono {
        font-family: 'Courier New', Courier, monospace !important;
    }

    /* List styling */
    ul {
        padding-left: 20px !important;
        margin: 10px 0 !important;
    }

    li {
        margin-bottom: 5px !important;
    }

    /* Ensure all paragraphs have proper spacing */
    p {
        margin: 5px 0 !important;
    }

    /* Hide any remaining navigation or non-content elements */
    nav, header, footer, .sidebar, .menu {
        display: none !important;
    }
}
</style>
@endpush

@push('scripts')
<script>
function emailReceipt() {
    document.getElementById('emailModal').classList.remove('hidden');
    document.getElementById('email_addresses').focus();
}

function closeEmailModal() {
    document.getElementById('emailModal').classList.add('hidden');
    // Reset form
    document.getElementById('emailForm').reset();
    document.getElementById('email-error').classList.add('hidden');
    resetEmailButton();
}

function resetEmailButton() {
    const btn = document.getElementById('sendEmailBtn');
    const textSpan = document.getElementById('sendEmailText');
    const loadingSpan = document.getElementById('sendEmailLoading');

    btn.disabled = false;
    textSpan.classList.remove('hidden');
    loadingSpan.classList.add('hidden');
}

function validateEmails(emailString) {
    const emails = emailString.split(',').map(email => email.trim()).filter(email => email.length > 0);
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const invalidEmails = [];

    for (let email of emails) {
        if (!emailRegex.test(email)) {
            invalidEmails.push(email);
        }
    }

    return {
        valid: invalidEmails.length === 0,
        invalidEmails: invalidEmails,
        validEmails: emails.filter(email => emailRegex.test(email))
    };
}

// Handle form submission
document.getElementById('emailForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const emailInput = document.getElementById('email_addresses');
    const errorDiv = document.getElementById('email-error');
    const btn = document.getElementById('sendEmailBtn');
    const textSpan = document.getElementById('sendEmailText');
    const loadingSpan = document.getElementById('sendEmailLoading');

    // Validate emails
    const validation = validateEmails(emailInput.value);

    if (!validation.valid) {
        errorDiv.textContent = 'Invalid email addresses: ' + validation.invalidEmails.join(', ');
        errorDiv.classList.remove('hidden');
        return;
    }

    if (validation.validEmails.length === 0) {
        errorDiv.textContent = 'Please enter at least one valid email address.';
        errorDiv.classList.remove('hidden');
        return;
    }

    // Hide error and show loading
    errorDiv.classList.add('hidden');
    btn.disabled = true;
    textSpan.classList.add('hidden');
    loadingSpan.classList.remove('hidden');

    // Convert emails to proper format for Laravel
    const emailsArray = validation.validEmails;

    // Create form data
    const formData = new FormData();
    formData.append('_token', document.querySelector('input[name="_token"]').value);
    emailsArray.forEach(email => {
        formData.append('email_addresses[]', email);
    });

    // Submit via fetch
    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeEmailModal();
            showNotification('Receipt emailed successfully!', 'success');
        } else {
            throw new Error(data.message || 'Failed to send email');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        errorDiv.textContent = error.message || 'Failed to send email. Please try again.';
        errorDiv.classList.remove('hidden');
        resetEmailButton();
    });
});

function showNotification(message, type = 'success') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg ${type === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`;
    notification.innerHTML = `
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas ${type === 'success' ? 'fa-check-circle text-green-400' : 'fa-exclamation-circle text-red-400'}"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium ${type === 'success' ? 'text-green-800' : 'text-red-800'}">${message}</p>
            </div>
            <div class="ml-auto pl-3">
                <button onclick="this.parentElement.parentElement.parentElement.remove()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Close modal when clicking outside
document.getElementById('emailModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeEmailModal();
    }
});

// Handle escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && !document.getElementById('emailModal').classList.contains('hidden')) {
        closeEmailModal();
    }
});
</script>
@endpush
@endsection
