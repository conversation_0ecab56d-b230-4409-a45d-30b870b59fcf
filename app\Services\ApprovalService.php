<?php

namespace App\Services;

use App\Models\Request;
use App\Models\Church;
use App\Models\User;
use App\Models\ApprovalWorkflow;
use App\Models\AuditLog;
use App\Enums\ChurchLevel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Exception;

class ApprovalService
{
    /**
     * Create an approval workflow for the given request.
     */
    public function createApprovalWorkflow(Request $request): void
    {
        DB::transaction(function () use ($request) {
            $requestingChurch = $request->church;
            $approvalChain = $this->buildApprovalChain($requestingChurch, $request->type);

            foreach ($approvalChain as $index => $step) {
                ApprovalWorkflow::create([
                    'request_id'          => $request->id,
                    'approver_church_id' => $step['church_id'],
                    'step_order'         => $index + 1,
                    'status'             => 'pending',
                    'required_level'     => $step['level'],
                ]);
            }

            AuditLog::log(
                'approval_workflow_created',
                $request,
                [],
                ['workflow_steps' => count($approvalChain)],
                "Approval workflow created for {$request->type} request"
            );
        });
    }

    /**
     * Process approval or rejection.
     */
    public function processApproval(Request $request, User $approver, bool $approved, string $comments = null): bool
    {
        return DB::transaction(function () use ($request, $approver, $approved, $comments) {
            $currentStep = $request->currentWorkflowStep();

            if (!$currentStep) {
                throw new Exception('No pending approval step found for this request.');
            }

            if (!$this->canUserApproveStep($approver, $currentStep)) {
                throw new Exception('User does not have permission to approve this step.');
            }

            $approved ? $currentStep->approve($approver, $comments) : $currentStep->reject($approver, $comments);

            if ($approved) {
                if ($request->isFullyApproved()) {
                    $this->finalizeApproval($request);
                }
            } else {
                $this->finalizeRejection($request);
            }

            AuditLog::log(
                $approved ? 'request_approved' : 'request_rejected',
                $request,
                ['status' => $request->status],
                ['status' => $approved ? 'approved' : 'rejected'],
                "Request {$approved ? 'approved' : 'rejected'} by {$approver->full_name}"
            );

            return true;
        });
    }

    /**
     * Build the approval chain based on request type and church level.
     */
    private function buildApprovalChain(Church $church, string $type): array
    {
        $chain = [];
        $requiredLevels = $this->getRequiredApprovals($church->level, $type);

        foreach ($requiredLevels as $level) {
            $approverChurch = $this->findApproverChurch($church, $level);
            if ($approverChurch) {
                $chain[] = [
                    'church_id' => $approverChurch->id,
                    'level'     => $level,
                ];
            }
        }

        return $chain;
    }

    /**
     * Get the required church levels for approval based on request type.
     */
    private function getRequiredApprovals(ChurchLevel $level, string $type): array
    {
        return match ($type) {
            'upgrade_branch_to_parish'    => [ChurchLevel::LOCAL],
            'upgrade_parish_to_local'     => [ChurchLevel::REGIONAL],
            'upgrade_local_to_regional'   => [ChurchLevel::NATIONAL],
            'create_new_church'           => $this->getCreateChurchApprovals($level),
            'transfer_leadership'         => $this->getLeadershipTransferApprovals($level),
            'budget_approval'             => $this->getBudgetApprovals($level),
            default                       => [ChurchLevel::getApprovalAuthority($level)],
        };
    }

    private function getCreateChurchApprovals(ChurchLevel $level): array
    {
        return match ($level) {
            ChurchLevel::BRANCH  => [ChurchLevel::LOCAL],
            ChurchLevel::PARISH  => [ChurchLevel::LOCAL, ChurchLevel::REGIONAL],
            ChurchLevel::LOCAL   => [ChurchLevel::REGIONAL, ChurchLevel::NATIONAL],
            ChurchLevel::REGIONAL => [ChurchLevel::NATIONAL],
            default => [],
        };
    }

    private function getLeadershipTransferApprovals(ChurchLevel $level): array
    {
        return match ($level) {
            ChurchLevel::BRANCH,
            ChurchLevel::PARISH => [ChurchLevel::LOCAL],
            ChurchLevel::LOCAL  => [ChurchLevel::REGIONAL],
            ChurchLevel::REGIONAL => [ChurchLevel::NATIONAL],
            default => [],
        };
    }

    private function getBudgetApprovals(ChurchLevel $level): array
    {
        $authority = ChurchLevel::getApprovalAuthority($level);
        return $authority ? [$authority] : [];
    }

    /**
     * Find the appropriate approver church.
     */
    private function findApproverChurch(Church $church, ChurchLevel $level): ?Church
    {
        $current = $church->parentChurch;

        while ($current) {
            if ($current->level === $level) {
                return $current;
            }
            $current = $current->parentChurch;
        }

        return null;
    }

    /**
     * Check if the user can approve the given workflow step.
     */
    private function canUserApproveStep(User $user, ApprovalWorkflow $step): bool
    {
        if ($user->church_id !== $step->approver_church_id) {
            return false;
        }

        if (!$user->hasPermissionTo('approve-requests')) {
            return false;
        }

        $validRoles = $step->required_level->getRolesByLevel();
        $userRoles = $user->getRoleNames()->toArray();

        return !empty(array_intersect($userRoles, $validRoles));
    }

    /**
     * Finalize a fully approved request.
     */
    private function finalizeApproval(Request $request): void
    {
        $request->update([
            'status'       => 'approved',
            'approved_by'  => auth()->id(),
            'approved_at'  => now(),
        ]);

        $this->executeApprovedRequest($request);
    }

    /**
     * Finalize a rejected request.
     */
    private function finalizeRejection(Request $request): void
    {
        $request->update(['status' => 'rejected']);

        $request->approvalWorkflows()
                ->where('status', 'pending')
                ->update(['status' => 'cancelled']);
    }

    /**
     * Perform logic for approved requests.
     */
    private function executeApprovedRequest(Request $request): void
    {
        match ($request->type) {
            'upgrade_branch_to_parish'   => $this->upgradeChurchLevel($request->church, ChurchLevel::PARISH),
            'upgrade_parish_to_local'    => $this->upgradeChurchLevel($request->church, ChurchLevel::LOCAL),
            'upgrade_local_to_regional'  => $this->upgradeChurchLevel($request->church, ChurchLevel::REGIONAL),
            'create_new_church'          => $this->createNewChurch($request),
            'transfer_leadership'        => $this->transferLeadership($request),
            default                      => null,
        };
    }

    /**
     * Upgrade a church level.
     */
    private function upgradeChurchLevel(Church $church, ChurchLevel $newLevel): void
    {
        $oldLevel = $church->level;

        $church->update(['level' => $newLevel]);

        AuditLog::log(
            'church_level_upgraded',
            $church,
            ['level' => $oldLevel->value],
            ['level' => $newLevel->value],
            "Church upgraded from {$oldLevel->value} to {$newLevel->value}"
        );
    }

    /**
     * Create a new church based on request data.
     */
    private function createNewChurch(Request $request): void
    {
        $details = $request->details;

        $newChurch = Church::create([
            'name'              => $details['name'],
            'level'             => $details['level'],
            'location'          => $details['location'],
            'date_established'  => $details['date_established'] ?? now(),
            'parent_church_id'  => $details['parent_church_id'],
        ]);

        AuditLog::log(
            'church_created',
            $newChurch,
            [],
            $newChurch->toArray(),
            "New {$details['level']} church created: {$details['name']}"
        );
    }

    /**
     * Transfer leadership logic.
     */
    private function transferLeadership(Request $request): void
    {
        $details = $request->details;

        // Assume logic is implemented elsewhere

        AuditLog::log(
            'leadership_transferred',
            $request->church,
            [],
            $details,
            "Leadership transferred in {$request->church->name}"
        );
    }

    /**
     * Get approval workflow history for a request.
     */
    public function getApprovalHistory(Request $request): array
    {
        return $request->approvalWorkflows()
                      ->with(['approverChurch', 'approverUser'])
                      ->orderBy('step_order')
                      ->get()
                      ->toArray();
    }

    /**
     * Get all pending approvals for a given user.
     */
    public function getPendingApprovalsForUser(User $user): Collection
    {
        return ApprovalWorkflow::where('approver_church_id', $user->church_id)
                               ->where('status', 'pending')
                               ->with(['request.church', 'request.user'])
                               ->get()
                               ->filter(fn($workflow) => $this->canUserApproveStep($user, $workflow));
    }
}
