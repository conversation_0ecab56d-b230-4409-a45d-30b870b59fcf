<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\AzamPayService;

class TestAzamPayConfig extends Command
{
    protected $signature = 'azampay:test-config';
    protected $description = 'Test AzamPay service configuration';

    public function handle()
    {
        $this->info('Testing AzamPay Configuration...');
        
        try {
            $azamPayService = app(AzamPayService::class);
            
            if ($azamPayService->isConfigured()) {
                $this->info('✓ AzamPay service is properly configured');
                return 0;
            } else {
                $this->error('✗ AzamPay service is not configured properly');
                $this->warn('Please check the following environment variables:');
                $this->warn('- AZAMPAY_APP_NAME');
                $this->warn('- AZAMPAY_CLIENT_ID');
                $this->warn('- AZAMPAY_CLIENT_SECRET');
                $this->warn('- AZAMPAY_API_KEY');
                return 1;
            }
        } catch (\Exception $e) {
            $this->error('Error testing AzamPay configuration: ' . $e->getMessage());
            return 1;
        }
    }
}
