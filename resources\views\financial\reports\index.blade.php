@extends('layouts.app')

@section('title', __('Financial Reports'))

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ __('Financial Reports') }}</h1>
                    <p class="mt-2 text-gray-600">{{ __('Generate and manage comprehensive financial reports') }}</p>
                </div>
                <div class="flex space-x-3">
                    @can('generate-financial-reports')
                        <a href="{{ route('financial-reports.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-plus mr-2"></i>{{ __('Generate Report') }}
                        </a>
                    @endcan
                    <div class="relative">
                        <button type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="toggleQuickDropdown(event)">
                            <i class="fas fa-bolt mr-2"></i>{{ __('Quick Reports') }}
                            <i class="fas fa-chevron-down ml-2"></i>
                        </button>
                        <div id="quickDropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                            <div class="py-1">
                                <form method="POST" action="{{ route('financial-reports.quick-monthly') }}" style="display: inline;">
                                    @csrf
                                    <button type="submit" class="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-calendar-alt mr-2"></i>{{ __('This Month') }}
                                    </button>
                                </form>
                                <form method="POST" action="{{ route('financial-reports.quick-quarterly') }}" style="display: inline;">
                                    @csrf
                                    <button type="submit" class="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-calendar mr-2"></i>{{ __('This Quarter') }}
                                    </button>
                                </form>
                                <form method="POST" action="{{ route('financial-reports.quick-annual') }}" style="display: inline;">
                                    @csrf
                                    <button type="submit" class="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-calendar-year mr-2"></i>{{ __('This Year') }}
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Reports -->
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-white bg-opacity-30 rounded-md flex items-center justify-center">
                                <i class="fas fa-chart-bar text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-blue-100 truncate uppercase tracking-wide">{{ __('Total Reports') }}</dt>
                                <dd class="text-lg font-medium text-white">{{ $reports->total() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Completed Reports -->
            <div class="bg-gradient-to-r from-green-500 to-green-600 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-white bg-opacity-30 rounded-md flex items-center justify-center">
                                <i class="fas fa-check-circle text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-green-100 truncate uppercase tracking-wide">{{ __('Completed') }}</dt>
                                <dd class="text-lg font-medium text-white">{{ $reports->where('status', 'completed')->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Generating Reports -->
            <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-white bg-opacity-30 rounded-md flex items-center justify-center">
                                <i class="fas fa-cog fa-spin text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-yellow-100 truncate uppercase tracking-wide">{{ __('Generating') }}</dt>
                                <dd class="text-lg font-medium text-white">{{ $reports->where('status', 'generating')->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- This Month -->
            <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-white bg-opacity-30 rounded-md flex items-center justify-center">
                                <i class="fas fa-calendar-alt text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-indigo-100 truncate uppercase tracking-wide">{{ __('This Month') }}</dt>
                                <dd class="text-lg font-medium text-white">{{ $reports->where('created_at', '>=', now()->startOfMonth())->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Card -->
        <div class="bg-white shadow rounded-lg mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i class="fas fa-filter mr-2 text-blue-600"></i>
                    {{ __('Filters') }}
                </h3>
            </div>
            <div class="p-6">
                <form method="GET" action="{{ route('financial-reports.index') }}" class="grid grid-cols-1 md:grid-cols-6 gap-4">
                    <div class="md:col-span-2">
                        <label for="report_type" class="block text-sm font-medium text-gray-700 mb-2">{{ __('Report Type') }}</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="report_type" name="report_type">
                            <option value="">{{ __('All Types') }}</option>
                            <option value="monthly" {{ request('report_type') == 'monthly' ? 'selected' : '' }}>{{ __('Monthly') }}</option>
                            <option value="quarterly" {{ request('report_type') == 'quarterly' ? 'selected' : '' }}>{{ __('Quarterly') }}</option>
                            <option value="annual" {{ request('report_type') == 'annual' ? 'selected' : '' }}>{{ __('Annual') }}</option>
                            <option value="contribution_summary" {{ request('report_type') == 'contribution_summary' ? 'selected' : '' }}>{{ __('Contribution Summary') }}</option>
                            <option value="custom" {{ request('report_type') == 'custom' ? 'selected' : '' }}>{{ __('Custom') }}</option>
                        </select>
                    </div>

                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">{{ __('Status') }}</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="status" name="status">
                            <option value="">{{ __('All Statuses') }}</option>
                            <option value="generating" {{ request('status') == 'generating' ? 'selected' : '' }}>{{ __('Generating') }}</option>
                            <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>{{ __('Completed') }}</option>
                            <option value="failed" {{ request('status') == 'failed' ? 'selected' : '' }}>{{ __('Failed') }}</option>
                        </select>
                    </div>

                    <div>
                        <label for="date_from" class="block text-sm font-medium text-gray-700 mb-2">{{ __('From Date') }}</label>
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="date_from" name="date_from" value="{{ request('date_from') }}" placeholder="dd/mm/yyyy">
                    </div>

                    <div>
                        <label for="date_to" class="block text-sm font-medium text-gray-700 mb-2">{{ __('To Date') }}</label>
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="date_to" name="date_to" value="{{ request('date_to') }}" placeholder="dd/mm/yyyy">
                    </div>

                    <div class="flex items-end">
                        <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-search mr-2"></i> {{ __('Filter') }}
                        </button>
                    </div>

                    <div class="flex items-end">
                        <a href="{{ route('financial-reports.index') }}" class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Reports Table -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900 flex items-center">
                        <i class="fas fa-chart-bar mr-2 text-blue-600"></i>
                        {{ __('Generated Reports') }}
                    </h3>
                    <div class="relative">
                        <button type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="toggleBulkActionsDropdown(event)">
                            <i class="fas fa-cog mr-2"></i>{{ __('Actions') }}
                            <i class="fas fa-chevron-down ml-2"></i>
                        </button>
                        <div id="bulkActionsDropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                            <div class="py-1">
                                <a href="#" onclick="bulkDownload('pdf')" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-file-pdf mr-2"></i>{{ __('Bulk Download PDF') }}
                                </a>
                                <a href="#" onclick="bulkDownload('excel')" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-file-excel mr-2"></i>{{ __('Bulk Download Excel') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="overflow-hidden">
                @if($reports->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()" class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Report Name') }}</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Type') }}</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Period') }}</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Generated') }}</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Status') }}</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Files') }}</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Actions') }}</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($reports as $report)
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="report-checkbox focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded" value="{{ $report->id }}">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ $report->report_name }}</div>
                                            <div class="text-sm text-gray-500">{{ __('Generated by') }}: {{ $report->generatedByUser->full_name }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                                {{ $report->getReportTypeLabel() }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ $report->getPeriodLabel() }}</div>
                                            <div class="text-sm text-gray-500">{{ $report->period_start->format('M j') }} - {{ $report->period_end->format('M j, Y') }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $report->created_at->format('M j, Y H:i') }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @if($report->status === 'generating')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    <i class="fas fa-spinner fa-spin mr-1"></i> {{ __('Generating') }}
                                                </span>
                                            @elseif($report->status === 'completed')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    <i class="fas fa-check-circle mr-1"></i> {{ __('Completed') }}
                                                </span>
                                            @elseif($report->status === 'failed')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    <i class="fas fa-times-circle mr-1"></i> {{ __('Failed') }}
                                                </span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex flex-col space-y-1">
                                                @if($report->hasPdf())
                                                    <a href="{{ route('financial-reports.pdf', $report) }}" class="inline-flex items-center px-2 py-1 border border-red-300 rounded text-xs font-medium text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" title="{{ __('Download PDF') }}">
                                                        <i class="fas fa-file-pdf mr-1"></i> PDF
                                                    </a>
                                                @endif
                                                @if($report->hasExcel())
                                                    <a href="{{ route('financial-reports.excel', $report) }}" class="inline-flex items-center px-2 py-1 border border-green-300 rounded text-xs font-medium text-green-700 bg-green-50 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500" title="{{ __('Download Excel') }}">
                                                        <i class="fas fa-file-excel mr-1"></i> Excel
                                                    </a>
                                                @endif
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <a href="{{ route('financial-reports.show', $report) }}" class="text-blue-600 hover:text-blue-900" title="{{ __('View Details') }}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if($report->isCompleted())
                                                    <button type="button" class="text-indigo-600 hover:text-indigo-900" onclick="shareReport({{ $report->id }})" title="{{ __('Share Report') }}">
                                                        <i class="fas fa-share"></i>
                                                    </button>
                                                @endif
                                                <button type="button" class="text-red-600 hover:text-red-900" onclick="deleteReport({{ $report->id }})" title="{{ __('Delete Report') }}">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                    </table>
                    </div>

                    <!-- Pagination -->
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                {{ __('Showing') }} {{ $reports->firstItem() }} {{ __('to') }} {{ $reports->lastItem() }} {{ __('of') }} {{ $reports->total() }} {{ __('results') }}
                            </div>
                            <div>
                                {{ $reports->appends(request()->query())->links() }}
                            </div>
                        </div>
                    </div>
                @else
                    <div class="text-center py-12">
                        <div class="mx-auto h-12 w-12 text-gray-400">
                            <i class="fas fa-chart-bar text-4xl"></i>
                        </div>
                        <h3 class="mt-4 text-lg font-medium text-gray-900">{{ __('No reports found') }}</h3>
                        <p class="mt-2 text-sm text-gray-500">{{ __('No financial reports match your current filters.') }}</p>
                        @can('generate-financial-reports')
                            <div class="mt-6">
                                <a href="{{ route('financial-reports.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-plus mr-2"></i> {{ __('Generate First Report') }}
                                </a>
                            </div>
                        @endcan
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">{{ __('Delete Report') }}</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeDeleteModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500 mb-4">{{ __('Are you sure you want to delete this report? This action cannot be undone.') }}</p>
                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-yellow-700">
                                {{ __('This will also delete all associated files (PDF and Excel).') }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex items-center justify-end px-4 py-3 space-x-3">
                <button type="button" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300" onclick="closeDeleteModal()">
                    {{ __('Cancel') }}
                </button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                        {{ __('Delete') }}
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Share Report Modal -->
<div id="shareModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">{{ __('Share Report') }}</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeShareModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="mt-2 px-7 py-3">
                <div class="mb-4">
                    <label for="shareEmails" class="block text-sm font-medium text-gray-700 mb-2">{{ __('Email Addresses') }}</label>
                    <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="shareEmails" rows="3" placeholder="{{ __('Enter email addresses separated by commas...') }}"></textarea>
                </div>
                <div class="mb-4">
                    <label for="shareMessage" class="block text-sm font-medium text-gray-700 mb-2">{{ __('Message (Optional)') }}</label>
                    <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="shareMessage" rows="2" placeholder="{{ __('Add a personal message...') }}"></textarea>
                </div>
            </div>
            <div class="flex items-center justify-end px-4 py-3 space-x-3">
                <button type="button" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300" onclick="closeShareModal()">
                    {{ __('Cancel') }}
                </button>
                <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="sendReport()">
                    {{ __('Send Report') }}
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function toggleQuickDropdown(event) {
    event.stopPropagation();
    const dropdown = document.getElementById('quickDropdown');
    const bulkDropdown = document.getElementById('bulkActionsDropdown');

    // Close bulk dropdown if open
    bulkDropdown.classList.add('hidden');

    // Toggle quick dropdown
    dropdown.classList.toggle('hidden');
}

function toggleBulkActionsDropdown(event) {
    event.stopPropagation();
    const dropdown = document.getElementById('bulkActionsDropdown');
    const quickDropdown = document.getElementById('quickDropdown');

    // Close quick dropdown if open
    quickDropdown.classList.add('hidden');

    // Toggle bulk dropdown
    dropdown.classList.toggle('hidden');
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const quickDropdown = document.getElementById('quickDropdown');
    const bulkDropdown = document.getElementById('bulkActionsDropdown');
    const deleteModal = document.getElementById('deleteModal');
    const shareModal = document.getElementById('shareModal');

    // Check if click is inside dropdown containers
    const quickContainer = event.target.closest('.relative');
    const bulkContainer = event.target.closest('.relative');

    // Close dropdowns if click is outside their containers
    if (!quickContainer || !quickContainer.contains(quickDropdown)) {
        quickDropdown.classList.add('hidden');
    }
    if (!bulkContainer || !bulkContainer.contains(bulkDropdown)) {
        bulkDropdown.classList.add('hidden');
    }

    // Close modals when clicking outside
    if (event.target === deleteModal) {
        closeDeleteModal();
    }
    if (event.target === shareModal) {
        closeShareModal();
    }
});

let selectedReportId = null;

function deleteReport(reportId) {
    const form = document.getElementById('deleteForm');
    form.action = `/financial-reports/${reportId}`;
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}

function shareReport(reportId) {
    selectedReportId = reportId;
    document.getElementById('shareModal').classList.remove('hidden');
}

function closeShareModal() {
    document.getElementById('shareModal').classList.add('hidden');
    // Clear form
    document.getElementById('shareEmails').value = '';
    document.getElementById('shareMessage').value = '';
}

function sendReport() {
    const emails = document.getElementById('shareEmails').value;
    const message = document.getElementById('shareMessage').value;

    if (!emails.trim()) {
        alert('{{ __("Please enter at least one email address") }}');
        return;
    }

    // In a real implementation, you would make an AJAX call here
    alert('{{ __("Report sharing functionality would be implemented here") }}');
    closeShareModal();
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.report-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

function bulkDownload(format) {
    const selectedReports = Array.from(document.querySelectorAll('.report-checkbox:checked')).map(cb => cb.value);
    
    if (selectedReports.length === 0) {
        alert('{{ __("Please select at least one report") }}');
        return;
    }
    
    // In a real implementation, you would create a bulk download endpoint
    alert(`{{ __("Bulk download") }} ${format.toUpperCase()} {{ __("functionality would be implemented here") }}`);
}

// Auto-refresh for generating reports
setInterval(function() {
    const generatingReports = document.querySelectorAll('.badge:contains("Generating")');
    if (generatingReports.length > 0) {
        // In a real implementation, you would check status via AJAX
        // location.reload();
    }
}, 30000); // Check every 30 seconds
</script>
@endpush
@endsection
