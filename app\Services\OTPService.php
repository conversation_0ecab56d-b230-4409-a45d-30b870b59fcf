<?php

namespace App\Services;

use App\Models\User;
use App\Models\OTP;
use App\Models\AuditLog;
use Illuminate\Support\Facades\Cache;

class OTPService
{
    private const MAX_ATTEMPTS = 3;
    private const LOCKOUT_DURATION = 15; // minutes

    public function generateOTP(User $user, string $type = 'general', int $expiryMinutes = 30): string
    {
        // Check if user is locked out
        if ($this->isUserLockedOut($user, $type)) {
            throw new \Exception('Too many OTP attempts. Please try again later.');
        }

        $otpRecord = OTP::generateForUser($user, $type, $expiryMinutes);

        AuditLog::log(
            'otp_generated',
            $user,
            [],
            ['type' => $type, 'expires_at' => $otpRecord->expires_at],
            "OTP generated for {$type}"
        );

        return $otpRecord->otp;
    }

    public function validateOTP(User $user, string $otp, string $type = 'general'): bool
    {
        // Check if user is locked out
        if ($this->isUserLockedOut($user, $type)) {
            throw new \Exception('Too many OTP attempts. Please try again later.');
        }

        $isValid = OTP::validateOTP($user, $otp, $type);

        if (!$isValid) {
            $this->recordFailedAttempt($user, $type);
            
            AuditLog::log(
                'otp_validation_failed',
                $user,
                [],
                ['type' => $type, 'otp' => substr($otp, 0, 2) . '****'],
                "Failed OTP validation for {$type}"
            );
        } else {
            $this->clearFailedAttempts($user, $type);
            
            AuditLog::log(
                'otp_validation_success',
                $user,
                [],
                ['type' => $type],
                "Successful OTP validation for {$type}"
            );
        }

        return $isValid;
    }

    public function resendOTP(User $user, string $type = 'general'): string
    {
        // Check resend limits
        $resendKey = "otp_resend:{$user->id}:{$type}";
        $resendCount = Cache::get($resendKey, 0);

        if ($resendCount >= 3) {
            throw new \Exception('Maximum OTP resend limit reached. Please try again later.');
        }

        // Generate new OTP
        $otp = $this->generateOTP($user, $type);

        // Increment resend counter
        Cache::put($resendKey, $resendCount + 1, now()->addMinutes(30));

        AuditLog::log(
            'otp_resent',
            $user,
            [],
            ['type' => $type, 'resend_count' => $resendCount + 1],
            "OTP resent for {$type}"
        );

        return $otp;
    }

    public function cleanupExpiredOTPs(): int
    {
        $deletedCount = OTP::cleanupExpired();

        if ($deletedCount > 0) {
            AuditLog::log(
                'otp_cleanup',
                null,
                [],
                ['deleted_count' => $deletedCount],
                "Cleaned up {$deletedCount} expired OTPs"
            );
        }

        return $deletedCount;
    }

    public function getActiveOTPsForUser(User $user, string $type = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = $user->otps()
                     ->whereNull('used_at')
                     ->where('expires_at', '>', now());

        if ($type) {
            $query->where('type', $type);
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    public function revokeOTPsForUser(User $user, string $type = null): int
    {
        $query = $user->otps()->whereNull('used_at');

        if ($type) {
            $query->where('type', $type);
        }

        $count = $query->count();
        $query->update(['used_at' => now()]);

        if ($count > 0) {
            AuditLog::log(
                'otps_revoked',
                $user,
                [],
                ['type' => $type, 'revoked_count' => $count],
                "Revoked {$count} OTPs" . ($type ? " for {$type}" : '')
            );
        }

        return $count;
    }

    public function generateRegistrationOTP(User $user): string
    {
        return $this->generateOTP($user, 'registration', 30);
    }

    public function generatePasswordResetOTP(User $user): string
    {
        return $this->generateOTP($user, 'password_reset', 15);
    }

    public function generateLoginOTP(User $user): string
    {
        return $this->generateOTP($user, 'login', 10);
    }

    public function validateRegistrationOTP(User $user, string $otp): bool
    {
        return $this->validateOTP($user, $otp, 'registration');
    }

    public function validatePasswordResetOTP(User $user, string $otp): bool
    {
        return $this->validateOTP($user, $otp, 'password_reset');
    }

    public function validateLoginOTP(User $user, string $otp): bool
    {
        return $this->validateOTP($user, $otp, 'login');
    }

    /**
     * Admin function to resend OTP for any user (bypasses normal limits)
     */
    public function adminResendOTP(User $targetUser, string $type = 'general', User $adminUser = null): string
    {
        // Generate new OTP without checking resend limits (admin override)
        $otp = $this->generateOTP($targetUser, $type);

        AuditLog::log(
            'admin_otp_resent',
            $adminUser,
            [],
            [
                'target_user_id' => $targetUser->id,
                'target_user_email' => $targetUser->email,
                'type' => $type
            ],
            "Admin resent OTP for user {$targetUser->email} ({$type})"
        );

        return $otp;
    }

    /**
     * Check if user has expired OTPs
     */
    public function hasExpiredOTPs(User $user, string $type = null): bool
    {
        $query = $user->otps()
                     ->whereNull('used_at')
                     ->where('expires_at', '<', now());

        if ($type) {
            $query->where('type', $type);
        }

        return $query->exists();
    }

    /**
     * Get expired OTPs for a user
     */
    public function getExpiredOTPsForUser(User $user, string $type = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = $user->otps()
                     ->whereNull('used_at')
                     ->where('expires_at', '<', now());

        if ($type) {
            $query->where('type', $type);
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    private function isUserLockedOut(User $user, string $type): bool
    {
        $lockoutKey = "otp_lockout:{$user->id}:{$type}";
        return Cache::has($lockoutKey);
    }

    private function recordFailedAttempt(User $user, string $type): void
    {
        $attemptKey = "otp_attempts:{$user->id}:{$type}";
        $attempts = Cache::get($attemptKey, 0) + 1;

        Cache::put($attemptKey, $attempts, now()->addMinutes(30));

        if ($attempts >= self::MAX_ATTEMPTS) {
            $lockoutKey = "otp_lockout:{$user->id}:{$type}";
            Cache::put($lockoutKey, true, now()->addMinutes(self::LOCKOUT_DURATION));

            AuditLog::log(
                'otp_user_locked_out',
                $user,
                [],
                ['type' => $type, 'attempts' => $attempts],
                "User locked out for {$type} OTP after {$attempts} failed attempts"
            );
        }
    }

    private function clearFailedAttempts(User $user, string $type): void
    {
        $attemptKey = "otp_attempts:{$user->id}:{$type}";
        $resendKey = "otp_resend:{$user->id}:{$type}";
        
        Cache::forget($attemptKey);
        Cache::forget($resendKey);
    }

    public function getOTPStats(): array
    {
        $totalActive = OTP::whereNull('used_at')
                         ->where('expires_at', '>', now())
                         ->count();

        $totalExpired = OTP::where('expires_at', '<', now())
                          ->whereNull('used_at')
                          ->count();

        $totalUsed = OTP::whereNotNull('used_at')->count();

        $byType = OTP::whereNull('used_at')
                    ->where('expires_at', '>', now())
                    ->groupBy('type')
                    ->selectRaw('type, count(*) as count')
                    ->pluck('count', 'type')
                    ->toArray();

        return [
            'total_active' => $totalActive,
            'total_expired' => $totalExpired,
            'total_used' => $totalUsed,
            'by_type' => $byType,
        ];
    }
}
