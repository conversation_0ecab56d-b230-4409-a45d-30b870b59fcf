<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class ResetAdminPassword extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:reset-password {--password=admin123}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reset the super admin password';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $password = $this->option('password');

        $admin = User::where('email', '<EMAIL>')->first();

        if (!$admin) {
            $this->error('Super admin user not found. Please run the SuperAdminSeeder first.');
            return 1;
        }

        $admin->update([
            'password' => Hash::make($password),
            'is_first_login' => false,
        ]);

        $this->info('✓ Super admin password reset successfully!');
        $this->info("📧 Email: <EMAIL>");
        $this->info("🔑 Password: {$password}");
        $this->warn('⚠️  Please change this password after logging in!');

        return 0;
    }
}
