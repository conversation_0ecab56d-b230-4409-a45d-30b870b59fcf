<?php

namespace App\Services;

use App\Models\Message;
use App\Models\User;
use App\Models\Church;
use App\Models\MessageRecipient;
use App\Models\AuditLog;
use App\Events\MessageSent;
use App\Services\NotificationService;
use App\Services\ChurchHierarchyService;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Collection;
use Exception;

class MessagingService
{
    protected NotificationService $notificationService;
    protected ChurchHierarchyService $hierarchyService;

    public function __construct(
        NotificationService $notificationService,
        ChurchHierarchyService $hierarchyService
    ) {
        $this->notificationService = $notificationService;
        $this->hierarchyService = $hierarchyService;
    }

    /**
     * Send a message to specific users.
     */
    public function sendMessage(
        User $sender,
        array $recipientIds,
        string $content,
        bool $isAnnouncement = false,
        string $priority = 'normal'
    ): Message {
        return DB::transaction(function () use ($sender, $recipientIds, $content, $isAnnouncement, $priority) {
            $this->validateSenderPermissions($sender, $recipientIds, $isAnnouncement);

            $message = Message::create([
                'sender_id'        => $sender->id,
                'content'          => $content,
                'is_group_message' => count($recipientIds) > 1,
                'is_announcement'  => $isAnnouncement,
                'priority'         => $priority,
            ]);

            $this->addRecipientsToMessage($message, $recipientIds);
            $this->sendMessageNotifications($message);

            AuditLog::log(
                'message_sent',
                $message,
                [],
                [
                    'recipient_count' => count($recipientIds),
                    'is_announcement' => $isAnnouncement,
                    'priority'        => $priority,
                ],
                "Message sent to " . count($recipientIds) . " recipients"
            );

            return $message;
        });
    }

    /**
     * Send a bulk message by church, role, level, or hierarchy.
     */
    public function sendBulkMessage(
        User $sender,
        string $content,
        string $targetType,
        array $targetIds = [],
        bool $isAnnouncement = true
    ): Message {
        return DB::transaction(function () use ($sender, $content, $targetType, $targetIds, $isAnnouncement) {
            $recipients = $this->getBulkMessageRecipients($sender, $targetType, $targetIds);

            if ($recipients->isEmpty()) {
                throw new Exception('No valid recipients found for bulk message.');
            }

            $message = $this->sendMessage(
                $sender,
                $recipients->pluck('id')->toArray(),
                $content,
                $isAnnouncement,
                'high'
            );

            if ($isAnnouncement) {
                $this->notificationService->sendBulkAnnouncement(
                    'Church Announcement',
                    $content,
                    $recipients->toArray(),
                    $sender
                );
            }

            return $message;
        });
    }

    /**
     * Send message to church hierarchy.
     */
    public function sendChurchHierarchyMessage(
        User $sender,
        string $content,
        bool $includeDescendants = true,
        bool $includeAncestors = false
    ): Message {
        $recipients = $this->getHierarchyRecipients($sender, $includeDescendants, $includeAncestors);

        return $this->sendMessage(
            $sender,
            $recipients->pluck('id')->toArray(),
            $content,
            true,
            'high'
        );
    }

    /**
     * Reply to a message.
     */
    public function replyToMessage(User $sender, Message $originalMessage, string $content): Message
    {
        $participants = collect([$originalMessage->sender_id])
            ->merge($originalMessage->recipients->pluck('id'))
            ->unique()
            ->reject(fn($id) => $id === $sender->id)
            ->values()
            ->toArray();

        return $this->sendMessage($sender, $participants, $content);
    }

    public function markMessageAsRead(Message $message, User $user): bool
    {
        $recipient = $message->recipients()->where('user_id', $user->id)->first();

        if (!$recipient || !$recipient->pivot) {
            return false;
        }

        $recipient->pivot->update(['read_at' => now()]);

        return true;
    }

    public function getMessagesForUser(User $user, int $limit = 50, int $offset = 0): Collection
    {
        return Message::where(function ($query) use ($user) {
            $query->where('sender_id', $user->id)
                ->orWhereHas('recipients', fn($q) => $q->where('user_id', $user->id));
        })
        ->with(['sender', 'recipients'])
        ->orderBy('created_at', 'desc')
        ->limit($limit)
        ->offset($offset)
        ->get();
    }

    public function getUnreadMessagesForUser(User $user): Collection
    {
        return Message::whereHas('recipients', function ($query) use ($user) {
            $query->where('user_id', $user->id)
                ->whereNull('read_at');
        })
        ->with(['sender'])
        ->orderBy('created_at', 'desc')
        ->get();
    }

    public function getConversation(User $user1, User $user2, int $limit = 50): Collection
    {
        return Message::where(function ($query) use ($user1, $user2) {
            $query->where('sender_id', $user1->id)
                ->whereHas('recipients', fn($q) => $q->where('user_id', $user2->id));
        })
        ->orWhere(function ($query) use ($user1, $user2) {
            $query->where('sender_id', $user2->id)
                ->whereHas('recipients', fn($q) => $q->where('user_id', $user1->id));
        })
        ->where('is_group_message', false)
        ->with(['sender', 'recipients'])
        ->orderBy('created_at', 'desc')
        ->limit($limit)
        ->get();
    }

    public function deleteMessage(Message $message, User $user): bool
    {
        if ($message->sender_id !== $user->id) {
            throw new Exception('Only the sender can delete messages.');
        }

        $message->delete();

        AuditLog::log(
            'message_deleted',
            $message,
            [],
            [],
            'Message deleted by sender'
        );

        return true;
    }

    public function getMessageStatistics(User $user = null): array
    {
        $stats = [
            'total_messages'       => Message::count(),
            'total_announcements'  => Message::where('is_announcement', true)->count(),
            'total_group_messages' => Message::where('is_group_message', true)->count(),
            'messages_today'       => Message::whereDate('created_at', today())->count(),
            'messages_this_week'   => Message::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
        ];

        if ($user) {
            $stats['user_sent']    = Message::where('sender_id', $user->id)->count();
            $stats['user_received'] = MessageRecipient::where('user_id', $user->id)->count();
            $stats['user_unread']   = MessageRecipient::where('user_id', $user->id)->whereNull('read_at')->count();
        }

        return $stats;
    }

    private function validateSenderPermissions(User $sender, array $recipientIds, bool $isAnnouncement): void
    {
        if (!$sender->hasPermissionTo('send-messages')) {
            throw new Exception('User does not have permission to send messages.');
        }

        if ($isAnnouncement && !$sender->hasPermissionTo('send-announcements')) {
            throw new Exception('User does not have permission to send announcements.');
        }

        $recipients = User::whereIn('id', $recipientIds)->get();
        $accessibleIds = $this->getAccessibleUsers($sender)->pluck('id')->toArray();

        foreach ($recipients as $recipient) {
            if (!in_array($recipient->id, $accessibleIds)) {
                throw new Exception("Cannot send message to user: {$recipient->full_name}");
            }
        }
    }

    private function addRecipientsToMessage(Message $message, array $recipientIds): void
    {
        $data = collect($recipientIds)->map(fn($id) => [
            'message_id' => $message->id,
            'user_id'    => $id,
            'created_at' => now(),
            'updated_at' => now(),
        ])->toArray();

        MessageRecipient::insert($data);
    }

    private function sendMessageNotifications(Message $message): void
    {
        $recipients = User::whereIn('id', $message->recipients->pluck('id'))->get();

        foreach ($recipients as $recipient) {
            $this->notificationService->sendMessageNotification($message, $recipient);
        }

        event(new MessageSent($message));
    }

    private function getBulkMessageRecipients(User $sender, string $type, array $targetIds): Collection
    {
        return match ($type) {
            'all_hierarchy' => $this->getHierarchyRecipients($sender, true, false),
            'church'        => $this->getChurchRecipients($targetIds),
            'role'          => $this->getRoleRecipients($targetIds, $sender),
            'level'         => $this->getLevelRecipients($targetIds, $sender),
            default         => collect(),
        };
    }

    private function getHierarchyRecipients(User $sender, bool $includeDescendants, bool $includeAncestors): Collection
    {
        $churches = collect([$sender->church]);

        if ($includeDescendants) {
            $churches = $churches->merge($this->hierarchyService->getAllDescendants($sender->church));
        }

        if ($includeAncestors) {
            $parent = $sender->church->parentChurch;
            while ($parent) {
                $churches->push($parent);
                $parent = $parent->parentChurch;
            }
        }

        return User::whereIn('church_id', $churches->pluck('id'))
                  ->where('is_active', true)
                  ->where('id', '!=', $sender->id)
                  ->get();
    }

    private function getChurchRecipients(array $churchIds): Collection
    {
        return User::whereIn('church_id', $churchIds)
                  ->where('is_active', true)
                  ->get();
    }

    private function getRoleRecipients(array $roles, User $sender): Collection
    {
        return $this->getAccessibleUsers($sender)->filter(
            fn($user) => $user->hasAnyRole($roles)
        );
    }

    private function getLevelRecipients(array $levels, User $sender): Collection
    {
        $churchIds = $this->hierarchyService->getChurchesUserCanAccess($sender)
                        ->whereIn('level', $levels)
                        ->pluck('id');

        return User::whereIn('church_id', $churchIds)
                  ->where('is_active', true)
                  ->where('id', '!=', $sender->id)
                  ->get();
    }

    private function getAccessibleUsers(User $sender): Collection
    {
        return User::whereIn('church_id', $this->hierarchyService->getChurchesUserCanAccess($sender)->pluck('id'))
                  ->where('is_active', true)
                  ->where('id', '!=', $sender->id)
                  ->get();
    }
}
