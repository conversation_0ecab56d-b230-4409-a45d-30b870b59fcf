@extends('layouts.app')

@section('title', __('common.dashboard') . ' - ' . __('common.fpct_system'))
@section('page-title', __('common.dashboard'))

@section('content')
<div class="space-y-6">
    <!-- Welcome Header -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">{{ __('common.welcome_back') }}, {{ Auth::user()?->full_name ?? 'User' }}!</h1>
                    <p class="mt-1 text-sm text-gray-600">
                        {{ Auth::user()?->church?->name ?? __('common.no_church_assigned') }} • {{ Auth::user()?->church?->level ? __('common.' . strtolower(Auth::user()->church->level->value)) : __('common.unknown') }} {{ __('common.level') }}
                        @if(Auth::user() && count(Auth::user()->getRoleNames()) > 0)
                            • {{ Auth::user()->getRoleNames()[0] }}
                        @endif
                    </p>
                </div>
                <div class="flex-shrink-0">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-2xl text-blue-600"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Users -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-users text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">{{ __('common.total_users') }}</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $statistics['total_users'] ?? 0 }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Users -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-user-check text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">{{ __('common.active_users') }}</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $statistics['active_users'] ?? 0 }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Requests -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-clock text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">{{ __('common.pending_requests') }}</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $statistics['pending_requests'] ?? 0 }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Unread Messages -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-envelope text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">{{ __('common.unread_messages') }}</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $statistics['unread_messages'] ?? 0 }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Church Information -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                    <i class="fas fa-church mr-2 text-blue-600"></i>
                    {{ __('common.church_information') }}
                </h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">{{ __('common.name') }}:</span>
                        <span class="text-sm text-gray-900">{{ Auth::user()->church ? Auth::user()->church->name : __('common.no_church_assigned') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">{{ __('common.level') }}:</span>
                        <span class="text-sm text-gray-900">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ Auth::user()->church && Auth::user()->church->level ? __('common.' . strtolower(Auth::user()->church->level->value)) : __('common.unknown') }}
                            </span>
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">{{ __('common.location') }}:</span>
                        <span class="text-sm text-gray-900">{{ Auth::user()->church ? Auth::user()->church->location : 'N/A' }}</span>
                    </div>
                    @if(Auth::user()->church && (Auth::user()->church->district || Auth::user()->church->region))
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">{{ __('churches.district') }}/{{ __('churches.region') }}:</span>
                        <span class="text-sm text-gray-900">
                            @if(Auth::user()->church->district){{ Auth::user()->church->district }}@endif
                            @if(Auth::user()->church->district && Auth::user()->church->region), @endif
                            @if(Auth::user()->church->region){{ Auth::user()->church->region }}@endif
                        </span>
                    </div>
                    @endif
                    @if(Auth::user()->church && Auth::user()->church->phone_number)
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">{{ __('churches.phone_number') }}:</span>
                        <span class="text-sm text-gray-900">
                            <a href="tel:{{ Auth::user()->church->phone_number }}" class="text-blue-600 hover:text-blue-800">
                                {{ Auth::user()->church->phone_number }}
                            </a>
                        </span>
                    </div>
                    @endif
                    @if(Auth::user()->church && Auth::user()->church->email)
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">{{ __('churches.email') }}:</span>
                        <span class="text-sm text-gray-900">
                            <a href="mailto:{{ Auth::user()->church->email }}" class="text-blue-600 hover:text-blue-800">
                                {{ Auth::user()->church->email }}
                            </a>
                        </span>
                    </div>
                    @endif
                    @if(Auth::user()->church && Auth::user()->church->date_established)
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">{{ __('common.date_established') }}:</span>
                        <span class="text-sm text-gray-900">{{ Auth::user()->church->date_established->format('M d, Y') }}</span>
                    </div>
                    @endif
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">{{ __('common.your_role') }}:</span>
                        <span class="text-sm text-gray-900">
                            @if(count(Auth::user()->getRoleNames()) > 0)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    {{ Auth::user()->getRoleNames()[0] }}
                                </span>
                            @else
                                <span class="text-gray-400">{{ __('common.no_role_assigned') }}</span>
                            @endif
                        </span>
                    </div>
                </div>

                @can('view-churches')
                <div class="mt-4">
                    <a href="{{ route('churches.show', Auth::user()->church) }}"
                       class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        {{ __('common.view_church_details') }}
                        <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
                @endcan
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                    <i class="fas fa-bolt mr-2 text-yellow-600"></i>
                    {{ __('common.quick_actions') }}
                </h3>
                <div class="grid grid-cols-2 gap-3">
                    @can('send-messages')
                    <a href="{{ route('messages.create') }}"
                       class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-envelope mr-2"></i>
                        {{ __('common.send_message') }}
                    </a>
                    @endcan

                    @can('create-requests')
                    <a href="{{ route('requests.create') }}"
                       class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        <i class="fas fa-plus mr-2"></i>
                        {{ __('common.create_request') }}
                    </a>
                    @endcan

                    @can('create-users')
                    <a href="{{ route('users.create') }}"
                       class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                        <i class="fas fa-user-plus mr-2"></i>
                        {{ __('common.add_user') }}
                    </a>
                    @endcan

                    @can('create-churches')
                    <a href="{{ route('churches.create') }}"
                       class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <i class="fas fa-church mr-2"></i>
                        {{ __('common.add_church') }}
                    </a>
                    @endcan
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Requests Section -->
    @if(isset($pending_requests) && count($pending_requests) > 0)
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
                <i class="fas fa-clock mr-2 text-yellow-600"></i>
                {{ __('common.pending_approvals') }}
            </h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                {{ __('common.requests_waiting_approval') }}
            </p>
        </div>
        <ul class="divide-y divide-gray-200">
            @foreach($pending_requests as $request)
            <li>
                <div class="px-4 py-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-file-alt text-yellow-600"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">
                                {{ __('common.' . $request->type) }}
                            </div>
                            <div class="text-sm text-gray-500">
                                {{ $request->church->name }} • {{ __('common.requested_by') }} {{ $request->user->full_name }}
                            </div>
                            <div class="text-xs text-gray-400">
                                {{ $request->created_at->diffForHumans() }}
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <a href="{{ route('requests.show', $request) }}"
                           class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200">
                            {{ __('common.review') }}
                        </a>
                    </div>
                </div>
            </li>
            @endforeach
        </ul>
        <div class="bg-gray-50 px-4 py-3 text-right">
            <a href="{{ route('requests.index') }}"
               class="text-sm font-medium text-blue-600 hover:text-blue-500">
                {{ __('common.view_all_requests') }}
                <i class="fas fa-arrow-right ml-1"></i>
            </a>
        </div>
    </div>
    @endif

    <!-- Recent Messages Section -->
    @if(isset($recent_messages) && count($recent_messages) > 0)
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
                <i class="fas fa-envelope mr-2 text-blue-600"></i>
                {{ __('common.recent_messages') }}
            </h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                {{ __('common.latest_messages_community') }}
            </p>
        </div>
        <ul class="divide-y divide-gray-200">
            @foreach($recent_messages as $message)
            <li>
                <div class="px-4 py-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                <span class="text-sm font-medium text-blue-600">
                                    {{ substr($message->sender->full_name, 0, 1) }}
                                </span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">
                                {{ $message->sender->full_name }}
                            </div>
                            <div class="text-sm text-gray-500">
                                {{ Str::limit($message->content, 80) }}
                            </div>
                            <div class="text-xs text-gray-400">
                                {{ $message->created_at ? $message->created_at->diffForHumans() : __('common.unknown') }}
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center">
                        @if($message->is_announcement)
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mr-2">
                                <i class="fas fa-bullhorn mr-1"></i>
                                {{ __('common.announcement') }}
                            </span>
                        @endif
                        <a href="{{ route('messages.show', $message) }}"
                           class="text-blue-600 hover:text-blue-500">
                            <i class="fas fa-eye"></i>
                        </a>
                    </div>
                </div>
            </li>
            @endforeach
        </ul>
        <div class="bg-gray-50 px-4 py-3 text-right">
            <a href="{{ route('messages.index') }}"
               class="text-sm font-medium text-blue-600 hover:text-blue-500">
                {{ __('common.view_all_messages') }}
                <i class="fas fa-arrow-right ml-1"></i>
            </a>
        </div>
    </div>
    @endif
</div>
@endsection