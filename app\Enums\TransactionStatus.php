<?php

namespace App\Enums;

enum TransactionStatus: string
{
    case PENDING = 'pending';
    case PROCESSING = 'processing';
    case COMPLETED = 'completed';
    case FAILED = 'failed';
    case CANCELLED = 'cancelled';

    public function getLabel(): string
    {
        return match ($this) {
            self::PENDING => 'Pending',
            self::PROCESSING => 'Processing',
            self::COMPLETED => 'Completed',
            self::FAILED => 'Failed',
            self::CANCELLED => 'Cancelled',
        };
    }

    public function getColor(): string
    {
        return match ($this) {
            self::PENDING => 'warning',
            self::PROCESSING => 'info',
            self::COMPLETED => 'success',
            self::FAILED => 'danger',
            self::CANCELLED => 'secondary',
        };
    }

    public function canBeUpdatedTo(TransactionStatus $newStatus): bool
    {
        return match ($this) {
            self::PENDING => in_array($newStatus, [self::PROCESSING, self::CANCELLED]),
            self::PROCESSING => in_array($newStatus, [self::COMPLETED, self::FAILED, self::CANCELLED]),
            self::COMPLETED, self::FAILED, self::CANCELLED => false,
        };
    }

    public static function getActiveStatuses(): array
    {
        return [self::PENDING, self::PROCESSING];
    }

    public static function getCompletedStatuses(): array
    {
        return [self::COMPLETED, self::FAILED, self::CANCELLED];
    }
}
