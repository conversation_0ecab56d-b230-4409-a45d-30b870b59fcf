@extends('layouts.app')

@section('title', 'Profile')
@section('page-title', 'My Profile')

@section('breadcrumbs')
    <li>
        <span class="mx-2">/</span>
        <span class="font-medium text-gray-900">Profile</span>
    </li>
@endsection

@section('content')
    <div class="max-w-4xl mx-auto space-y-6">
        <!-- Profile Header -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center space-x-6">
                    <div class="flex-shrink-0">
                        <img class="h-24 w-24 rounded-full object-cover border-4 border-blue-100"
                             src="{{ auth()->user()->profile_picture_url }}"
                             alt="{{ auth()->user()->full_name }}"
                             id="profile-preview">
                    </div>
                    <div class="flex-1">
                        <h1 class="text-2xl font-bold text-gray-900">{{ auth()->user()->full_name }}</h1>
                        <p class="text-sm text-gray-600">{{ auth()->user()->email }}</p>
                        <p class="text-sm text-gray-600">{{ auth()->user()->getRoleNames()->first() ?: 'User' }}</p>
                        @if(auth()->user()->church)
                            <p class="text-sm text-gray-600">{{ auth()->user()->church->name }}</p>
                        @endif
                    </div>
                    <div class="flex-shrink-0">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ auth()->user()->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ auth()->user()->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Form -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-6">Personal Information</h2>

                <form method="POST" action="{{ route('user.profile.update') }}" enctype="multipart/form-data" class="space-y-6">
                    @csrf
                    @method('PUT')

                    <!-- Profile Picture Upload -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Profile Picture</label>
                        <div class="flex items-center space-x-6">
                            <div class="flex-shrink-0">
                                <img class="h-16 w-16 rounded-full object-cover"
                                     src="{{ auth()->user()->profile_picture_url }}"
                                     alt="{{ auth()->user()->full_name }}"
                                     id="preview-image">
                            </div>
                            <div class="flex-1">
                                <input type="file"
                                       id="profile_picture"
                                       name="profile_picture"
                                       accept="image/*"
                                       class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                                <p class="text-xs text-gray-500 mt-1">PNG, JPG, GIF up to 2MB</p>
                            </div>
                        </div>
                        @error('profile_picture')
                            <span class="text-red-500 text-sm mt-1">{{ $message }}</span>
                        @enderror
                    </div>

                    <!-- Basic Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="full_name" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                            <input type="text"
                                   id="full_name"
                                   name="full_name"
                                   value="{{ old('full_name', auth()->user()->full_name) }}"
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            @error('full_name')
                                <span class="text-red-500 text-sm mt-1">{{ $message }}</span>
                            @enderror
                        </div>

                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input type="email"
                                   id="email"
                                   name="email"
                                   value="{{ old('email', auth()->user()->email) }}"
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            @error('email')
                                <span class="text-red-500 text-sm mt-1">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="phone_number" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                            <input type="text"
                                   id="phone_number"
                                   name="phone_number"
                                   value="{{ old('phone_number', auth()->user()->phone_number) }}"
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            @error('phone_number')
                                <span class="text-red-500 text-sm mt-1">{{ $message }}</span>
                            @enderror
                        </div>

                        <div>
                            <label for="date_of_birth" class="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
                            <input type="date"
                                   id="date_of_birth"
                                   name="date_of_birth"
                                   value="{{ old('date_of_birth', auth()->user()->date_of_birth?->format('Y-m-d')) }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            @error('date_of_birth')
                                <span class="text-red-500 text-sm mt-1">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>

                    <div>
                        <label for="gender" class="block text-sm font-medium text-gray-700 mb-1">Gender</label>
                        <select id="gender"
                                name="gender"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select Gender</option>
                            <option value="male" {{ old('gender', auth()->user()->gender) == 'male' ? 'selected' : '' }}>Male</option>
                            <option value="female" {{ old('gender', auth()->user()->gender) == 'female' ? 'selected' : '' }}>Female</option>
                            <option value="other" {{ old('gender', auth()->user()->gender) == 'other' ? 'selected' : '' }}>Other</option>
                        </select>
                        @error('gender')
                            <span class="text-red-500 text-sm mt-1">{{ $message }}</span>
                        @enderror
                    </div>

                    <div>
                        <label for="address" class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                        <textarea id="address"
                                  name="address"
                                  rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="Enter your full address">{{ old('address', auth()->user()->address) }}</textarea>
                        @error('address')
                            <span class="text-red-500 text-sm mt-1">{{ $message }}</span>
                        @enderror
                    </div>

                    <div>
                        <label for="bio" class="block text-sm font-medium text-gray-700 mb-1">Bio</label>
                        <textarea id="bio"
                                  name="bio"
                                  rows="4"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="Tell us about yourself">{{ old('bio', auth()->user()->bio) }}</textarea>
                        @error('bio')
                            <span class="text-red-500 text-sm mt-1">{{ $message }}</span>
                        @enderror
                    </div>

                    <!-- Emergency Contact -->
                    <div class="border-t pt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Emergency Contact</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="emergency_contact_name" class="block text-sm font-medium text-gray-700 mb-1">Contact Name</label>
                                <input type="text"
                                       id="emergency_contact_name"
                                       name="emergency_contact_name"
                                       value="{{ old('emergency_contact_name', auth()->user()->emergency_contact_name) }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                @error('emergency_contact_name')
                                    <span class="text-red-500 text-sm mt-1">{{ $message }}</span>
                                @enderror
                            </div>

                            <div>
                                <label for="emergency_contact_phone" class="block text-sm font-medium text-gray-700 mb-1">Contact Phone</label>
                                <input type="text"
                                       id="emergency_contact_phone"
                                       name="emergency_contact_phone"
                                       value="{{ old('emergency_contact_phone', auth()->user()->emergency_contact_phone) }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                @error('emergency_contact_phone')
                                    <span class="text-red-500 text-sm mt-1">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-4 pt-6 border-t">
                        <a href="{{ route('dashboard.index') }}"
                           class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                            Cancel
                        </a>
                        <button type="submit"
                                class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                            Update Profile
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Password Change Section -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-6">Change Password</h2>

                <form method="POST" action="{{ route('user.password.update') }}" class="space-y-6" id="password-change-form">
                    @csrf
                    @method('PUT')

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Current Password -->
                        <div class="md:col-span-2">
                            <label for="current_password" class="block text-sm font-medium text-gray-700 mb-2">
                                Current Password <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <input type="password"
                                       id="current_password"
                                       name="current_password"
                                       required
                                       class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 @error('current_password') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror"
                                       placeholder="Enter your current password">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <button type="button" onclick="togglePassword('current_password')" class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-eye" id="current_password_icon"></i>
                                    </button>
                                </div>
                            </div>
                            @error('current_password')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <!-- Real-time validation for current password -->
                            <div id="current-password-validation" class="mt-2 hidden">
                                <div id="current-password-checking" class="text-sm text-blue-600 hidden">
                                    <i class="fas fa-spinner fa-spin mr-1"></i>
                                    Verifying current password...
                                </div>
                                <div id="current-password-valid" class="text-sm text-green-600 hidden">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Current password is correct
                                </div>
                                <div id="current-password-invalid" class="text-sm text-red-600 hidden">
                                    <i class="fas fa-times-circle mr-1"></i>
                                    Current password is incorrect
                                </div>
                            </div>
                        </div>

                        <!-- New Password -->
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                New Password <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <input type="password"
                                       id="password"
                                       name="password"
                                       required
                                       minlength="8"
                                       class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 @error('password') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror"
                                       placeholder="Enter new password">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <button type="button" onclick="togglePassword('password')" class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-eye" id="password_icon"></i>
                                    </button>
                                </div>
                            </div>
                            @error('password')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <!-- Real-time validation for new password -->
                            <div id="new-password-validation" class="mt-2">
                                <div class="space-y-1">
                                    <div id="length-check" class="text-sm text-gray-500">
                                        <i class="fas fa-circle mr-1 text-xs"></i>
                                        At least 8 characters
                                    </div>
                                    <div id="uppercase-check" class="text-sm text-gray-500">
                                        <i class="fas fa-circle mr-1 text-xs"></i>
                                        One uppercase letter
                                    </div>
                                    <div id="lowercase-check" class="text-sm text-gray-500">
                                        <i class="fas fa-circle mr-1 text-xs"></i>
                                        One lowercase letter
                                    </div>
                                    <div id="number-check" class="text-sm text-gray-500">
                                        <i class="fas fa-circle mr-1 text-xs"></i>
                                        One number
                                    </div>
                                    <div id="special-check" class="text-sm text-gray-500">
                                        <i class="fas fa-circle mr-1 text-xs"></i>
                                        One special character
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Confirm Password -->
                        <div>
                            <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">
                                Confirm New Password <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <input type="password"
                                       id="password_confirmation"
                                       name="password_confirmation"
                                       required
                                       minlength="8"
                                       class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                                       placeholder="Confirm new password">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <button type="button" onclick="togglePassword('password_confirmation')" class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-eye" id="password_confirmation_icon"></i>
                                    </button>
                                </div>
                            </div>
                            <!-- Real-time validation for confirm password -->
                            <div id="confirm-password-validation" class="mt-2 hidden">
                                <div id="passwords-match" class="text-sm text-green-600 hidden">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Passwords match
                                </div>
                                <div id="passwords-no-match" class="text-sm text-red-600 hidden">
                                    <i class="fas fa-times-circle mr-1"></i>
                                    Passwords do not match
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Password Strength Indicator -->
                    <div id="password-strength-container" class="hidden">
                        <div class="mb-4">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700">Password Strength</span>
                                <span id="strength-text" class="text-sm font-medium">Weak</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div id="strength-bar" class="bg-red-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Validation Summary -->
                    <div id="validation-summary" class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-800 mb-3">Validation Status:</h4>
                        <div class="space-y-2">
                            <div id="current-password-status" class="flex items-center text-sm">
                                <i class="fas fa-circle mr-2 text-gray-400"></i>
                                <span class="text-gray-600">Current password verification</span>
                            </div>
                            <div id="new-password-status" class="flex items-center text-sm">
                                <i class="fas fa-circle mr-2 text-gray-400"></i>
                                <span class="text-gray-600">New password requirements</span>
                            </div>
                            <div id="password-match-status" class="flex items-center text-sm">
                                <i class="fas fa-circle mr-2 text-gray-400"></i>
                                <span class="text-gray-600">Password confirmation match</span>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end space-x-4 pt-6 border-t">
                        <button type="submit" id="change-password-btn"
                                class="px-6 py-2 bg-gray-400 text-white rounded-md cursor-not-allowed transition-colors duration-200"
                                disabled>
                            <i class="fas fa-key mr-2"></i>
                            Change Password
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        // Profile picture preview
        document.getElementById('profile_picture').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('preview-image').src = e.target.result;
                    document.getElementById('profile-preview').src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });

        // Password visibility toggle
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const icon = document.getElementById(fieldId + '_icon');

            if (field.type === 'password') {
                field.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Real-time password validation
        let currentPasswordValid = false;
        let newPasswordValid = false;
        let passwordsMatch = false;
        let currentPasswordTimeout;

        // Debounce function for API calls
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Check current password against server
        async function verifyCurrentPassword(password) {
            if (!password) {
                hideCurrentPasswordValidation();
                return;
            }

            showCurrentPasswordChecking();

            try {
                const response = await fetch('{{ route("user.password.verify") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        current_password: password
                    })
                });

                const data = await response.json();

                if (data.valid) {
                    showCurrentPasswordValid();
                    currentPasswordValid = true;
                } else {
                    showCurrentPasswordInvalid();
                    currentPasswordValid = false;
                }
            } catch (error) {
                console.error('Error verifying password:', error);
                showCurrentPasswordInvalid();
                currentPasswordValid = false;
            }

            updateValidationSummary();
            updateSubmitButton();
        }

        // Debounced version of password verification
        const debouncedVerifyPassword = debounce(verifyCurrentPassword, 800);

        // Validate new password strength
        function validateNewPassword(password) {
            const checks = {
                length: password.length >= 8,
                uppercase: /[A-Z]/.test(password),
                lowercase: /[a-z]/.test(password),
                number: /\d/.test(password),
                special: /[@$!%*?&]/.test(password)
            };

            // Update visual indicators
            updatePasswordCheck('length-check', checks.length);
            updatePasswordCheck('uppercase-check', checks.uppercase);
            updatePasswordCheck('lowercase-check', checks.lowercase);
            updatePasswordCheck('number-check', checks.number);
            updatePasswordCheck('special-check', checks.special);

            // Update password strength indicator
            updatePasswordStrength(password, checks);

            newPasswordValid = Object.values(checks).every(check => check);
            updateValidationSummary();
            updateSubmitButton();

            return newPasswordValid;
        }

        // Update password strength indicator
        function updatePasswordStrength(password, checks) {
            if (!password) {
                document.getElementById('password-strength-container').classList.add('hidden');
                return;
            }

            document.getElementById('password-strength-container').classList.remove('hidden');

            const validChecks = Object.values(checks).filter(check => check).length;
            const strengthBar = document.getElementById('strength-bar');
            const strengthText = document.getElementById('strength-text');

            let strength, color, width, text;

            if (validChecks <= 2) {
                strength = 'weak';
                color = 'bg-red-500';
                width = '25%';
                text = 'Weak';
            } else if (validChecks <= 3) {
                strength = 'fair';
                color = 'bg-yellow-500';
                width = '50%';
                text = 'Fair';
            } else if (validChecks <= 4) {
                strength = 'good';
                color = 'bg-blue-500';
                width = '75%';
                text = 'Good';
            } else {
                strength = 'strong';
                color = 'bg-green-500';
                width = '100%';
                text = 'Strong';
            }

            strengthBar.className = `${color} h-2 rounded-full transition-all duration-300`;
            strengthBar.style.width = width;
            strengthText.textContent = text;
            strengthText.className = `text-sm font-medium ${color.replace('bg-', 'text-')}`;
        }

        // Check if passwords match
        function checkPasswordsMatch() {
            const newPassword = document.getElementById('password').value;
            const confirmPassword = document.getElementById('password_confirmation').value;

            if (!confirmPassword) {
                hidePasswordMatchValidation();
                passwordsMatch = false;
                updateSubmitButton();
                return;
            }

            showPasswordMatchValidation();

            if (newPassword === confirmPassword) {
                showPasswordsMatch();
                passwordsMatch = true;
            } else {
                showPasswordsNoMatch();
                passwordsMatch = false;
            }

            updateValidationSummary();
            updateSubmitButton();
        }

        // Update validation summary
        function updateValidationSummary() {
            updateSummaryItem('current-password-status', currentPasswordValid, 'Current password verification');
            updateSummaryItem('new-password-status', newPasswordValid, 'New password requirements');
            updateSummaryItem('password-match-status', passwordsMatch, 'Password confirmation match');
        }

        function updateSummaryItem(elementId, isValid, text) {
            const element = document.getElementById(elementId);
            const icon = element.querySelector('i');
            const span = element.querySelector('span');

            if (isValid) {
                icon.className = 'fas fa-check-circle mr-2 text-green-500';
                span.className = 'text-green-700 font-medium';
                span.textContent = text + ' ✓';
            } else {
                icon.className = 'fas fa-circle mr-2 text-gray-400';
                span.className = 'text-gray-600';
                span.textContent = text;
            }
        }

        // Update submit button state
        function updateSubmitButton() {
            const submitBtn = document.getElementById('change-password-btn');
            const allValid = currentPasswordValid && newPasswordValid && passwordsMatch;

            if (allValid) {
                submitBtn.disabled = false;
                submitBtn.className = 'px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200 cursor-pointer';
            } else {
                submitBtn.disabled = true;
                submitBtn.className = 'px-6 py-2 bg-gray-400 text-white rounded-md cursor-not-allowed transition-colors duration-200';
            }
        }

        // Helper functions for UI updates
        function updatePasswordCheck(elementId, isValid) {
            const element = document.getElementById(elementId);
            const icon = element.querySelector('i');

            if (isValid) {
                element.className = 'text-sm text-green-600';
                icon.className = 'fas fa-check-circle mr-1 text-xs';
            } else {
                element.className = 'text-sm text-gray-500';
                icon.className = 'fas fa-circle mr-1 text-xs';
            }
        }

        function showCurrentPasswordChecking() {
            document.getElementById('current-password-validation').classList.remove('hidden');
            document.getElementById('current-password-checking').classList.remove('hidden');
            document.getElementById('current-password-valid').classList.add('hidden');
            document.getElementById('current-password-invalid').classList.add('hidden');
        }

        function showCurrentPasswordValid() {
            document.getElementById('current-password-checking').classList.add('hidden');
            document.getElementById('current-password-valid').classList.remove('hidden');
            document.getElementById('current-password-invalid').classList.add('hidden');
        }

        function showCurrentPasswordInvalid() {
            document.getElementById('current-password-checking').classList.add('hidden');
            document.getElementById('current-password-valid').classList.add('hidden');
            document.getElementById('current-password-invalid').classList.remove('hidden');
        }

        function hideCurrentPasswordValidation() {
            document.getElementById('current-password-validation').classList.add('hidden');
            currentPasswordValid = false;
            updateValidationSummary();
        }

        function showPasswordMatchValidation() {
            document.getElementById('confirm-password-validation').classList.remove('hidden');
        }

        function hidePasswordMatchValidation() {
            document.getElementById('confirm-password-validation').classList.add('hidden');
        }

        function showPasswordsMatch() {
            document.getElementById('passwords-match').classList.remove('hidden');
            document.getElementById('passwords-no-match').classList.add('hidden');
        }

        function showPasswordsNoMatch() {
            document.getElementById('passwords-match').classList.add('hidden');
            document.getElementById('passwords-no-match').classList.remove('hidden');
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            const currentPasswordField = document.getElementById('current_password');
            const newPasswordField = document.getElementById('password');
            const confirmPasswordField = document.getElementById('password_confirmation');

            // Current password validation
            currentPasswordField.addEventListener('input', function() {
                debouncedVerifyPassword(this.value);
            });

            // New password validation
            newPasswordField.addEventListener('input', function() {
                validateNewPassword(this.value);
                checkPasswordsMatch(); // Also check if passwords still match
            });

            // Confirm password validation
            confirmPasswordField.addEventListener('input', function() {
                checkPasswordsMatch();
            });
        });
    </script>
    @endpush
@endsection