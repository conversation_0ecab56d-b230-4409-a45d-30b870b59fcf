<x-layout>
    <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8 text-center">
            <div>
                <div class="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-red-100">
                    <i class="fas fa-exclamation-triangle text-red-600 text-4xl"></i>
                </div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                    {{ __('server_error') }}
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    {{ __('server_error') }}
                </p>
            </div>
            
            <div class="bg-white py-8 px-6 shadow-xl rounded-lg">
                <div class="space-y-4">
                    <div class="flex items-center justify-center">
                        <i class="fas fa-tools text-orange-500 text-2xl mr-3"></i>
                        <span class="text-lg font-medium text-gray-900">{{ __('support') }}</span>
                    </div>
                    
                    <p class="text-gray-600">
                        {{ __('try_again') }}
                    </p>
                    
                    @if(config('app.debug') && isset($exception))
                        <div class="mt-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-left">
                            <div class="text-sm font-medium mb-2">Debug Information:</div>
                            <div class="text-xs font-mono">
                                {{ $exception->getMessage() }}
                            </div>
                        </div>
                    @endif
                    
                    <div class="mt-6 space-y-3">
                        <button onclick="location.reload()" 
                                class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-redo mr-2"></i>
                            {{ __('try_again') }}
                        </button>
                        
                        <a href="{{ route('dashboard.index') }}" 
                           class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-home mr-2"></i>
                            {{ __('dashboard') }}
                        </a>
                        
                        <button onclick="history.back()" 
                                class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-arrow-left mr-2"></i>
                            {{ __('go_back') }}
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="text-xs text-gray-500">
                {{ __('error') }}: 500 - {{ __('server_error') }}
            </div>
        </div>
    </div>
</x-layout>
