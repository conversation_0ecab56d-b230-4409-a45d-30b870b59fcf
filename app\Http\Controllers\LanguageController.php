<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Auth;

class LanguageController extends Controller
{
    /**
     * Switch the application language
     */
    public function switch(Request $request)
    {
        $request->validate([
            'locale' => 'required|string|in:en,sw'
        ]);
        
        $locale = $request->input('locale');
        
        // Set the application locale
        App::setLocale($locale);
        
        // Store in session
        Session::put('locale', $locale);
        
        // Update user preference if authenticated
        if (Auth::check()) {
            Auth::user()->update(['locale' => $locale]);
        }
        
        // Return JSON response for AJAX requests
        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'locale' => $locale,
                'message' => __('Language switched successfully')
            ]);
        }
        
        // Redirect back for regular requests
        return redirect()->back()->with('success', __('Language switched successfully'));
    }
    
    /**
     * Get current locale information
     */
    public function current()
    {
        $currentLocale = App::getLocale();
        $supportedLocales = config('app.supported_locales', ['en', 'sw']);
        
        $localeInfo = [
            'current' => $currentLocale,
            'supported' => $supportedLocales,
            'languages' => [
                'en' => [
                    'code' => 'en',
                    'name' => 'English',
                    'native' => 'English',
                    'flag' => '🇺🇸'
                ],
                'sw' => [
                    'code' => 'sw',
                    'name' => 'Swahili',
                    'native' => 'Kiswahili',
                    'flag' => '🇹🇿'
                ]
            ]
        ];
        
        return response()->json($localeInfo);
    }
    
    /**
     * Get available languages for dropdown
     */
    public function getLanguages()
    {
        $currentLocale = App::getLocale();
        
        $languages = [
            [
                'code' => 'en',
                'name' => 'English',
                'native' => 'English',
                'flag' => '🇺🇸',
                'active' => $currentLocale === 'en'
            ],
            [
                'code' => 'sw',
                'name' => 'Swahili',
                'native' => 'Kiswahili',
                'flag' => '🇹🇿',
                'active' => $currentLocale === 'sw'
            ]
        ];
        
        return response()->json($languages);
    }
}
