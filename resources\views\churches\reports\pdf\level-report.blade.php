<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ ucfirst($level->value) }} Churches Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            color: #2563eb;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .summary {
            margin-bottom: 30px;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        .summary h2 {
            margin-top: 0;
            font-size: 18px;
            color: #1f2937;
        }
        .stats-grid {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .stats-row {
            display: table-row;
        }
        .stats-cell {
            display: table-cell;
            padding: 10px;
            text-align: center;
            border: 1px solid #ddd;
            background-color: #fff;
        }
        .stats-value {
            font-size: 20px;
            font-weight: bold;
            color: #2563eb;
        }
        .stats-label {
            font-size: 10px;
            color: #666;
            margin-top: 5px;
        }
        .churches-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .churches-table th,
        .churches-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-size: 10px;
        }
        .churches-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #1f2937;
        }
        .churches-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .demographics {
            font-size: 9px;
            line-height: 1.2;
        }
        .leadership {
            font-size: 9px;
            line-height: 1.2;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .page-break {
            page-break-after: always;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>{{ ucfirst($level->value) }} Churches Report</h1>
        <p>Free Pentecostal Church of Tanzania (FPCT)</p>
        <p>Generated on {{ $generated_at->format('F j, Y \a\t g:i A') }} by {{ $generated_by->full_name }}</p>
    </div>

    <!-- Summary Statistics -->
    <div class="summary">
        <h2>Summary Statistics</h2>
        <div class="stats-grid">
            <div class="stats-row">
                <div class="stats-cell">
                    <div class="stats-value">{{ $summary['total_churches'] }}</div>
                    <div class="stats-label">Total Churches</div>
                </div>
                <div class="stats-cell">
                    <div class="stats-value">{{ $summary['total_users'] }}</div>
                    <div class="stats-label">Total Users</div>
                </div>
                <div class="stats-cell">
                    <div class="stats-value">{{ $summary['active_users'] }}</div>
                    <div class="stats-label">Active Users</div>
                </div>
            </div>
        </div>

        @if($options['include_demographics'] && isset($summary['demographics']))
        <h3>Demographics Breakdown</h3>
        <div class="stats-grid">
            <div class="stats-row">
                <div class="stats-cell">
                    <div class="stats-value">{{ $summary['demographics']['total_children'] }}</div>
                    <div class="stats-label">Children (0-12)</div>
                </div>
                <div class="stats-cell">
                    <div class="stats-value">{{ $summary['demographics']['total_youth'] }}</div>
                    <div class="stats-label">Youth (13-25)</div>
                </div>
                <div class="stats-cell">
                    <div class="stats-value">{{ $summary['demographics']['total_young_adults'] }}</div>
                    <div class="stats-label">Young Adults (26-40)</div>
                </div>
                <div class="stats-cell">
                    <div class="stats-value">{{ $summary['demographics']['total_elders'] }}</div>
                    <div class="stats-label">Elders (41+)</div>
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- Churches List -->
    <h2>{{ ucfirst($level->value) }} Churches Details</h2>
    <table class="churches-table">
        <thead>
            <tr>
                <th>Church Name</th>
                <th>Location</th>
                <th>District/Region</th>
                <th>Parent Church</th>
                <th>Users</th>
                <th>Sub Churches</th>
                <th>Established</th>
                @if($options['include_demographics'])
                <th>Demographics</th>
                @endif
                @if($options['include_leadership'])
                <th>Leadership</th>
                @endif
            </tr>
        </thead>
        <tbody>
            @foreach($churches as $church)
            <tr>
                <td><strong>{{ $church->name }}</strong></td>
                <td>{{ $church->location }}</td>
                <td>
                    @if($church->district || $church->region)
                        {{ $church->district }}{{ $church->district && $church->region ? ', ' : '' }}{{ $church->region }}
                    @else
                        N/A
                    @endif
                </td>
                <td>{{ $church->parentChurch ? $church->parentChurch->name : 'None' }}</td>
                <td>{{ $church->users->count() }} ({{ $church->users->where('is_active', true)->count() }} active)</td>
                <td>{{ $church->childChurches->count() }}</td>
                <td>{{ $church->date_established ? $church->date_established->format('Y') : 'N/A' }}</td>
                @if($options['include_demographics'])
                <td class="demographics">
                    Children: {{ $church->children_count ?? 0 }}<br>
                    Youth: {{ $church->youth_count ?? 0 }}<br>
                    Young Adults: {{ $church->young_adults_count ?? 0 }}<br>
                    Elders: {{ $church->elders_count ?? 0 }}
                </td>
                @endif
                @if($options['include_leadership'])
                <td class="leadership">
                    @if($church->leaders->count() > 0)
                        @foreach($church->leaders as $leader)
                            @if($leader->user && $leader->user->full_name && $leader->role)
                                {{ $leader->user->full_name }} ({{ $leader->role }})<br>
                            @elseif($leader->user && $leader->user->full_name)
                                {{ $leader->user->full_name }}<br>
                            @endif
                        @endforeach
                    @else
                        No leaders assigned
                    @endif
                </td>
                @endif
            </tr>
            @endforeach
        </tbody>
    </table>

    <!-- Footer -->
    <div class="footer">
        <p>This report was generated by the FPCT Management System</p>
        <p>© {{ date('Y') }} Free Pentecostal Church of Tanzania</p>
    </div>
</body>
</html>
