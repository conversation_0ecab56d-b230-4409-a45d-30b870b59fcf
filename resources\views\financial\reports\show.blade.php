@extends('layouts.app')

@section('title', $financialReport->report_name)

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Success/Error Messages -->
        @if(session('success'))
            <div class="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                    </div>
                </div>
            </div>
        @endif

        @if(session('error'))
            <div class="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-circle text-red-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-red-800">{{ session('error') }}</p>
                    </div>
                </div>
            </div>
        @endif

        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ $financialReport->report_name }}</h1>
                    <p class="mt-2 text-gray-600">
                        {{ $financialReport->church->name }} • {{ $financialReport->period_start->format('M j, Y') }} - {{ $financialReport->period_end->format('M j, Y') }}
                    </p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('financial-reports.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-arrow-left mr-2"></i>{{ __('Back to Reports') }}
                    </a>
                    @if($financialReport->pdf_path)
                        <a href="{{ route('financial-reports.pdf', $financialReport) }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            <i class="fas fa-file-pdf mr-2"></i>{{ __('Download PDF') }}
                        </a>
                    @endif
                    @if($financialReport->excel_path)
                        <a href="{{ route('financial-reports.excel', $financialReport) }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            <i class="fas fa-file-excel mr-2"></i>{{ __('Download Excel') }}
                        </a>
                    @endif
                </div>
            </div>
        </div>

        <!-- Report Information -->
        <div class="bg-white shadow rounded-lg mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">{{ __('Report Information') }}</h3>
            </div>
            <div class="px-6 py-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{{ __('Report Type') }}</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ ucfirst($financialReport->report_type) }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{{ __('Generated By') }}</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $financialReport->generatedByUser->full_name }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{{ __('Generated On') }}</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $financialReport->created_at->format('M j, Y \a\t H:i') }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{{ __('Status') }}</dt>
                        <dd class="mt-1">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $financialReport->status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                {{ ucfirst($financialReport->status) }}
                            </span>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{{ __('Church Level') }}</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $financialReport->church->level->value }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{{ __('Location') }}</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $financialReport->church->location }}</dd>
                    </div>
                </div>
            </div>
        </div>

        @if(isset($financialReport->report_data['summary']))
        <!-- Financial Summary -->
        <div class="bg-white shadow rounded-lg mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">{{ __('Financial Summary') }}</h3>
            </div>
            <div class="px-6 py-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-arrow-down text-green-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-green-900">{{ __('Incoming Revenue') }}</p>
                                <p class="text-2xl font-bold text-green-900">{{ number_format($financialReport->report_data['summary']['incoming_revenue'] ?? 0, 2) }} TZS</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-arrow-up text-red-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-red-900">{{ __('Outgoing Revenue') }}</p>
                                <p class="text-2xl font-bold text-red-900">{{ number_format($financialReport->report_data['summary']['outgoing_revenue'] ?? 0, 2) }} TZS</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-chart-line text-blue-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-blue-900">{{ __('Net Revenue') }}</p>
                                <p class="text-2xl font-bold text-blue-900">{{ number_format($financialReport->report_data['summary']['net_revenue'] ?? 0, 2) }} TZS</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-exchange-alt text-gray-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-900">{{ __('Total Transactions') }}</p>
                                <p class="text-2xl font-bold text-gray-900">{{ number_format($financialReport->report_data['summary']['total_transactions'] ?? 0) }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-wallet text-purple-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-purple-900">{{ __('Current Balance') }}</p>
                                <p class="text-2xl font-bold text-purple-900">{{ number_format($financialReport->report_data['summary']['current_balance'] ?? 0, 2) }} TZS</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-coins text-indigo-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-indigo-900">{{ __('Total Balance') }}</p>
                                <p class="text-2xl font-bold text-indigo-900">{{ number_format($financialReport->report_data['summary']['total_balance'] ?? 0, 2) }} TZS</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif

        @if(isset($financialReport->report_data['monthly_breakdown']) && count($financialReport->report_data['monthly_breakdown']) > 0)
        <!-- Monthly Breakdown -->
        <div class="bg-white shadow rounded-lg mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">{{ __('Monthly Breakdown') }}</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Month') }}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Incoming') }}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Outgoing') }}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Net') }}</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($financialReport->report_data['monthly_breakdown'] as $month)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $month['month'] }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium">{{ number_format($month['incoming'], 2) }} TZS</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-medium">{{ number_format($month['outgoing'], 2) }} TZS</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium {{ $month['net'] >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                {{ number_format($month['net'], 2) }} TZS
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
        @endif

        @if(isset($financialReport->report_data['contribution_summary']))
        <!-- Contribution Summary -->
        <div class="bg-white shadow rounded-lg mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">{{ __('Contribution Summary') }}</h3>
            </div>
            <div class="px-6 py-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="text-center">
                        <p class="text-3xl font-bold text-blue-600">{{ $financialReport->report_data['contribution_summary']['total_contributions'] ?? 0 }}</p>
                        <p class="text-sm text-gray-500">{{ __('Total Contributions') }}</p>
                    </div>
                    <div class="text-center">
                        <p class="text-3xl font-bold text-green-600">{{ number_format($financialReport->report_data['contribution_summary']['total_collected'] ?? 0, 2) }}</p>
                        <p class="text-sm text-gray-500">{{ __('Total Collected (TZS)') }}</p>
                    </div>
                    <div class="text-center">
                        <p class="text-3xl font-bold text-purple-600">{{ $financialReport->report_data['contribution_summary']['active_contributions'] ?? 0 }}</p>
                        <p class="text-sm text-gray-500">{{ __('Active Contributions') }}</p>
                    </div>
                    <div class="text-center">
                        <p class="text-3xl font-bold text-orange-600">{{ number_format($financialReport->report_data['contribution_summary']['average_contribution'] ?? 0, 2) }}</p>
                        <p class="text-sm text-gray-500">{{ __('Average Contribution (TZS)') }}</p>
                    </div>
                </div>
            </div>
        </div>
        @endif

        @if(isset($financialReport->report_data['transactions']) && count($financialReport->report_data['transactions']) > 0)
        <!-- Recent Transactions -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">{{ __('Transaction Details') }}</h3>
                <p class="text-sm text-gray-500">{{ __('Showing') }} {{ count($financialReport->report_data['transactions']) }} {{ __('transactions') }}</p>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Date') }}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Transaction ID') }}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('From') }}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('To') }}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Amount') }}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Type') }}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Status') }}</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach(array_slice($financialReport->report_data['transactions'], 0, 20) as $transaction)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $transaction['date'] }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">{{ $transaction['transaction_id'] }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $transaction['from_church'] }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $transaction['to_church'] }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ number_format($transaction['amount'], 2) }} TZS</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $transaction['type'] }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    {{ $transaction['status'] }}
                                </span>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            @if(count($financialReport->report_data['transactions']) > 20)
            <div class="px-6 py-4 border-t border-gray-200 text-center">
                <p class="text-sm text-gray-500">{{ __('Showing first 20 transactions. Download the full report for complete data.') }}</p>
            </div>
            @endif
        </div>
        @endif
    </div>
</div>
@endsection
