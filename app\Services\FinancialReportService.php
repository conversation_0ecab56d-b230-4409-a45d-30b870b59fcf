<?php

namespace App\Services;

use App\Models\Church;
use App\Models\Transaction;
use App\Models\Contribution;
use App\Models\FinancialBalance;
use App\Models\FinancialReport;
use App\Models\User;
use App\Enums\TransactionStatus;
use App\Enums\TransactionType;
use App\Exports\FinancialReportExport;
use Illuminate\Support\Facades\Storage;
use Barryvdh\DomPDF\Facade\Pdf;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\Carbon;

class FinancialReportService
{
    /**
     * Generate financial report
     */
    public function generateReport(
        Church $church,
        User $generatedBy,
        string $reportType,
        Carbon $periodStart,
        Carbon $periodEnd,
        array $filters = []
    ): FinancialReport {
        $reportData = $this->collectReportData($church, $reportType, $periodStart, $periodEnd, $filters);
        
        $report = FinancialReport::create([
            'report_name' => $this->generateReportName($reportType, $periodStart, $periodEnd),
            'report_type' => $reportType,
            'church_id' => $church->id,
            'generated_by_user_id' => $generatedBy->id,
            'period_start' => $periodStart,
            'period_end' => $periodEnd,
            'report_data' => $reportData,
            'filters' => $filters,
            'status' => 'generating',
        ]);

        // Generate PDF and Excel files
        $this->generateReportFiles($report);

        $report->update(['status' => 'completed']);

        return $report;
    }

    /**
     * Collect report data based on type
     */
    protected function collectReportData(Church $church, string $reportType, Carbon $periodStart, Carbon $periodEnd, array $filters): array
    {
        return match ($reportType) {
            'monthly' => $this->getMonthlyReportData($church, $periodStart, $periodEnd, $filters),
            'quarterly' => $this->getQuarterlyReportData($church, $periodStart, $periodEnd, $filters),
            'annual' => $this->getAnnualReportData($church, $periodStart, $periodEnd, $filters),
            'contribution_summary' => $this->getContributionSummaryData($church, $periodStart, $periodEnd, $filters),
            'custom' => $this->getCustomReportData($church, $periodStart, $periodEnd, $filters),
            default => $this->getGeneralReportData($church, $periodStart, $periodEnd, $filters),
        };
    }

    /**
     * Get monthly report data
     */
    protected function getMonthlyReportData(Church $church, Carbon $periodStart, Carbon $periodEnd, array $filters): array
    {
        $transactions = $this->getTransactionsForPeriod($church, $periodStart, $periodEnd, $filters);
        
        return [
            'summary' => $this->getFinancialSummary($church, $periodStart, $periodEnd),
            'transactions' => $this->formatTransactionsData($transactions),
            'balance_history' => $this->getBalanceHistory($church, $periodStart, $periodEnd),
            'contribution_summary' => $this->getContributionSummaryForPeriod($church, $periodStart, $periodEnd),
            'monthly_breakdown' => $this->getMonthlyBreakdown($church, $periodStart, $periodEnd),
        ];
    }

    /**
     * Get quarterly report data
     */
    protected function getQuarterlyReportData(Church $church, Carbon $periodStart, Carbon $periodEnd, array $filters): array
    {
        return [
            'summary' => $this->getFinancialSummary($church, $periodStart, $periodEnd),
            'quarterly_trends' => $this->getQuarterlyTrends($church, $periodStart, $periodEnd),
            'contribution_performance' => $this->getContributionPerformance($church, $periodStart, $periodEnd),
            'church_comparisons' => $this->getChurchComparisons($church, $periodStart, $periodEnd),
        ];
    }

    /**
     * Get annual report data
     */
    protected function getAnnualReportData(Church $church, Carbon $periodStart, Carbon $periodEnd, array $filters): array
    {
        return [
            'summary' => $this->getFinancialSummary($church, $periodStart, $periodEnd),
            'annual_trends' => $this->getAnnualTrends($church, $periodStart, $periodEnd),
            'yearly_comparisons' => $this->getYearlyComparisons($church, $periodStart, $periodEnd),
            'growth_analysis' => $this->getGrowthAnalysis($church, $periodStart, $periodEnd),
            'top_contributors' => $this->getTopContributors($church, $periodStart, $periodEnd),
        ];
    }

    /**
     * Get contribution summary data
     */
    protected function getContributionSummaryData(Church $church, Carbon $periodStart, Carbon $periodEnd, array $filters): array
    {
        $contributions = Contribution::where('created_by_church_id', $church->id)
            ->whereBetween('created_at', [$periodStart, $periodEnd])
            ->with(['transactions'])
            ->get();

        return [
            'contributions' => $contributions->map(function ($contribution) {
                return [
                    'id' => $contribution->id,
                    'name' => $contribution->name,
                    'target_amount' => $contribution->target_amount,
                    'collected_amount' => $contribution->collected_amount,
                    'progress_percentage' => $contribution->getProgressPercentage(),
                    'status' => $contribution->status->value,
                    'transaction_count' => $contribution->transactions->count(),
                    'participating_churches' => $contribution->transactions->unique('from_church_id')->count(),
                ];
            }),
            'total_contributions' => $contributions->count(),
            'total_target' => $contributions->sum('target_amount'),
            'total_collected' => $contributions->sum('collected_amount'),
            'average_completion' => $contributions->avg(fn($c) => $c->getProgressPercentage()),
        ];
    }

    /**
     * Get financial summary
     */
    protected function getFinancialSummary(Church $church, Carbon $periodStart, Carbon $periodEnd): array
    {
        $transactions = Transaction::forChurch($church->id)
            ->where('status', TransactionStatus::COMPLETED)
            ->whereBetween('completed_at', [$periodStart, $periodEnd]);

        $incomingRevenue = (clone $transactions)->where('to_church_id', $church->id)->sum('amount');
        $outgoingRevenue = (clone $transactions)->where('from_church_id', $church->id)->sum('amount');
        $totalTransactions = (clone $transactions)->count();

        $balance = FinancialBalance::getOrCreateForChurch($church->id);

        return [
            'period_start' => $periodStart->format('Y-m-d'),
            'period_end' => $periodEnd->format('Y-m-d'),
            'incoming_revenue' => $incomingRevenue,
            'outgoing_revenue' => $outgoingRevenue,
            'net_revenue' => $incomingRevenue - $outgoingRevenue,
            'total_transactions' => $totalTransactions,
            'current_balance' => $balance->available_balance,
            'total_balance' => $balance->getTotalBalance(),
        ];
    }

    /**
     * Get transactions for period
     */
    protected function getTransactionsForPeriod(Church $church, Carbon $periodStart, Carbon $periodEnd, array $filters): \Illuminate\Database\Eloquent\Collection
    {
        $query = Transaction::forChurch($church->id)
            ->whereBetween('created_at', [$periodStart, $periodEnd])
            ->with(['fromChurch', 'toChurch', 'initiatedByUser', 'contribution']);

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * Format transactions data for report
     */
    protected function formatTransactionsData(\Illuminate\Database\Eloquent\Collection $transactions): array
    {
        return $transactions->map(function ($transaction) {
            return [
                'id' => $transaction->id,
                'transaction_id' => $transaction->transaction_id,
                'date' => $transaction->created_at->format('Y-m-d'),
                'from_church' => $transaction->fromChurch->name,
                'to_church' => $transaction->toChurch->name,
                'amount' => $transaction->amount,
                'type' => $transaction->type->getLabel(),
                'status' => $transaction->status->getLabel(),
                'description' => $transaction->description,
                'contribution' => $transaction->contribution?->name,
            ];
        })->toArray();
    }

    /**
     * Generate report files (PDF and Excel)
     */
    protected function generateReportFiles(FinancialReport $report): void
    {
        // Generate PDF
        $pdfPath = $this->generatePDF($report);
        $report->update(['pdf_path' => $pdfPath]);

        // Generate Excel
        $excelPath = $this->generateExcel($report);
        $report->update(['excel_path' => $excelPath]);
    }

    /**
     * Generate PDF report
     */
    protected function generatePDF(FinancialReport $report): string
    {
        $data = [
            'report' => $report,
            'church' => $report->church,
            'data' => $report->report_data,
            'generated_at' => now(),
        ];

        $pdf = Pdf::loadView('reports.financial-report-pdf', $data);
        
        $filename = 'reports/' . $report->id . '_' . $report->report_type . '_report.pdf';
        $pdfContent = $pdf->output();
        
        Storage::disk('local')->put($filename, $pdfContent);
        
        return $filename;
    }

    /**
     * Generate Excel report
     */
    protected function generateExcel(FinancialReport $report): string
    {
        $filename = 'reports/' . $report->id . '_' . $report->report_type . '_report.xlsx';
        
        Excel::store(new FinancialReportExport($report), $filename, 'local');
        
        return $filename;
    }

    /**
     * Generate report name
     */
    protected function generateReportName(string $reportType, Carbon $periodStart, Carbon $periodEnd): string
    {
        $typeName = match ($reportType) {
            'monthly' => 'Monthly Report',
            'quarterly' => 'Quarterly Report',
            'annual' => 'Annual Report',
            'contribution_summary' => 'Contribution Summary',
            'custom' => 'Custom Report',
            default => 'Financial Report',
        };

        $period = $periodStart->format('M Y');
        if ($periodStart->format('Y-m') !== $periodEnd->format('Y-m')) {
            $period = $periodStart->format('M Y') . ' - ' . $periodEnd->format('M Y');
        }

        return $typeName . ' - ' . $period;
    }

    /**
     * Get monthly breakdown
     */
    protected function getMonthlyBreakdown(Church $church, Carbon $periodStart, Carbon $periodEnd): array
    {
        $breakdown = [];
        $current = $periodStart->copy()->startOfMonth();
        
        while ($current->lte($periodEnd)) {
            $monthStart = $current->copy()->startOfMonth();
            $monthEnd = $current->copy()->endOfMonth();
            
            $incoming = Transaction::where('to_church_id', $church->id)
                ->where('status', TransactionStatus::COMPLETED)
                ->whereBetween('completed_at', [$monthStart, $monthEnd])
                ->sum('amount');
                
            $outgoing = Transaction::where('from_church_id', $church->id)
                ->where('status', TransactionStatus::COMPLETED)
                ->whereBetween('completed_at', [$monthStart, $monthEnd])
                ->sum('amount');
            
            $breakdown[] = [
                'month' => $current->format('F Y'),
                'incoming' => $incoming,
                'outgoing' => $outgoing,
                'net' => $incoming - $outgoing,
            ];
            
            $current->addMonth();
        }
        
        return $breakdown;
    }

    /**
     * Get custom report data
     */
    protected function getCustomReportData(Church $church, Carbon $periodStart, Carbon $periodEnd, array $filters): array
    {
        return $this->getGeneralReportData($church, $periodStart, $periodEnd, $filters);
    }

    /**
     * Get general report data
     */
    protected function getGeneralReportData(Church $church, Carbon $periodStart, Carbon $periodEnd, array $filters): array
    {
        $transactions = $this->getTransactionsForPeriod($church, $periodStart, $periodEnd, $filters);

        return [
            'summary' => $this->getFinancialSummary($church, $periodStart, $periodEnd),
            'transactions' => $this->formatTransactionsData($transactions),
        ];
    }

    /**
     * Get balance history for the period
     */
    protected function getBalanceHistory(Church $church, Carbon $periodStart, Carbon $periodEnd): array
    {
        $history = [];
        $current = $periodStart->copy();
        $balance = FinancialBalance::getOrCreateForChurch($church->id);
        $currentBalance = $balance->available_balance;

        // Calculate daily balance changes
        while ($current->lte($periodEnd)) {
            $dayStart = $current->copy()->startOfDay();
            $dayEnd = $current->copy()->endOfDay();

            // Get transactions for this day
            $incoming = Transaction::where('to_church_id', $church->id)
                ->where('status', TransactionStatus::COMPLETED)
                ->whereBetween('completed_at', [$dayStart, $dayEnd])
                ->sum('amount');

            $outgoing = Transaction::where('from_church_id', $church->id)
                ->where('status', TransactionStatus::COMPLETED)
                ->whereBetween('completed_at', [$dayStart, $dayEnd])
                ->sum('amount');

            $netChange = $incoming - $outgoing;
            $currentBalance += $netChange;

            $history[] = [
                'date' => $current->format('Y-m-d'),
                'balance' => $currentBalance,
                'incoming' => $incoming,
                'outgoing' => $outgoing,
                'net_change' => $netChange,
            ];

            $current->addDay();
        }

        return $history;
    }

    /**
     * Get contribution summary for period
     */
    protected function getContributionSummaryForPeriod(Church $church, Carbon $periodStart, Carbon $periodEnd): array
    {
        $contributions = Contribution::where('created_by_church_id', $church->id)
            ->whereBetween('created_at', [$periodStart, $periodEnd])
            ->with(['transactions' => function ($query) use ($periodStart, $periodEnd) {
                $query->whereBetween('completed_at', [$periodStart, $periodEnd])
                      ->where('status', TransactionStatus::COMPLETED);
            }])
            ->get();

        return [
            'total_contributions' => $contributions->count(),
            'total_collected' => $contributions->sum(function ($contribution) {
                return $contribution->transactions->sum('amount');
            }),
            'active_contributions' => $contributions->where('status', 'active')->count(),
            'completed_contributions' => $contributions->where('status', 'completed')->count(),
            'average_contribution' => $contributions->count() > 0
                ? $contributions->sum(function ($contribution) {
                    return $contribution->transactions->sum('amount');
                }) / $contributions->count()
                : 0,
        ];
    }

    /**
     * Get quarterly trends
     */
    protected function getQuarterlyTrends(Church $church, Carbon $periodStart, Carbon $periodEnd): array
    {
        $trends = [];
        $current = $periodStart->copy()->startOfQuarter();

        while ($current->lte($periodEnd)) {
            $quarterStart = $current->copy()->startOfQuarter();
            $quarterEnd = $current->copy()->endOfQuarter();

            $incoming = Transaction::where('to_church_id', $church->id)
                ->where('status', TransactionStatus::COMPLETED)
                ->whereBetween('completed_at', [$quarterStart, $quarterEnd])
                ->sum('amount');

            $outgoing = Transaction::where('from_church_id', $church->id)
                ->where('status', TransactionStatus::COMPLETED)
                ->whereBetween('completed_at', [$quarterStart, $quarterEnd])
                ->sum('amount');

            $trends[] = [
                'quarter' => 'Q' . $current->quarter . ' ' . $current->year,
                'period' => $quarterStart->format('M Y') . ' - ' . $quarterEnd->format('M Y'),
                'incoming' => $incoming,
                'outgoing' => $outgoing,
                'net' => $incoming - $outgoing,
            ];

            $current->addQuarter();
        }

        return $trends;
    }

    /**
     * Get annual trends
     */
    protected function getAnnualTrends(Church $church, Carbon $periodStart, Carbon $periodEnd): array
    {
        $trends = [];
        $current = $periodStart->copy()->startOfYear();

        while ($current->lte($periodEnd)) {
            $yearStart = $current->copy()->startOfYear();
            $yearEnd = $current->copy()->endOfYear();

            $incoming = Transaction::where('to_church_id', $church->id)
                ->where('status', TransactionStatus::COMPLETED)
                ->whereBetween('completed_at', [$yearStart, $yearEnd])
                ->sum('amount');

            $outgoing = Transaction::where('from_church_id', $church->id)
                ->where('status', TransactionStatus::COMPLETED)
                ->whereBetween('completed_at', [$yearStart, $yearEnd])
                ->sum('amount');

            $trends[] = [
                'year' => $current->year,
                'incoming' => $incoming,
                'outgoing' => $outgoing,
                'net' => $incoming - $outgoing,
            ];

            $current->addYear();
        }

        return $trends;
    }

    /**
     * Get contribution performance
     */
    protected function getContributionPerformance(Church $church, Carbon $periodStart, Carbon $periodEnd): array
    {
        $contributions = Contribution::where('created_by_church_id', $church->id)
            ->whereBetween('created_at', [$periodStart, $periodEnd])
            ->with(['transactions'])
            ->get();

        return $contributions->map(function ($contribution) {
            return [
                'name' => $contribution->name,
                'target' => $contribution->target_amount,
                'collected' => $contribution->collected_amount,
                'progress' => $contribution->getProgressPercentage(),
                'status' => $contribution->status->value,
                'transaction_count' => $contribution->transactions->count(),
            ];
        })->toArray();
    }

    /**
     * Get church comparisons
     */
    protected function getChurchComparisons(Church $church, Carbon $periodStart, Carbon $periodEnd): array
    {
        // Get sibling churches at the same level
        $siblingChurches = Church::where('level', $church->level)
            ->where('parent_church_id', $church->parent_church_id)
            ->where('id', '!=', $church->id)
            ->get();

        $comparisons = [];
        foreach ($siblingChurches as $siblingChurch) {
            $incoming = Transaction::where('to_church_id', $siblingChurch->id)
                ->where('status', TransactionStatus::COMPLETED)
                ->whereBetween('completed_at', [$periodStart, $periodEnd])
                ->sum('amount');

            $outgoing = Transaction::where('from_church_id', $siblingChurch->id)
                ->where('status', TransactionStatus::COMPLETED)
                ->whereBetween('completed_at', [$periodStart, $periodEnd])
                ->sum('amount');

            $comparisons[] = [
                'church_name' => $siblingChurch->name,
                'incoming' => $incoming,
                'outgoing' => $outgoing,
                'net' => $incoming - $outgoing,
            ];
        }

        return $comparisons;
    }

    /**
     * Get yearly comparisons
     */
    protected function getYearlyComparisons(Church $church, Carbon $periodStart, Carbon $periodEnd): array
    {
        $currentYear = $periodEnd->year;
        $previousYear = $currentYear - 1;

        $currentYearStart = Carbon::create($currentYear, 1, 1);
        $currentYearEnd = Carbon::create($currentYear, 12, 31);
        $previousYearStart = Carbon::create($previousYear, 1, 1);
        $previousYearEnd = Carbon::create($previousYear, 12, 31);

        // Current year data
        $currentIncoming = Transaction::where('to_church_id', $church->id)
            ->where('status', TransactionStatus::COMPLETED)
            ->whereBetween('completed_at', [$currentYearStart, $currentYearEnd])
            ->sum('amount');

        $currentOutgoing = Transaction::where('from_church_id', $church->id)
            ->where('status', TransactionStatus::COMPLETED)
            ->whereBetween('completed_at', [$currentYearStart, $currentYearEnd])
            ->sum('amount');

        // Previous year data
        $previousIncoming = Transaction::where('to_church_id', $church->id)
            ->where('status', TransactionStatus::COMPLETED)
            ->whereBetween('completed_at', [$previousYearStart, $previousYearEnd])
            ->sum('amount');

        $previousOutgoing = Transaction::where('from_church_id', $church->id)
            ->where('status', TransactionStatus::COMPLETED)
            ->whereBetween('completed_at', [$previousYearStart, $previousYearEnd])
            ->sum('amount');

        return [
            'current_year' => [
                'year' => $currentYear,
                'incoming' => $currentIncoming,
                'outgoing' => $currentOutgoing,
                'net' => $currentIncoming - $currentOutgoing,
            ],
            'previous_year' => [
                'year' => $previousYear,
                'incoming' => $previousIncoming,
                'outgoing' => $previousOutgoing,
                'net' => $previousIncoming - $previousOutgoing,
            ],
            'growth' => [
                'incoming_growth' => $previousIncoming > 0 ? (($currentIncoming - $previousIncoming) / $previousIncoming) * 100 : 0,
                'outgoing_growth' => $previousOutgoing > 0 ? (($currentOutgoing - $previousOutgoing) / $previousOutgoing) * 100 : 0,
                'net_growth' => $previousIncoming - $previousOutgoing != 0 ?
                    (($currentIncoming - $currentOutgoing) - ($previousIncoming - $previousOutgoing)) / abs($previousIncoming - $previousOutgoing) * 100 : 0,
            ],
        ];
    }

    /**
     * Get growth analysis
     */
    protected function getGrowthAnalysis(Church $church, Carbon $periodStart, Carbon $periodEnd): array
    {
        $monthlyData = [];
        $current = $periodStart->copy()->startOfMonth();

        while ($current->lte($periodEnd)) {
            $monthStart = $current->copy()->startOfMonth();
            $monthEnd = $current->copy()->endOfMonth();

            $incoming = Transaction::where('to_church_id', $church->id)
                ->where('status', TransactionStatus::COMPLETED)
                ->whereBetween('completed_at', [$monthStart, $monthEnd])
                ->sum('amount');

            $outgoing = Transaction::where('from_church_id', $church->id)
                ->where('status', TransactionStatus::COMPLETED)
                ->whereBetween('completed_at', [$monthStart, $monthEnd])
                ->sum('amount');

            $monthlyData[] = [
                'month' => $current->format('Y-m'),
                'incoming' => $incoming,
                'outgoing' => $outgoing,
                'net' => $incoming - $outgoing,
            ];

            $current->addMonth();
        }

        // Calculate growth rates
        $growthRates = [];
        for ($i = 1; $i < count($monthlyData); $i++) {
            $current = $monthlyData[$i];
            $previous = $monthlyData[$i - 1];

            $growthRates[] = [
                'month' => $current['month'],
                'incoming_growth' => $previous['incoming'] > 0 ? (($current['incoming'] - $previous['incoming']) / $previous['incoming']) * 100 : 0,
                'outgoing_growth' => $previous['outgoing'] > 0 ? (($current['outgoing'] - $previous['outgoing']) / $previous['outgoing']) * 100 : 0,
                'net_growth' => $previous['net'] != 0 ? (($current['net'] - $previous['net']) / abs($previous['net'])) * 100 : 0,
            ];
        }

        return [
            'monthly_data' => $monthlyData,
            'growth_rates' => $growthRates,
            'average_growth' => [
                'incoming' => count($growthRates) > 0 ? array_sum(array_column($growthRates, 'incoming_growth')) / count($growthRates) : 0,
                'outgoing' => count($growthRates) > 0 ? array_sum(array_column($growthRates, 'outgoing_growth')) / count($growthRates) : 0,
                'net' => count($growthRates) > 0 ? array_sum(array_column($growthRates, 'net_growth')) / count($growthRates) : 0,
            ],
        ];
    }

    /**
     * Get top contributors
     */
    protected function getTopContributors(Church $church, Carbon $periodStart, Carbon $periodEnd): array
    {
        $contributors = Transaction::where('to_church_id', $church->id)
            ->where('status', TransactionStatus::COMPLETED)
            ->where('type', TransactionType::CONTRIBUTION)
            ->whereBetween('completed_at', [$periodStart, $periodEnd])
            ->with(['fromChurch'])
            ->selectRaw('from_church_id, SUM(amount) as total_contributed, COUNT(*) as transaction_count')
            ->groupBy('from_church_id')
            ->orderByDesc('total_contributed')
            ->limit(10)
            ->get();

        return $contributors->map(function ($contributor) {
            return [
                'church_name' => $contributor->fromChurch->name,
                'church_level' => $contributor->fromChurch->level->value,
                'total_contributed' => $contributor->total_contributed,
                'transaction_count' => $contributor->transaction_count,
                'average_contribution' => $contributor->transaction_count > 0 ? $contributor->total_contributed / $contributor->transaction_count : 0,
            ];
        })->toArray();
    }
}
