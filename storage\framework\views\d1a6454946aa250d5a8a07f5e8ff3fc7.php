<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo e($title ?? 'FPCT Church Management System (CMS)'); ?></title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Alpine.js for interactive components -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <?php echo app('Illuminate\Foundation\Vite')(['resources/js/app.js']); ?>

    <script>
        window.Laravel = {
            userId: <?php echo e(Auth::id() ?? 'null'); ?>,
            csrfToken: '<?php echo e(csrf_token()); ?>'
        };
    </script>

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>

<body class="bg-gray-50 font-sans antialiased">
    <?php if(auth()->guard()->check()): ?>
        <!-- Authenticated layout with sidebar -->
        <div class="flex h-screen bg-gray-100" x-data="{ sidebarOpen: false }">
            <!-- Sidebar for desktop -->
            <div class="hidden lg:flex lg:flex-shrink-0">
                <div class="flex flex-col w-64">
                    <div class="flex flex-col flex-grow bg-gradient-to-b from-blue-600 to-blue-800 pt-5 pb-4 overflow-y-auto">
                        <!-- Logo -->
                        <div class="flex items-center flex-shrink-0 px-4">
                            <a href="<?php echo e(route('dashboard.index')); ?>" class="flex items-center text-white font-bold text-xl">
                                <i class="fas fa-church mr-3"></i>
                                FPCT Church Management System (CMS)
                            </a>
                        </div>

                        <!-- Navigation Links -->
                        <nav class="mt-8 flex-1 px-2 space-y-1">
                            <!-- Dashboard -->
                            <a href="<?php echo e(route('dashboard.index')); ?>"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 <?php echo e(request()->routeIs('dashboard.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white'); ?>">
                                <i class="fas fa-tachometer-alt mr-3"></i>
                                <?php echo e(__('common.dashboard')); ?>

                            </a>

                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage-users')): ?>
                            <!-- Users -->
                            <a href="<?php echo e(route('users.index')); ?>"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 <?php echo e(request()->routeIs('users.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white'); ?>">
                                <i class="fas fa-users mr-3"></i>
                                <?php echo e(__('common.users')); ?>

                            </a>
                            <?php endif; ?>

                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage-churches')): ?>
                            <!-- Churches -->
                            <a href="<?php echo e(route('churches.index')); ?>"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 <?php echo e(request()->routeIs('churches.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white'); ?>">
                                <i class="fas fa-church mr-3"></i>
                                <?php echo e(__('common.churches')); ?>

                            </a>
                            <?php endif; ?>

                            <!-- Messages -->
                            <a href="<?php echo e(route('messages.index')); ?>"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 <?php echo e(request()->routeIs('messages.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white'); ?>">
                                <i class="fas fa-envelope mr-3"></i>
                                <?php echo e(__('common.messages')); ?>

                            </a>

                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage-requests')): ?>
                            <!-- Requests -->
                            <a href="<?php echo e(route('requests.index')); ?>"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 <?php echo e(request()->routeIs('requests.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white'); ?>">
                                <i class="fas fa-clipboard-list mr-3"></i>
                                <?php echo e(__('common.requests')); ?>

                            </a>
                            <?php endif; ?>

                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage-permissions')): ?>
                            <!-- Roles & Permissions -->
                            <div class="space-y-1">
                                <a href="<?php echo e(route('roles.index')); ?>"
                                   class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 <?php echo e(request()->routeIs('roles.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white'); ?>">
                                    <i class="fas fa-user-tag mr-3"></i>
                                    <?php echo e(__('common.roles')); ?>

                                </a>
                                <a href="<?php echo e(route('permissions.index')); ?>"
                                   class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 <?php echo e(request()->routeIs('permissions.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white'); ?>">
                                    <i class="fas fa-key mr-3"></i>
                                    <?php echo e(__('common.permissions')); ?>

                                </a>
                            </div>
                            <?php endif; ?>
                        </nav>

                        <!-- User Menu -->
                        <div class="flex-shrink-0 flex border-t border-blue-700 p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <?php if(Auth::user()->profile_picture): ?>
                                        <img class="h-8 w-8 rounded-full" src="<?php echo e(asset('storage/' . Auth::user()->profile_picture)); ?>" alt="<?php echo e(Auth::user()->full_name); ?>">
                                    <?php else: ?>
                                        <div class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                                            <span class="text-sm font-medium text-white"><?php echo e(substr(Auth::user()->full_name, 0, 1)); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-white"><?php echo e(Auth::user()->full_name); ?></p>
                                    <div class="flex space-x-2">
                                        <a href="<?php echo e(route('user.profile')); ?>" class="text-xs text-blue-200 hover:text-white"><?php echo e(__('common.profile')); ?></a>
                                        <form method="POST" action="<?php echo e(route('logout')); ?>" class="inline">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="text-xs text-blue-200 hover:text-white"><?php echo e(__('common.logout')); ?></button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile sidebar -->
            <div x-show="sidebarOpen" class="fixed inset-0 flex z-40 lg:hidden" x-cloak>
                <div x-show="sidebarOpen" x-transition:enter="transition-opacity ease-linear duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="transition-opacity ease-linear duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 bg-gray-600 bg-opacity-75" @click="sidebarOpen = false"></div>
                
                <div x-show="sidebarOpen" x-transition:enter="transition ease-in-out duration-300 transform" x-transition:enter-start="-translate-x-full" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out duration-300 transform" x-transition:leave-start="translate-x-0" x-transition:leave-end="-translate-x-full" class="relative flex-1 flex flex-col max-w-xs w-full bg-gradient-to-b from-blue-600 to-blue-800">
                    <!-- Mobile sidebar content (same as desktop) -->
                    <div class="flex flex-col flex-grow pt-5 pb-4 overflow-y-auto">
                        <div class="flex items-center flex-shrink-0 px-4">
                            <a href="<?php echo e(route('dashboard.index')); ?>" class="flex items-center text-white font-bold text-xl">
                                <i class="fas fa-church mr-3"></i>
                                FPCT Church Management System (CMS)
                            </a>
                        </div>
                        <!-- Same navigation as desktop -->
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <div class="flex flex-col w-0 flex-1 overflow-hidden">
                <!-- Top navigation -->
                <div class="relative z-10 flex-shrink-0 flex h-16 bg-white shadow">
                    <button @click="sidebarOpen = true" class="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 lg:hidden">
                        <i class="fas fa-bars"></i>
                    </button>
                    
                    <div class="flex-1 px-4 flex justify-between">
                        <div class="flex-1 flex">
                            <!-- Breadcrumbs or page title can go here -->
                        </div>
                        <div class="ml-4 flex items-center md:ml-6">
                            <!-- Language switcher -->
                            <?php if (isset($component)) { $__componentOriginal8d3bff7d7383a45350f7495fc470d934 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8d3bff7d7383a45350f7495fc470d934 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.language-switcher','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('language-switcher'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8d3bff7d7383a45350f7495fc470d934)): ?>
<?php $attributes = $__attributesOriginal8d3bff7d7383a45350f7495fc470d934; ?>
<?php unset($__attributesOriginal8d3bff7d7383a45350f7495fc470d934); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8d3bff7d7383a45350f7495fc470d934)): ?>
<?php $component = $__componentOriginal8d3bff7d7383a45350f7495fc470d934; ?>
<?php unset($__componentOriginal8d3bff7d7383a45350f7495fc470d934); ?>
<?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Page content -->
                <main class="flex-1 relative overflow-y-auto focus:outline-none">
                    <div class="py-6">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
                            <?php echo e($slot); ?>

                        </div>
                    </div>
                </main>
            </div>
        </div>
    <?php else: ?>
        <!-- Guest layout (for login, register, password reset, etc.) -->
        <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div class="max-w-md w-full space-y-8">
                <div>
                    <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
                        <i class="fas fa-church text-blue-600 text-xl"></i>
                    </div>
                    <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                        FPCT Church Management System (CMS)
                    </h2>
                </div>
                <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                    <?php echo e($slot); ?>

                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>

</html>
<?php /**PATH C:\wamp64\www\fpct-system\resources\views/components/layout.blade.php ENDPATH**/ ?>