<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Churches table indexes
        Schema::table('churches', function (Blueprint $table) {
            $table->index(['level', 'parent_church_id']);
            $table->index(['parent_church_id', 'level']);
            $table->index('level');
            $table->index('date_established');
        });

        // Users table indexes
        Schema::table('users', function (Blueprint $table) {
            $table->index(['church_id', 'is_active']);
            $table->index(['is_active', 'church_id']);
            $table->index('role');
            $table->index('is_first_login');
        });

        // Messages table indexes
        Schema::table('messages', function (Blueprint $table) {
            $table->index(['sender_id', 'created_at']);
            $table->index(['is_announcement', 'created_at']);
            $table->index(['is_group_message', 'created_at']);
            $table->index('priority');
        });

        // Message recipient table indexes
        Schema::table('message_recipient', function (Blueprint $table) {
            $table->index(['user_id', 'read_at']);
            $table->index(['message_id', 'user_id']);
            $table->index('read_at');
        });

        // Requests table indexes
        Schema::table('requests', function (Blueprint $table) {
            $table->index(['church_id', 'status']);
            $table->index(['user_id', 'status']);
            $table->index(['status', 'created_at']);
            $table->index('type');
            $table->index('approved_by');
        });

        // Church leaders table indexes
        Schema::table('church_leaders', function (Blueprint $table) {
            $table->index(['church_id', 'position']);
            $table->index('user_id');
        });

        // Note: OTPs table indexes already exist in the OTPs migration
        // Approval workflows table indexes
        Schema::table('approval_workflows', function (Blueprint $table) {
            $table->index(['request_id', 'step_order']);
            $table->index(['approver_church_id', 'status']);
            $table->index(['status', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Churches table indexes
        Schema::table('churches', function (Blueprint $table) {
            $table->dropIndex(['level', 'parent_church_id']);
            $table->dropIndex(['parent_church_id', 'level']);
            $table->dropIndex(['level']);
            $table->dropIndex(['date_established']);
        });

        // Users table indexes
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['church_id', 'is_active']);
            $table->dropIndex(['is_active', 'church_id']);
            $table->dropIndex(['role']);
            $table->dropIndex(['is_first_login']);
        });

        // Messages table indexes
        Schema::table('messages', function (Blueprint $table) {
            $table->dropIndex(['sender_id', 'created_at']);
            $table->dropIndex(['is_announcement', 'created_at']);
            $table->dropIndex(['is_group_message', 'created_at']);
            $table->dropIndex(['priority']);
        });

        // Message recipient table indexes
        Schema::table('message_recipient', function (Blueprint $table) {
            $table->dropIndex(['user_id', 'read_at']);
            $table->dropIndex(['message_id', 'user_id']);
            $table->dropIndex(['read_at']);
        });

        // Requests table indexes
        Schema::table('requests', function (Blueprint $table) {
            $table->dropIndex(['church_id', 'status']);
            $table->dropIndex(['user_id', 'status']);
            $table->dropIndex(['status', 'created_at']);
            $table->dropIndex(['type']);
            $table->dropIndex(['approved_by']);
        });

        // Church leaders table indexes
        Schema::table('church_leaders', function (Blueprint $table) {
            $table->dropIndex(['church_id', 'position']);
            $table->dropIndex(['user_id']);
        });

        // Approval workflows table indexes
        Schema::table('approval_workflows', function (Blueprint $table) {
            $table->dropIndex(['request_id', 'step_order']);
            $table->dropIndex(['approver_church_id', 'status']);
            $table->dropIndex(['status', 'created_at']);
        });
    }
};
