<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Models\Transaction;
use App\Enums\TransactionStatus;
use App\Enums\PaymentMethod;

class AzamPayService
{
    private string $baseUrl;
    private ?string $appName;
    private ?string $clientId;
    private ?string $clientSecret;
    private ?string $apiKey;
    private ?string $accessToken = null;

    public function __construct()
    {
        $this->baseUrl = config('services.azampay.base_url', 'https://sandbox.azampay.co.tz');
        $this->appName = config('services.azampay.app_name');
        $this->clientId = config('services.azampay.client_id');
        $this->clientSecret = config('services.azampay.client_secret');
        $this->apiKey = config('services.azampay.api_key');
    }

    /**
     * Check if AzamPay service is properly configured
     */
    public function isConfigured(): bool
    {
        return !empty($this->appName) &&
               !empty($this->clientId) &&
               !empty($this->clientSecret) &&
               !empty($this->apiKey);
    }

    /**
     * Get access token for API authentication
     */
    private function getAccessToken(): ?string
    {
        if (!$this->isConfigured()) {
            Log::warning('AzamPay service not configured properly');
            return null;
        }

        if ($this->accessToken) {
            return $this->accessToken;
        }

        try {
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'X-API-Key' => $this->apiKey,
            ])->post($this->baseUrl . '/azampay/auth/login', [
                'appName' => $this->appName,
                'clientId' => $this->clientId,
                'clientSecret' => $this->clientSecret,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $this->accessToken = $data['data']['accessToken'] ?? null;
                return $this->accessToken;
            }

            Log::error('AzamPay authentication failed', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('AzamPay authentication error', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Initiate mobile money payment
     */
    public function initiateMobileMoneyPayment(Transaction $transaction, array $paymentDetails): array
    {
        $token = $this->getAccessToken();
        if (!$token) {
            return ['success' => false, 'message' => 'Authentication failed'];
        }

        try {
            $payload = [
                'accountNumber' => $paymentDetails['phone_number'],
                'amount' => $transaction->amount,
                'currency' => $transaction->currency,
                'externalId' => $transaction->transaction_id,
                'provider' => $this->mapMobileProvider($paymentDetails['provider']),
                'additionalProperties' => [
                    'description' => $transaction->description,
                    'fromChurch' => $transaction->fromChurch->name,
                    'toChurch' => $transaction->toChurch->name,
                ]
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json',
                'X-API-Key' => $this->apiKey,
            ])->post($this->baseUrl . '/azampay/mno/checkout', $payload);

            if ($response->successful()) {
                $data = $response->json();
                return [
                    'success' => true,
                    'transaction_id' => $data['transactionId'] ?? null,
                    'reference' => $data['reference'] ?? null,
                    'message' => $data['message'] ?? 'Payment initiated successfully',
                    'data' => $data
                ];
            }

            return [
                'success' => false,
                'message' => 'Payment initiation failed',
                'error' => $response->json()
            ];

        } catch (\Exception $e) {
            Log::error('AzamPay mobile money payment error', [
                'transaction_id' => $transaction->transaction_id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Payment processing error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Initiate bank transfer payment
     */
    public function initiateBankTransfer(Transaction $transaction, array $paymentDetails): array
    {
        $token = $this->getAccessToken();
        if (!$token) {
            return ['success' => false, 'message' => 'Authentication failed'];
        }

        try {
            $payload = [
                'bankCode' => $this->mapBankCode($paymentDetails['bank_name']),
                'accountNumber' => $paymentDetails['account_number'],
                'amount' => $transaction->amount,
                'currency' => $transaction->currency,
                'externalId' => $transaction->transaction_id,
                'additionalProperties' => [
                    'description' => $transaction->description,
                    'accountName' => $paymentDetails['account_name'] ?? '',
                    'fromChurch' => $transaction->fromChurch->name,
                    'toChurch' => $transaction->toChurch->name,
                ]
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json',
                'X-API-Key' => $this->apiKey,
            ])->post($this->baseUrl . '/azampay/bank/checkout', $payload);

            if ($response->successful()) {
                $data = $response->json();
                return [
                    'success' => true,
                    'transaction_id' => $data['transactionId'] ?? null,
                    'reference' => $data['reference'] ?? null,
                    'message' => $data['message'] ?? 'Bank transfer initiated successfully',
                    'data' => $data
                ];
            }

            return [
                'success' => false,
                'message' => 'Bank transfer initiation failed',
                'error' => $response->json()
            ];

        } catch (\Exception $e) {
            Log::error('AzamPay bank transfer error', [
                'transaction_id' => $transaction->transaction_id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Bank transfer processing error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Check transaction status
     */
    public function checkTransactionStatus(string $transactionId): array
    {
        $token = $this->getAccessToken();
        if (!$token) {
            return ['success' => false, 'message' => 'Authentication failed'];
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'X-API-Key' => $this->apiKey,
            ])->get($this->baseUrl . '/azampay/gettransactionstatus', [
                'pgReferenceId' => $transactionId
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return [
                    'success' => true,
                    'status' => $data['data']['transactionStatus'] ?? 'unknown',
                    'data' => $data
                ];
            }

            return [
                'success' => false,
                'message' => 'Status check failed',
                'error' => $response->json()
            ];

        } catch (\Exception $e) {
            Log::error('AzamPay status check error', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Status check error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Map mobile money provider names to AzamPay format
     */
    private function mapMobileProvider(string $provider): string
    {
        return match (strtolower($provider)) {
            'vodacom m-pesa', 'vodacom', 'm-pesa' => 'Vodacom',
            'airtel money', 'airtel' => 'Airtel',
            'tigo pesa', 'tigo' => 'Tigo',
            'halotel' => 'Halotel',
            default => $provider,
        };
    }

    /**
     * Map bank names to AzamPay bank codes
     */
    private function mapBankCode(string $bankName): string
    {
        return match (strtoupper($bankName)) {
            'CRDB' => '01',
            'NMB' => '02',
            'NBC' => '03',
            'STANBIC' => '04',
            'EQUITY' => '05',
            'DTB' => '06',
            'BOA' => '07',
            default => '01', // Default to CRDB
        };
    }

    /**
     * Process webhook callback from AzamPay
     */
    public function processCallback(array $callbackData): bool
    {
        try {
            $externalId = $callbackData['externalId'] ?? null;
            $status = $callbackData['status'] ?? null;
            $transactionId = $callbackData['transactionId'] ?? null;

            if (!$externalId) {
                Log::warning('AzamPay callback missing external ID', $callbackData);
                return false;
            }

            $transaction = Transaction::where('transaction_id', $externalId)->first();
            if (!$transaction) {
                Log::warning('Transaction not found for AzamPay callback', [
                    'external_id' => $externalId,
                    'callback_data' => $callbackData
                ]);
                return false;
            }

            // Update transaction with provider response
            $transaction->update([
                'provider_transaction_id' => $transactionId,
                'provider_response' => $callbackData,
            ]);

            // Update transaction status based on callback
            switch (strtolower($status)) {
                case 'success':
                case 'completed':
                    $transaction->complete();
                    break;
                case 'failed':
                case 'error':
                    $transaction->fail('Payment failed via AzamPay');
                    break;
                case 'pending':
                    // Keep as processing
                    break;
            }

            return true;

        } catch (\Exception $e) {
            Log::error('AzamPay callback processing error', [
                'callback_data' => $callbackData,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
