{{-- Language Switcher Component --}}
@props(['position' => 'top-right', 'size' => 'normal'])

@php
    $currentLocale = app()->getLocale();
    $locales = config('app.locale_names');
    $currentLocaleInfo = $locales[$currentLocale] ?? $locales['en'];
    
    $sizeClasses = match($size) {
        'small' => 'px-2 py-1 text-xs',
        'large' => 'px-4 py-3 text-base',
        default => 'px-3 py-2 text-sm'
    };
    
    $positionClasses = match($position) {
        'top-left' => 'left-0 top-full mt-2',
        'top-right' => 'right-0 top-full mt-2',
        'bottom-left' => 'left-0 bottom-full mb-2',
        'bottom-right' => 'right-0 bottom-full mb-2',
        default => 'right-0 top-full mt-2'
    };
@endphp

<div x-data="{ open: false }" class="relative inline-block text-left">
    <!-- Language Button -->
    <button @click="open = !open"
            type="button"
            class="inline-flex items-center justify-center {{ $sizeClasses }} border border-gray-300 rounded-lg shadow-sm bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            aria-expanded="false"
            aria-haspopup="true">
        <span class="mr-2 text-lg">{{ $currentLocaleInfo['flag'] }}</span>
        <span class="font-medium">{{ $currentLocaleInfo['native'] }}</span>
        <i class="fas fa-chevron-down ml-2 text-xs text-gray-400" :class="{ 'rotate-180': open }"></i>
    </button>

    <!-- Dropdown Menu -->
    <div x-show="open"
         @click.away="open = false"
         x-transition:enter="transition ease-out duration-100"
         x-transition:enter-start="transform opacity-0 scale-95"
         x-transition:enter-end="transform opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="transform opacity-100 scale-100"
         x-transition:leave-end="transform opacity-0 scale-95"
         class="absolute {{ $positionClasses }} w-56 bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 z-50"
         role="menu"
         aria-orientation="vertical">
        
        <div class="py-1" role="none">
            <div class="px-4 py-2 border-b border-gray-100">
                <p class="text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {{ __('common.select_language') }}
                </p>
            </div>
            
            @foreach($locales as $locale => $info)
                <button onclick="switchLanguage('{{ $locale }}')"
                        class="group flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200 {{ $currentLocale === $locale ? 'bg-blue-50 text-blue-700' : '' }}"
                        role="menuitem">
                    <span class="mr-3 text-lg">{{ $info['flag'] }}</span>
                    <div class="flex flex-col items-start flex-1">
                        <span class="font-medium">{{ $info['native'] }}</span>
                        <span class="text-xs text-gray-500">{{ $info['name'] }}</span>
                    </div>
                    @if($currentLocale === $locale)
                        <i class="fas fa-check text-blue-600 ml-2"></i>
                    @endif
                </button>
            @endforeach
        </div>
    </div>
</div>

<style>
    .rotate-180 {
        transform: rotate(180deg);
    }
</style>
