<?php

namespace App\Enums;

enum PaymentMethod: string
{
    case BANK_TRANSFER = 'bank_transfer';
    case MOBILE_MONEY = 'mobile_money';
    case CASH = 'cash';

    public function getLabel(): string
    {
        return match ($this) {
            self::BANK_TRANSFER => 'Bank Transfer',
            self::MOBILE_MONEY => 'Mobile Money',
            self::CASH => 'Cash',
        };
    }

    public function getProviders(): array
    {
        return match ($this) {
            self::BANK_TRANSFER => ['CRDB', 'NMB', 'NBC', 'Stanbic', 'Equity', 'DTB', 'BOA'],
            self::MOBILE_MONEY => ['Vodacom M-Pesa', 'Airtel Money', 'Tigo Pesa', 'Halotel'],
            self::CASH => ['Manual Entry'],
        };
    }

    public function requiresOnlineProcessing(): bool
    {
        return match ($this) {
            self::BANK_TRANSFER, self::MOBILE_MONEY => true,
            self::CASH => false,
        };
    }
}
