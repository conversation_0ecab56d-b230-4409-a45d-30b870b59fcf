<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ChurchReportExport implements FromArray, WithHeadings, WithStyles, WithTitle
{
    protected $reportData;

    public function __construct($reportData)
    {
        $this->reportData = $reportData;
    }

    /**
     * @return array
     */
    public function array(): array
    {
        $data = [];

        foreach ($this->reportData['churches'] as $church) {
            $row = [
                $church->name,
                $church->level->value,
                $church->location,
                $church->district ?? 'N/A',
                $church->region ?? 'N/A',
                $church->users->count(),
                $church->childChurches->count(),
                $church->parentChurch ? $church->parentChurch->name : 'N/A',
                $church->date_established ? $church->date_established->format('Y-m-d') : 'N/A',
            ];

            // Add demographics if included
            if ($this->reportData['options']['include_demographics']) {
                $row = array_merge($row, [
                    $church->youth_count ?? 0,
                    $church->young_adults_count ?? 0,
                    $church->children_count ?? 0,
                    $church->elders_count ?? 0,
                ]);
            }

            // Add leadership info if included
            if ($this->reportData['options']['include_leadership']) {
                $leaders = $church->leaders->map(function ($leader) {
                    if ($leader->user && $leader->user->full_name && $leader->role) {
                        return $leader->user->full_name . ' (' . $leader->role . ')';
                    } elseif ($leader->user && $leader->user->full_name) {
                        return $leader->user->full_name;
                    }
                    return null;
                })->filter()->implode(', ');

                $row[] = $leaders ?: 'No leaders assigned';
            }

            $data[] = $row;
        }

        return $data;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        $headings = [
            'Church Name',
            'Level',
            'Location',
            'District',
            'Region',
            'Total Users',
            'Sub Churches',
            'Parent Church',
            'Date Established',
        ];

        if ($this->reportData['options']['include_demographics']) {
            $headings = array_merge($headings, [
                'Youth (13-25)',
                'Young Adults (26-40)',
                'Children (0-12)',
                'Elders (41+)',
            ]);
        }

        if ($this->reportData['options']['include_leadership']) {
            $headings[] = 'Leadership';
        }

        return $headings;
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true]],
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return ucfirst($this->reportData['level']->value) . ' Churches Report';
    }
}
