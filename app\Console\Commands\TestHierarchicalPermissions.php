<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Church;
use App\Services\ChurchHierarchyService;
use App\Enums\ChurchLevel;

class TestHierarchicalPermissions extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'test:hierarchy-permissions {user_email?}';

    /**
     * The console command description.
     */
    protected $description = 'Test hierarchical permissions for a specific user';

    protected $hierarchyService;

    public function __construct(ChurchHierarchyService $hierarchyService)
    {
        parent::__construct();
        $this->hierarchyService = $hierarchyService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userEmail = $this->argument('user_email') ?? '<EMAIL>';
        
        $user = User::where('email', $userEmail)->first();
        
        if (!$user) {
            $this->error("User with email '{$userEmail}' not found.");
            $this->info("Available users:");
            User::all()->each(function ($u) {
                $this->line("  - {$u->email} ({$u->church->name} - {$u->church->level->value})");
            });
            return 1;
        }

        $this->info("🔍 Testing Hierarchical Permissions for: {$user->full_name}");
        $this->info("📧 Email: {$user->email}");
        $this->info("⛪ Church: {$user->church->name} ({$user->church->level->value})");
        $this->info("🎭 Roles: " . $user->roles->pluck('name')->join(', '));
        $this->info("🔑 Permissions: " . $user->getAllPermissions()->pluck('name')->join(', '));
        $this->line('');

        // Test 1: Churches User Can Access
        $this->info("1. 🏛️  Churches User Can Access:");
        $accessibleChurches = $this->hierarchyService->getChurchesUserCanAccess($user);
        
        if ($accessibleChurches->isEmpty()) {
            $this->warn("   ❌ No accessible churches found");
        } else {
            $accessibleChurches->each(function ($church) {
                $this->line("   ✅ {$church->name} ({$church->level->value}) - {$church->location}");
            });
        }
        $this->line('');

        // Test 2: Churches User Can Manage
        $this->info("2. ⚙️  Churches User Can Manage:");
        $manageableChurches = $this->hierarchyService->getChurchesUserCanManage($user);
        
        if ($manageableChurches->isEmpty()) {
            $this->warn("   ❌ No manageable churches found");
        } else {
            $manageableChurches->each(function ($church) {
                $this->line("   ✅ {$church->name} ({$church->level->value}) - {$church->location}");
            });
        }
        $this->line('');

        // Test 3: Users in Accessible Churches
        $this->info("3. 👥 Users in Accessible Churches:");
        $accessibleChurchIds = $accessibleChurches->pluck('id')->toArray();
        $accessibleUsers = User::whereIn('church_id', $accessibleChurchIds)->get();
        
        if ($accessibleUsers->isEmpty()) {
            $this->warn("   ❌ No accessible users found");
        } else {
            $accessibleUsers->each(function ($u) {
                $this->line("   ✅ {$u->full_name} ({$u->email}) - {$u->church->name}");
            });
        }
        $this->line('');

        // Test 4: Permission Checks
        $this->info("4. 🔐 Permission Checks:");
        $permissions = [
            'manage-churches',
            'create-churches', 
            'edit-churches',
            'manage-users',
            'create-users',
            'edit-users',
            'view-users',
            'manage-system'
        ];

        foreach ($permissions as $permission) {
            $hasPermission = $user->hasPermissionTo($permission);
            $icon = $hasPermission ? '✅' : '❌';
            $this->line("   {$icon} {$permission}");
        }
        $this->line('');

        // Test 5: Church Creation Permissions
        $this->info("5. 🏗️  Church Creation Permissions:");
        $levels = [
            ChurchLevel::NATIONAL,
            ChurchLevel::REGIONAL,
            ChurchLevel::LOCAL,
            ChurchLevel::PARISH,
            ChurchLevel::BRANCH
        ];

        foreach ($levels as $level) {
            $canCreate = $this->hierarchyService->canCreateChurchAtLevel($user, $level, $user->church()->first());
            $icon = $canCreate ? '✅' : '❌';
            $this->line("   {$icon} Can create {$level->value} level church");
        }
        $this->line('');

        // Test 6: Specific Church Access Test
        $this->info("6. 🎯 Specific Church Access Test:");
        $allChurches = Church::all();
        
        foreach ($allChurches as $church) {
            $canAccess = $accessibleChurches->contains('id', $church->id);
            $canManage = $manageableChurches->contains('id', $church->id);
            
            $accessIcon = $canAccess ? '👁️' : '🚫';
            $manageIcon = $canManage ? '⚙️' : '🚫';
            
            $this->line("   {$accessIcon} {$manageIcon} {$church->name} ({$church->level->value})");
        }
        $this->line('');

        // Test 7: Security Recommendations
        $this->info("7. 🛡️  Security Analysis:");
        
        if ($user->hasRole('Super Admin')) {
            $this->warn("   ⚠️  Super Admin has access to ALL resources");
        } else {
            $this->info("   ✅ User has limited access based on church hierarchy");
        }

        if ($accessibleChurches->count() === Church::count()) {
            $this->warn("   ⚠️  User can access ALL churches - check permissions");
        } else {
            $this->info("   ✅ User access is properly restricted to hierarchy");
        }

        if ($user->hasPermissionTo('manage-system')) {
            $this->warn("   ⚠️  User has system-wide management permissions");
        }

        $this->line('');
        $this->info("🎉 Hierarchical Permission Test Completed!");
        
        return 0;
    }
}
