<?php

namespace Database\Factories;

use App\Models\Transaction;
use App\Models\Church;
use App\Models\User;
use App\Enums\TransactionStatus;
use App\Enums\TransactionType;
use App\Enums\PaymentMethod;
use Illuminate\Database\Eloquent\Factories\Factory;

class TransactionFactory extends Factory
{
    protected $model = Transaction::class;

    public function definition(): array
    {
        return [
            'transaction_id' => 'TXN-' . strtoupper($this->faker->bothify('????????')) . '-' . now()->format('YmdHis'),
            'reference_number' => 'REF-' . strtoupper($this->faker->bothify('??????')) . '-' . now()->format('Ymd'),
            'from_church_id' => Church::factory(),
            'to_church_id' => Church::factory(),
            'initiated_by_user_id' => User::factory(),
            'approved_by_user_id' => null,
            'amount' => $this->faker->randomFloat(2, 1000, 100000),
            'currency' => 'TZS',
            'type' => $this->faker->randomElement(TransactionType::cases()),
            'status' => $this->faker->randomElement(TransactionStatus::cases()),
            'description' => $this->faker->sentence(),
            'notes' => null,
            'contribution_id' => null,
            'payment_method' => $this->faker->randomElement(PaymentMethod::cases()),
            'payment_provider' => null,
            'payment_details' => [
                'phone_number' => '+255' . $this->faker->numerify('#########'),
                'provider' => $this->faker->randomElement(['mpesa', 'tigo_pesa', 'airtel_money'])
            ],
            'provider_transaction_id' => null,
            'provider_response' => null,
            'initiated_at' => now(),
            'approved_at' => null,
            'completed_at' => null,
            'failed_at' => null,
        ];
    }

    /**
     * Indicate that the transaction is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => TransactionStatus::PENDING,
            'initiated_at' => now(),
            'approved_at' => null,
            'completed_at' => null,
            'failed_at' => null,
        ]);
    }

    /**
     * Indicate that the transaction is processing.
     */
    public function processing(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => TransactionStatus::PROCESSING,
            'initiated_at' => now()->subMinutes(5),
            'approved_at' => now()->subMinutes(2),
            'completed_at' => null,
            'failed_at' => null,
        ]);
    }

    /**
     * Indicate that the transaction is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => TransactionStatus::COMPLETED,
            'initiated_at' => now()->subMinutes(10),
            'approved_at' => now()->subMinutes(5),
            'completed_at' => now(),
            'failed_at' => null,
        ]);
    }

    /**
     * Indicate that the transaction failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => TransactionStatus::FAILED,
            'initiated_at' => now()->subMinutes(10),
            'approved_at' => now()->subMinutes(5),
            'completed_at' => null,
            'failed_at' => now(),
        ]);
    }

    /**
     * Indicate that the transaction was cancelled.
     */
    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => TransactionStatus::CANCELLED,
            'initiated_at' => now()->subMinutes(10),
            'approved_at' => null,
            'completed_at' => null,
            'failed_at' => null,
        ]);
    }

    /**
     * Indicate that this is a revenue collection transaction.
     */
    public function revenueCollection(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => TransactionType::REVENUE_COLLECTION,
            'description' => 'Monthly revenue collection from ' . $this->faker->company . ' to ' . $this->faker->company,
        ]);
    }

    /**
     * Indicate that this is a contribution transaction.
     */
    public function contribution(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => TransactionType::CONTRIBUTION,
            'description' => 'Contribution for ' . $this->faker->words(3, true) . ' campaign',
        ]);
    }

    /**
     * Indicate that this is a mobile money transaction.
     */
    public function mobileMoney(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_method' => PaymentMethod::MOBILE_MONEY,
            'payment_details' => [
                'phone_number' => '+255' . $this->faker->numerify('#########'),
                'provider' => $this->faker->randomElement(['mpesa', 'tigo_pesa', 'airtel_money'])
            ],
        ]);
    }

    /**
     * Indicate that this is a bank transfer transaction.
     */
    public function bankTransfer(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_method' => PaymentMethod::BANK_TRANSFER,
            'payment_details' => [
                'bank_name' => $this->faker->company . ' Bank',
                'account_number' => $this->faker->numerify('##########'),
                'account_name' => $this->faker->name,
            ],
        ]);
    }

    /**
     * Indicate that this is a cash transaction.
     */
    public function cash(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_method' => PaymentMethod::CASH,
            'payment_details' => [
                'received_by' => $this->faker->name,
                'receipt_number' => 'CASH-' . $this->faker->numerify('######'),
            ],
        ]);
    }
}
