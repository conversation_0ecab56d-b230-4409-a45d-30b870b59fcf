<?php

namespace App\Observers;

use App\Models\Transaction;
use App\Events\TransactionCompleted;
use App\Services\NotificationService;
use App\Enums\TransactionStatus;
use Illuminate\Support\Facades\Log;

class TransactionObserver
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Handle the Transaction "updated" event.
     */
    public function updated(Transaction $transaction): void
    {
        // Check if the transaction status was changed to completed
        if ($transaction->isDirty('status') && 
            $transaction->status === TransactionStatus::COMPLETED) {
            
            // Fire the TransactionCompleted event
            event(new TransactionCompleted($transaction));
            
            // Send SMS notification to the transaction initiator
            $this->sendTransactionCompletedSMS($transaction);
            
            Log::info("Transaction completed notification sent", [
                'transaction_id' => $transaction->transaction_id,
                'user_id' => $transaction->initiated_by_user_id,
                'amount' => $transaction->amount
            ]);
        }
    }

    /**
     * Send SMS notification for completed transaction
     */
    private function sendTransactionCompletedSMS(Transaction $transaction): void
    {
        try {
            $user = $transaction->initiatedByUser;
            
            if (!$user || !$user->phone_number) {
                Log::warning("Cannot send transaction SMS: User or phone number not found", [
                    'transaction_id' => $transaction->transaction_id,
                    'user_id' => $transaction->initiated_by_user_id
                ]);
                return;
            }

            // Send transaction completion notification
            $this->notificationService->sendTransactionCompletedNotification($transaction, $user);
            
        } catch (\Exception $e) {
            Log::error("Failed to send transaction completion SMS", [
                'transaction_id' => $transaction->transaction_id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
