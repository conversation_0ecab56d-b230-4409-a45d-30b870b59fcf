@extends('layouts.app')

@section('title', $contribution->name)

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- <PERSON> Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ $contribution->name }}</h1>
                    <p class="mt-2 text-gray-600">{{ __('Contribution Campaign Details') }}</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('contributions.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-arrow-left mr-2"></i>{{ __('Back to Contributions') }}
                    </a>
                    @can('edit-contributions')
                        @if($contribution->created_by_church_id === auth()->user()->church_id)
                            <a href="{{ route('contributions.edit', $contribution) }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <i class="fas fa-edit mr-2"></i>{{ __('Edit') }}
                            </a>
                        @endif
                    @endcan
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Contribution Details -->
                <div class="bg-white shadow rounded-lg mb-8">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium text-gray-900">{{ __('Campaign Information') }}</h3>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-{{ $contribution->status->getColor() }}-100 text-{{ $contribution->status->getColor() }}-800">
                                {{ $contribution->status->getLabel() }}
                            </span>
                        </div>
                    </div>
                    <div class="px-6 py-6">
                        @if($contribution->description)
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-3">{{ __('Description') }}</label>
                                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                    <p class="text-gray-900">{{ $contribution->description }}</p>
                                </div>
                            </div>
                        @endif

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                                <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Type') }}</label>
                                <div class="flex space-x-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <i class="fas fa-tag mr-1"></i>{{ ucfirst($contribution->type) }}
                                    </span>
                                    @if($contribution->is_mandatory)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ __('Mandatory') }}
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                                <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Collection Scope') }}</label>
                                <p class="text-gray-900 font-medium">
                                    <i class="fas fa-church mr-2 text-green-600"></i>{{ ucfirst($contribution->collection_scope) }} Level Churches
                                </p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Start Date') }}</label>
                                <p class="text-gray-900 font-medium">
                                    <i class="fas fa-calendar-alt mr-2 text-gray-600"></i>{{ $contribution->start_date->format('F j, Y') }}
                                </p>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('End Date') }}</label>
                                <p class="text-gray-900 font-medium">
                                    <i class="fas fa-calendar-check mr-2 text-gray-600"></i>
                                    @if($contribution->end_date)
                                        {{ $contribution->end_date->format('F j, Y') }}
                                    @else
                                        {{ __('No end date specified') }}
                                    @endif
                                </p>
                            </div>
                        </div>

                        @if($contribution->instructions)
                            <div class="mt-6">
                                <label class="block text-sm font-medium text-gray-700 mb-3">{{ __('Instructions') }}</label>
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-info-circle text-blue-600"></i>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-blue-800">{{ $contribution->instructions }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-3">{{ __('Created By') }}</label>
                            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-user-circle text-gray-600 text-lg"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-gray-900 font-medium">{{ $contribution->createdByChurch->name }} ({{ $contribution->createdByChurch->level->value }})</p>
                                        <p class="text-sm text-gray-500 mt-1">{{ __('By') }}: {{ $contribution->createdByUser->full_name }} on {{ $contribution->created_at->format('M j, Y') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                </div>
            </div>

                <!-- Progress and Statistics -->
                @if($contribution->target_amount)
                    <div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-8">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                <i class="fas fa-chart-line mr-2 text-blue-600"></i>
                                {{ __('Campaign Progress') }}
                            </h3>
                        </div>
                        <div class="px-6 py-6">
                            <div class="mb-6">
                                <div class="flex justify-between items-center mb-3">
                                    <span class="text-sm font-medium text-gray-700">{{ __('Progress') }}</span>
                                    <span class="text-lg font-bold text-green-600">{{ number_format($contribution->getProgressPercentage(), 1) }}%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-4 mb-4">
                                    <div class="bg-gradient-to-r from-green-400 to-green-600 h-4 rounded-full transition-all duration-300"
                                         style="width: {{ $contribution->getProgressPercentage() }}%">
                                    </div>
                                </div>
                                <div class="grid grid-cols-3 gap-4 text-center">
                                    <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                                        <div class="text-2xl font-bold text-green-600">{{ number_format($contribution->collected_amount, 0) }} TZS</div>
                                        <div class="text-sm text-gray-600 mt-1">{{ __('Collected') }}</div>
                                    </div>
                                    <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                                        <div class="text-2xl font-bold text-blue-600">{{ number_format($contribution->target_amount, 0) }} TZS</div>
                                        <div class="text-sm text-gray-600 mt-1">{{ __('Target') }}</div>
                                    </div>
                                    <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                                        <div class="text-2xl font-bold text-yellow-600">{{ number_format($contribution->getRemainingAmount(), 0) }} TZS</div>
                                        <div class="text-sm text-gray-600 mt-1">{{ __('Remaining') }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Recent Transactions -->
                <div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-8">
                    <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-exchange-alt mr-2 text-blue-600"></i>
                            {{ __('Recent Transactions') }}
                        </h3>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {{ $transactions->total() }} {{ __('Total') }}
                        </span>
                    </div>
                    <div class="overflow-hidden">
                        @if($transactions->count() > 0)
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Date') }}</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Church') }}</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Amount') }}</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Status') }}</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Actions') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        @foreach($transactions as $transaction)
                                            <tr class="hover:bg-gray-50">
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $transaction->created_at->format('M j, Y') }}</td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm font-medium text-gray-900">{{ $transaction->fromChurch->name }}</div>
                                                    <div class="text-sm text-gray-500">{{ $transaction->fromChurch->level->value }}</div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">{{ number_format($transaction->amount, 2) }} TZS</td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-{{ $transaction->status->getColor() }}-100 text-{{ $transaction->status->getColor() }}-800">
                                                        {{ $transaction->status->getLabel() }}
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                    <a href="{{ route('transactions.show', $transaction) }}" class="inline-flex items-center p-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <div class="bg-white px-6 py-4 border-t border-gray-200">
                                {{ $transactions->links() }}
                            </div>
                        @else
                            <div class="text-center py-16">
                                <div class="mx-auto h-24 w-24 text-gray-300 mb-4">
                                    <i class="fas fa-exchange-alt text-6xl"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">{{ __('No transactions yet') }}</h3>
                                <p class="text-gray-500">{{ __('No contributions have been made to this campaign yet.') }}</p>
                            </div>
                        @endif
                    </div>
                </div>
        </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Quick Statistics -->
                <div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-chart-bar mr-2 text-blue-600"></i>
                            {{ __('Campaign Statistics') }}
                        </h3>
                    </div>
                    <div class="px-6 py-6">
                        <div class="grid grid-cols-2 gap-4 mb-6">
                            <div class="bg-blue-50 p-4 rounded-lg border border-blue-200 text-center">
                                <div class="text-2xl font-bold text-blue-600">{{ $statistics['total_transactions'] }}</div>
                                <div class="text-sm text-gray-600 mt-1">{{ __('Total Transactions') }}</div>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg border border-green-200 text-center">
                                <div class="text-2xl font-bold text-green-600">{{ $statistics['completed_transactions'] }}</div>
                                <div class="text-sm text-gray-600 mt-1">{{ __('Completed') }}</div>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-4 mb-6">
                            <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200 text-center">
                                <div class="text-2xl font-bold text-yellow-600">{{ $statistics['pending_transactions'] }}</div>
                                <div class="text-sm text-gray-600 mt-1">{{ __('Pending') }}</div>
                            </div>
                            <div class="bg-purple-50 p-4 rounded-lg border border-purple-200 text-center">
                                <div class="text-2xl font-bold text-purple-600">{{ $statistics['participating_churches'] }}</div>
                                <div class="text-sm text-gray-600 mt-1">{{ __('Churches Participating') }}</div>
                            </div>
                        </div>
                        @if($statistics['target_churches'] > 0)
                            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium text-gray-700">{{ __('Participation Rate') }}</span>
                                    <span class="text-sm font-bold text-gray-900">{{ number_format($statistics['participation_rate'], 1) }}%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                                    <div class="bg-gradient-to-r from-purple-400 to-purple-600 h-2 rounded-full transition-all duration-300"
                                         style="width: {{ $statistics['participation_rate'] }}%">
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500">{{ $statistics['participating_churches'] }} of {{ $statistics['target_churches'] }} churches</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Status Management -->
                @can('manage-contributions')
                    @if($contribution->created_by_church_id === auth()->user()->church_id)
                        <div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-6">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                    <i class="fas fa-cog mr-2 text-blue-600"></i>
                                    {{ __('Campaign Management') }}
                                </h3>
                            </div>
                            <div class="px-6 py-6">
                                <form method="POST" action="{{ route('contributions.update-status', $contribution) }}">
                                    @csrf
                                    @method('PATCH')
                                    <div class="mb-4">
                                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">{{ __('Change Status') }}</label>
                                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="status" name="status">
                                            <option value="active" {{ $contribution->status->value === 'active' ? 'selected' : '' }}>{{ __('Active') }}</option>
                                            <option value="completed" {{ $contribution->status->value === 'completed' ? 'selected' : '' }}>{{ __('Completed') }}</option>
                                            <option value="cancelled" {{ $contribution->status->value === 'cancelled' ? 'selected' : '' }}>{{ __('Cancelled') }}</option>
                                            <option value="expired" {{ $contribution->status->value === 'expired' ? 'selected' : '' }}>{{ __('Expired') }}</option>
                                        </select>
                                    </div>
                                    <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                                        <i class="fas fa-save mr-2"></i>{{ __('Update Status') }}
                                    </button>
                                </form>
                            </div>
                        </div>
                    @endif
                @endcan

                <!-- Campaign Timeline -->
                <div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-clock mr-2 text-blue-600"></i>
                            {{ __('Campaign Timeline') }}
                        </h3>
                    </div>
                    <div class="px-6 py-6">
                        <div class="flow-root">
                            <ul class="-mb-8">
                                <li>
                                    <div class="relative pb-8">
                                        <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                        <div class="relative flex space-x-3">
                                            <div>
                                                <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                                    <i class="fas fa-plus text-white text-xs"></i>
                                                </span>
                                            </div>
                                            <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                                <div>
                                                    <p class="text-sm text-gray-900 font-medium">{{ __('Campaign Created') }}</p>
                                                    <p class="text-sm text-gray-500">{{ $contribution->created_at->format('M j, Y H:i') }}</p>
                                                    <p class="text-xs text-gray-400">{{ __('By') }}: {{ $contribution->createdByUser->full_name }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>

                                <li>
                                    <div class="relative pb-8">
                                        @if($contribution->end_date)
                                            <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                        @endif
                                        <div class="relative flex space-x-3">
                                            <div>
                                                <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                                    <i class="fas fa-play text-white text-xs"></i>
                                                </span>
                                            </div>
                                            <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                                <div>
                                                    <p class="text-sm text-gray-900 font-medium">{{ __('Campaign Started') }}</p>
                                                    <p class="text-sm text-gray-500">{{ $contribution->start_date->format('M j, Y') }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>

                                @if($contribution->end_date)
                                    <li>
                                        <div class="relative">
                                            <div class="relative flex space-x-3">
                                                <div>
                                                    <span class="h-8 w-8 rounded-full {{ $contribution->end_date->isPast() ? 'bg-yellow-500' : 'bg-blue-500' }} flex items-center justify-center ring-8 ring-white">
                                                        <i class="fas {{ $contribution->end_date->isPast() ? 'fa-stop' : 'fa-flag-checkered' }} text-white text-xs"></i>
                                                    </span>
                                                </div>
                                                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                                    <div>
                                                        <p class="text-sm text-gray-900 font-medium">
                                                            @if($contribution->end_date->isPast())
                                                                {{ __('Campaign Ended') }}
                                                            @else
                                                                {{ __('Campaign Ends') }}
                                                            @endif
                                                        </p>
                                                        <p class="text-sm text-gray-500">{{ $contribution->end_date->format('M j, Y') }}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                @endif
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
