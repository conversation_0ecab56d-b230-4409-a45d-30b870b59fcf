<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('church_leaders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('church_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('position'); // e.g., Archbishop, Bishop, Pastor
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('church_leaders');
    }
};
