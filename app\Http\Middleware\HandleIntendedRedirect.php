<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class HandleIntendedRedirect
{
    /**
     * Handle an incoming request.
     * Store intended URL for unauthenticated users trying to access protected routes
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // If user is not authenticated and trying to access a protected route
        if (!Auth::check()) {
            // Only store intended URL for GET requests (not POST, PUT, DELETE, etc.)
            if ($request->method() === 'GET' && !$request->expectsJson()) {
                // Don't store login/logout/register pages as intended URLs
                $currentUrl = $request->fullUrl();
                $loginUrl = route('login');
                $logoutUrl = route('logout');

                if ($currentUrl !== $loginUrl &&
                    $currentUrl !== $logoutUrl &&
                    !str_contains($currentUrl, '/login') &&
                    !str_contains($currentUrl, '/logout') &&
                    !str_contains($currentUrl, '/register')) {

                    session(['url.intended' => $currentUrl]);
                }
            }

            // Redirect to login
            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Unauthenticated',
                    'redirect' => route('login')
                ], 401);
            }

            return redirect()->route('login')->with('info', 'Please log in to access this page.');
        }

        return $next($request);
    }
}
