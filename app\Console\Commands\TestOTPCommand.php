<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Services\OTPService;
use App\Services\NotificationService;

class TestOTPCommand extends Command
{
    protected $signature = 'otp:test {email} {--type=general}';
    protected $description = 'Test OTP generation and sending for a user';

    protected OTPService $otpService;
    protected NotificationService $notificationService;

    public function __construct(OTPService $otpService, NotificationService $notificationService)
    {
        parent::__construct();
        $this->otpService = $otpService;
        $this->notificationService = $notificationService;
    }

    public function handle()
    {
        $email = $this->argument('email');
        $type = $this->option('type');

        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error("User with email {$email} not found.");
            return 1;
        }

        try {
            $this->info("Generating {$type} OTP for user: {$user->full_name} ({$user->email})");
            
            $otp = $this->otpService->generateOTP($user, $type, 30);
            
            $this->info("OTP generated: {$otp}");
            
            // Send notification
            $this->notificationService->sendOTPNotification($user, $otp, $type);
            
            $this->info("OTP sent via email and SMS successfully!");
            
            // Show OTP status
            $activeOTPs = $this->otpService->getActiveOTPsForUser($user);
            $expiredOTPs = $this->otpService->getExpiredOTPsForUser($user);
            
            $this->info("\nOTP Status for {$user->full_name}:");
            $this->info("Active OTPs: " . $activeOTPs->count());
            $this->info("Expired OTPs: " . $expiredOTPs->count());
            
            if ($activeOTPs->count() > 0) {
                $this->table(
                    ['Type', 'OTP', 'Expires At', 'Expires In'],
                    $activeOTPs->map(function ($otp) {
                        return [
                            $otp->type,
                            $otp->otp,
                            $otp->expires_at->format('Y-m-d H:i:s'),
                            $otp->expires_at->diffForHumans()
                        ];
                    })->toArray()
                );
            }
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error("Failed to generate/send OTP: " . $e->getMessage());
            return 1;
        }
    }
}
