import './bootstrap';
import './session-manager';
import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

window.Pusher = Pusher;

window.Echo = new Echo({
    broadcaster: 'pusher',
    key: process.env.MIX_PUSHER_APP_KEY,
    wsHost: window.location.hostname,
    wsPort: 6001,
    forceTLS: false,
    disableStats: true,
});

// Only setup Echo for authenticated users
if (window.Laravel && window.Laravel.userId) {
    window.Echo.private(`user.${window.Laravel.userId}`)
        .listen('MessageSent', (e) => {
            // Enhanced toast notification with message type
            if (typeof showToast === 'function') {
                let messageType = 'message';
                let icon = '💬';

                if (e.message.is_announcement) {
                    messageType = 'announcement';
                    icon = '📢';
                } else if (e.message.is_group_message) {
                    messageType = 'group message';
                    icon = '👥';
                }

                const preview = e.message.content.length > 50
                    ? e.message.content.substring(0, 50) + '...'
                    : e.message.content;

                showToast(
                    `${icon} New ${messageType} from ${e.message.sender.full_name}: ${preview}`,
                    'info'
                );
            } else {
                // Fallback to alert if showToast is not available
                alert(`New message from ${e.message.sender.full_name}: ${e.message.content}`);
            }

            // Update message count badge if on messages page
            const messageBadge = document.querySelector('.message-count-badge');
            if (messageBadge) {
                const currentCount = parseInt(messageBadge.textContent) || 0;
                messageBadge.textContent = currentCount + 1;
                messageBadge.style.display = 'flex';
            }

            // Play notification sound (optional)
            if (window.Audio) {
                try {
                    const audio = new Audio('/sounds/notification.mp3');
                    audio.volume = 0.3;
                    audio.play().catch(() => {
                        // Ignore audio play errors (user interaction required)
                    });
                } catch (e) {
                    // Ignore audio errors
                }
            }
        });
}