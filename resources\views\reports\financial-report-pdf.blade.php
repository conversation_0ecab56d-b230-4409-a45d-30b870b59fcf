<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $report->report_name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2563eb;
            margin: 0;
            font-size: 24px;
        }
        .header h2 {
            color: #666;
            margin: 5px 0;
            font-size: 18px;
            font-weight: normal;
        }
        .report-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .report-info table {
            width: 100%;
            border-collapse: collapse;
        }
        .report-info td {
            padding: 5px 10px;
            border: none;
        }
        .report-info td:first-child {
            font-weight: bold;
            width: 30%;
        }
        .summary-section {
            margin-bottom: 30px;
        }
        .summary-section h3 {
            color: #2563eb;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .summary-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2563eb;
        }
        .summary-card h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 14px;
        }
        .summary-card .amount {
            font-size: 18px;
            font-weight: bold;
            color: #2563eb;
        }
        .transactions-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .transactions-table th,
        .transactions-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .transactions-table th {
            background: #2563eb;
            color: white;
            font-weight: bold;
        }
        .transactions-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        .amount-cell {
            text-align: right;
            font-weight: bold;
        }
        .positive {
            color: #28a745;
        }
        .negative {
            color: #dc3545;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Free Pentecostal Church of Tanzania</h1>
        <h2>{{ $report->report_name }}</h2>
    </div>

    <div class="report-info">
        <table>
            <tr>
                <td>Church:</td>
                <td>{{ $church->name }}</td>
            </tr>
            <tr>
                <td>Church Level:</td>
                <td>{{ $church->level->value }}</td>
            </tr>
            <tr>
                <td>Report Period:</td>
                <td>{{ $report->period_start->format('F j, Y') }} - {{ $report->period_end->format('F j, Y') }}</td>
            </tr>
            <tr>
                <td>Generated By:</td>
                <td>{{ $report->generatedByUser->full_name }}</td>
            </tr>
            <tr>
                <td>Generated On:</td>
                <td>{{ $generated_at->format('F j, Y \a\t H:i') }}</td>
            </tr>
        </table>
    </div>

    @if(isset($data['summary']))
    <div class="summary-section">
        <h3>Financial Summary</h3>
        <div class="summary-grid">
            <div class="summary-card">
                <h4>Incoming Revenue</h4>
                <div class="amount positive">{{ number_format($data['summary']['incoming_revenue'] ?? 0, 2) }} TZS</div>
            </div>
            <div class="summary-card">
                <h4>Outgoing Revenue</h4>
                <div class="amount negative">{{ number_format($data['summary']['outgoing_revenue'] ?? 0, 2) }} TZS</div>
            </div>
            <div class="summary-card">
                <h4>Net Revenue</h4>
                <div class="amount {{ ($data['summary']['net_revenue'] ?? 0) >= 0 ? 'positive' : 'negative' }}">
                    {{ number_format($data['summary']['net_revenue'] ?? 0, 2) }} TZS
                </div>
            </div>
            <div class="summary-card">
                <h4>Total Transactions</h4>
                <div class="amount">{{ number_format($data['summary']['total_transactions'] ?? 0) }}</div>
            </div>
            <div class="summary-card">
                <h4>Current Balance</h4>
                <div class="amount">{{ number_format($data['summary']['current_balance'] ?? 0, 2) }} TZS</div>
            </div>
            <div class="summary-card">
                <h4>Total Balance</h4>
                <div class="amount">{{ number_format($data['summary']['total_balance'] ?? 0, 2) }} TZS</div>
            </div>
        </div>
    </div>
    @endif

    @if(isset($data['transactions']) && count($data['transactions']) > 0)
    <div class="summary-section">
        <h3>Transaction Details</h3>
        <table class="transactions-table">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Transaction ID</th>
                    <th>From</th>
                    <th>To</th>
                    <th>Amount</th>
                    <th>Type</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                @foreach($data['transactions'] as $transaction)
                <tr>
                    <td>{{ $transaction['date'] ?? '' }}</td>
                    <td>{{ $transaction['transaction_id'] ?? '' }}</td>
                    <td>{{ $transaction['from_church'] ?? '' }}</td>
                    <td>{{ $transaction['to_church'] ?? '' }}</td>
                    <td class="amount-cell">{{ number_format($transaction['amount'] ?? 0, 2) }} TZS</td>
                    <td>{{ $transaction['type'] ?? '' }}</td>
                    <td>{{ $transaction['status'] ?? '' }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    @if(isset($data['monthly_breakdown']) && count($data['monthly_breakdown']) > 0)
    <div class="summary-section page-break">
        <h3>Monthly Breakdown</h3>
        <table class="transactions-table">
            <thead>
                <tr>
                    <th>Month</th>
                    <th>Incoming</th>
                    <th>Outgoing</th>
                    <th>Net</th>
                </tr>
            </thead>
            <tbody>
                @foreach($data['monthly_breakdown'] as $month)
                <tr>
                    <td>{{ $month['month'] ?? '' }}</td>
                    <td class="amount-cell positive">{{ number_format($month['incoming'] ?? 0, 2) }} TZS</td>
                    <td class="amount-cell negative">{{ number_format($month['outgoing'] ?? 0, 2) }} TZS</td>
                    <td class="amount-cell {{ ($month['net'] ?? 0) >= 0 ? 'positive' : 'negative' }}">
                        {{ number_format($month['net'] ?? 0, 2) }} TZS
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    @if(isset($data['contribution_summary']))
    <div class="summary-section">
        <h3>Contribution Summary</h3>
        <div class="summary-grid">
            <div class="summary-card">
                <h4>Total Contributions</h4>
                <div class="amount">{{ $data['contribution_summary']['total_contributions'] ?? 0 }}</div>
            </div>
            <div class="summary-card">
                <h4>Total Collected</h4>
                <div class="amount positive">{{ number_format($data['contribution_summary']['total_collected'] ?? 0, 2) }} TZS</div>
            </div>
            <div class="summary-card">
                <h4>Active Contributions</h4>
                <div class="amount">{{ $data['contribution_summary']['active_contributions'] ?? 0 }}</div>
            </div>
            <div class="summary-card">
                <h4>Average Contribution</h4>
                <div class="amount">{{ number_format($data['contribution_summary']['average_contribution'] ?? 0, 2) }} TZS</div>
            </div>
        </div>
    </div>
    @endif

    <div class="footer">
        <p>&copy; {{ date('Y') }} Free Pentecostal Church of Tanzania. All rights reserved.</p>
        <p>This report was generated automatically by the FPCT Management System.</p>
        <p>Report ID: {{ $report->id }} | Generated: {{ $generated_at->format('Y-m-d H:i:s') }}</p>
    </div>
</body>
</html>
