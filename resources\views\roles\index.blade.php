@extends('layouts.app')

@section('title', 'Role Management')
@section('page-title', 'Roles & Permissions')

@section('breadcrumbs')
    <li>
        <span class="mx-2">/</span>
        <span class="font-medium text-gray-900">Roles & Permissions</span>
    </li>
@endsection

@section('content')
<div class="min-h-screen bg-gray-50 py-8" x-data="{ showFilters: false }">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Role Management</h1>
                    <p class="mt-2 text-sm text-gray-600">Manage system roles and their permissions</p>
                </div>
                <div class="flex items-center space-x-3">
                    @can('manage-permissions')
                    <a href="{{ route('permissions.index') }}" 
                       class="inline-flex items-center px-4 py-2 border border-purple-300 rounded-lg text-sm font-medium text-purple-700 bg-purple-50 hover:bg-purple-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200">
                        <i class="fas fa-key mr-2"></i>
                        Manage Permissions
                    </a>
                    <a href="{{ route('roles.create') }}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5">
                        <i class="fas fa-plus mr-2"></i>
                        Create Role
                    </a>
                    @endcan
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="mb-8 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-shield text-2xl text-blue-600"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500">Total Roles</p>
                        <p class="text-lg font-semibold text-gray-900">{{ $roles->total() }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-key text-2xl text-green-600"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500">Total Permissions</p>
                        <p class="text-lg font-semibold text-gray-900">{{ $permissions->count() }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-users text-2xl text-purple-600"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500">Users with Roles</p>
                        <p class="text-lg font-semibold text-gray-900">{{ $roles->sum('users_count') }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-chart-line text-2xl text-orange-600"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500">Avg Permissions/Role</p>
                        <p class="text-lg font-semibold text-gray-900">
                            {{ $roles->count() > 0 ? round($roles->sum('permissions_count') / $roles->count(), 1) : 0 }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden mb-8">
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Search & Filter</h3>
                    <button @click="showFilters = !showFilters" 
                            class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-filter"></i>
                    </button>
                </div>
            </div>
            
            <div class="p-6" x-show="showFilters" x-transition>
                <form method="GET" action="{{ route('roles.index') }}" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search Roles</label>
                        <input type="text" 
                               id="search"
                               name="search" 
                               value="{{ request('search') }}"
                               placeholder="Search by role name..."
                               class="block w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label for="permission" class="block text-sm font-medium text-gray-700 mb-2">Filter by Permission</label>
                        <select name="permission" 
                                id="permission"
                                class="block w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">All Permissions</option>
                            @foreach($permissions as $permission)
                                <option value="{{ $permission->name }}" {{ request('permission') == $permission->name ? 'selected' : '' }}>
                                    {{ $permission->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div class="flex items-end space-x-2">
                        <button type="submit" 
                                class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-200">
                            <i class="fas fa-search mr-2"></i>
                            Search
                        </button>
                        <a href="{{ route('roles.index') }}" 
                           class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Roles Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            @forelse($roles as $role)
                <div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow duration-200">
                    <!-- Role Header -->
                    <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">{{ $role->name }}</h3>
                                <p class="text-sm text-gray-600">{{ $role->users_count }} users assigned</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                @can('manage-permissions')
                                <a href="{{ route('roles.edit', $role) }}" 
                                   class="text-blue-600 hover:text-blue-800 transition-colors duration-200"
                                   title="Edit Role">
                                    <i class="fas fa-edit"></i>
                                </a>
                                @if($role->users_count == 0)
                                <form action="{{ route('roles.destroy', $role) }}" 
                                      method="POST" 
                                      class="inline"
                                      onsubmit="return confirm('Are you sure you want to delete this role?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" 
                                            class="text-red-600 hover:text-red-800 transition-colors duration-200"
                                            title="Delete Role">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                @endif
                                @endcan
                            </div>
                        </div>
                    </div>

                    <!-- Role Content -->
                    <div class="p-6">
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Permissions ({{ $role->permissions_count }})</h4>
                            <div class="flex flex-wrap gap-1">
                                @forelse($role->permissions->take(6) as $permission)
                                    <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800">
                                        {{ $permission->name }}
                                    </span>
                                @empty
                                    <span class="text-sm text-gray-500">No permissions assigned</span>
                                @endforelse
                                @if($role->permissions->count() > 6)
                                    <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-600">
                                        +{{ $role->permissions->count() - 6 }} more
                                    </span>
                                @endif
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500">
                                <i class="fas fa-users mr-1"></i>
                                {{ $role->users_count }} users
                            </div>
                            <a href="{{ route('roles.show', $role) }}" 
                               class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                                View Details
                                <i class="fas fa-arrow-right ml-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-span-full">
                    <div class="text-center py-12">
                        <i class="fas fa-user-shield text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No roles found</h3>
                        <p class="text-gray-600 mb-4">Get started by creating your first role.</p>
                        @can('manage-permissions')
                        <a href="{{ route('roles.create') }}" 
                           class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-plus mr-2"></i>
                            Create Role
                        </a>
                        @endcan
                    </div>
                </div>
            @endforelse
        </div>

        <!-- Pagination -->
        @if($roles->hasPages())
            <div class="bg-white px-4 py-3 border border-gray-200 rounded-lg">
                {{ $roles->links() }}
            </div>
        @endif
    </div>
</div>
@endsection
