<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\OTPService;

class CleanupExpiredOTPsCommand extends Command
{
    protected $signature = 'otp:cleanup';
    protected $description = 'Clean up expired OTPs from the database';

    protected OTPService $otpService;

    public function __construct(OTPService $otpService)
    {
        parent::__construct();
        $this->otpService = $otpService;
    }

    public function handle()
    {
        $this->info('Cleaning up expired OTPs...');
        
        try {
            $deletedCount = $this->otpService->cleanupExpiredOTPs();
            
            if ($deletedCount > 0) {
                $this->info("Successfully cleaned up {$deletedCount} expired OTPs.");
            } else {
                $this->info('No expired OTPs found to clean up.');
            }
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error("Failed to clean up expired OTPs: " . $e->getMessage());
            return 1;
        }
    }
}
