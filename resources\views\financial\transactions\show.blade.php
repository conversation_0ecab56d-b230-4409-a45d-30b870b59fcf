@extends('layouts.app')

@section('title', __('Transaction Details'))

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ __('Transaction Details') }}</h1>
                    <p class="mt-2 text-gray-600">{{ $transaction->custom_id }}</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('transactions.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-arrow-left mr-2"></i>{{ __('Back to Transactions') }}
                    </a>
                    @if($transaction->receipt)
                        <a href="{{ route('receipts.show', $transaction->receipt) }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-receipt mr-2"></i>{{ __('View Receipt') }}
                        </a>
                    @elseif($transaction->status->value === 'completed')
                        @can('generate-receipts')
                            <form method="POST" action="{{ route('transactions.generate-receipt', $transaction) }}" style="display: inline;">
                                @csrf
                                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                    <i class="fas fa-receipt mr-2"></i>{{ __('Generate Receipt') }}
                                </button>
                            </form>
                        @endcan
                    @endif
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Transaction Details -->
            <div class="lg:col-span-2">
                <!-- Basic Information -->
                <div class="bg-white shadow rounded-lg mb-8">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                                <i class="fas fa-info-circle mr-2 text-blue-600"></i>
                                {{ __('Transaction Information') }}
                            </h3>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $transaction->status->value === 'completed' ? 'bg-green-100 text-green-800' : ($transaction->status->value === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                {{ $transaction->status->getLabel() }}
                            </span>
                        </div>
                    </div>
                    <div class="px-6 py-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Transaction ID') }}</label>
                                <p class="text-sm text-gray-900 font-mono">{{ $transaction->transaction_id }}</p>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Reference Number') }}</label>
                                <p class="text-sm text-gray-900 font-mono">{{ $transaction->reference_number }}</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                            <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                                <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Type') }}</label>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                    {{ $transaction->type->getLabel() }}
                                </span>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                                <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Amount') }}</label>
                                <p class="text-2xl font-bold text-green-600">{{ number_format($transaction->amount, 2) }} {{ $transaction->currency }}</p>
                            </div>
                        </div>

                        @if($transaction->description)
                            <div class="mt-6">
                                <label class="block text-sm font-medium text-gray-700 mb-3">{{ __('Description') }}</label>
                                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                    <p class="text-sm text-gray-900">{{ $transaction->description }}</p>
                                </div>
                            </div>
                        @endif

                        @if($transaction->contribution)
                            <div class="mt-6">
                                <label class="block text-sm font-medium text-gray-700 mb-3">{{ __('Related Contribution') }}</label>
                                <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-hand-holding-usd text-blue-600 mt-1"></i>
                                        </div>
                                        <div class="ml-3">
                                            <a href="{{ route('contributions.show', $transaction->contribution) }}" class="text-blue-700 hover:text-blue-900 font-medium text-lg">
                                                {{ $transaction->contribution->name }}
                                            </a>
                                            <p class="text-sm text-blue-600 mt-1">{{ $transaction->contribution->type }} - {{ $transaction->contribution->status->getLabel() }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if($transaction->notes)
                            <div class="mt-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Notes') }}</label>
                                <div class="bg-blue-50 border border-blue-200 rounded-md p-3">
                                    <p class="text-sm text-blue-800">{{ $transaction->notes }}</p>
                                </div>
                            </div>
                        @endif
                </div>
            </div>

            <!-- Church Information -->
            <div class="bg-white shadow rounded-lg mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900 flex items-center">
                        <i class="fas fa-church mr-2 text-blue-600"></i>
                        {{ __('Church Information') }}
                    </h3>
                </div>
                <div class="px-6 py-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-arrow-right text-red-400"></i>
                                </div>
                                <div class="ml-3">
                                    <h4 class="text-sm font-medium text-red-800 mb-2">{{ __('From Church') }}</h4>
                                    <h3 class="text-lg font-semibold text-gray-900">{{ $transaction->fromChurch->name }}</h3>
                                    <p class="text-sm text-gray-600 mt-1">{{ ucfirst($transaction->fromChurch->level->value) }} {{ __('Level') }}</p>
                                    <p class="text-sm text-gray-500">{{ $transaction->fromChurch->location }}</p>
                                    @if($transaction->fromChurch->district || $transaction->fromChurch->region)
                                    <p class="text-xs text-gray-400 mt-1">
                                        @if($transaction->fromChurch->district){{ $transaction->fromChurch->district }}@endif
                                        @if($transaction->fromChurch->district && $transaction->fromChurch->region), @endif
                                        @if($transaction->fromChurch->region){{ $transaction->fromChurch->region }}@endif
                                    </p>
                                    @endif
                                    @if($transaction->fromChurch->phone_number)
                                    <p class="text-xs text-gray-500 mt-1">
                                        <i class="fas fa-phone mr-1"></i>{{ $transaction->fromChurch->phone_number }}
                                    </p>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="bg-green-50 border-l-4 border-green-400 p-4 rounded-r-lg">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-arrow-left text-green-400"></i>
                                </div>
                                <div class="ml-3">
                                    <h4 class="text-sm font-medium text-green-800 mb-2">{{ __('To Church') }}</h4>
                                    <h3 class="text-lg font-semibold text-gray-900">{{ $transaction->toChurch->name }}</h3>
                                    <p class="text-sm text-gray-600 mt-1">{{ ucfirst($transaction->toChurch->level->value) }} {{ __('Level') }}</p>
                                    <p class="text-sm text-gray-500">{{ $transaction->toChurch->location }}</p>
                                    @if($transaction->toChurch->district || $transaction->toChurch->region)
                                    <p class="text-xs text-gray-400 mt-1">
                                        @if($transaction->toChurch->district){{ $transaction->toChurch->district }}@endif
                                        @if($transaction->toChurch->district && $transaction->toChurch->region), @endif
                                        @if($transaction->toChurch->region){{ $transaction->toChurch->region }}@endif
                                    </p>
                                    @endif
                                    @if($transaction->toChurch->phone_number)
                                    <p class="text-xs text-gray-500 mt-1">
                                        <i class="fas fa-phone mr-1"></i>{{ $transaction->toChurch->phone_number }}
                                    </p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

                <!-- Payment Information -->
                <div class="bg-white shadow rounded-lg mb-8">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                            <i class="fas fa-credit-card mr-2 text-blue-600"></i>
                            {{ __('Payment Information') }}
                        </h3>
                    </div>
                    <div class="px-6 py-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Payment Method') }}</label>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                                    <i class="fas fa-credit-card mr-2"></i>{{ $transaction->payment_method->getLabel() }}
                                </span>
                            </div>
                            @if($transaction->payment_provider)
                                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Payment Provider') }}</label>
                                    <p class="text-sm text-gray-900 font-medium">{{ $transaction->payment_provider }}</p>
                                </div>
                            @endif
                        </div>

                        @if($transaction->payment_details)
                            <div class="mt-6">
                                <label class="block text-sm font-medium text-gray-700 mb-3">{{ __('Payment Details') }}</label>
                                <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Detail') }}</th>
                                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Value') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            @foreach($transaction->payment_details as $key => $value)
                                                <tr class="hover:bg-gray-50">
                                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">{{ ucfirst(str_replace('_', ' ', $key)) }}</td>
                                                    <td class="px-4 py-3 text-sm text-gray-700">{{ $value }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        @endif

                        @if($transaction->provider_transaction_id)
                            <div class="mt-6">
                                <label class="block text-sm font-medium text-gray-700 mb-3">{{ __('Provider Transaction ID') }}</label>
                                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                    <p class="text-sm text-gray-900 font-mono break-all">{{ $transaction->provider_transaction_id }}</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Status Timeline -->
                <div class="bg-white shadow rounded-lg mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                            <i class="fas fa-clock mr-2 text-blue-600"></i>
                            {{ __('Transaction Timeline') }}
                        </h3>
                    </div>
                    <div class="px-6 py-6">
                        <div class="flow-root">
                            <ul class="-mb-8">
                                <li>
                                    <div class="relative pb-8">
                                        <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                        <div class="relative flex space-x-3">
                                            <div>
                                                <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                                    <i class="fas fa-plus text-white text-xs"></i>
                                                </span>
                                            </div>
                                            <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                                <div>
                                                    <p class="text-sm text-gray-900 font-medium">{{ __('Transaction Initiated') }}</p>
                                                    <p class="text-sm text-gray-500">{{ $transaction->created_at->format('M j, Y H:i') }}</p>
                                                    <p class="text-xs text-gray-400">{{ __('By') }}: {{ $transaction->createdByUser?->full_name ?? __('System') }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>

                                @if($transaction->approved_at)
                                    <li>
                                        <div class="relative pb-8">
                                            <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                            <div class="relative flex space-x-3">
                                                <div>
                                                    <span class="h-8 w-8 rounded-full bg-indigo-500 flex items-center justify-center ring-8 ring-white">
                                                        <i class="fas fa-check text-white text-xs"></i>
                                                    </span>
                                                </div>
                                                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                                    <div>
                                                        <p class="text-sm text-gray-900 font-medium">{{ __('Transaction Approved') }}</p>
                                                        <p class="text-sm text-gray-500">{{ $transaction->approved_at->format('M j, Y H:i') }}</p>
                                                        @if($transaction->approvedByUser)
                                                            <p class="text-xs text-gray-400">{{ __('By') }}: {{ $transaction->approvedByUser->full_name }}</p>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                @endif

                                @if($transaction->completed_at)
                                    <li>
                                        <div class="relative pb-8">
                                            <div class="relative flex space-x-3">
                                                <div>
                                                    <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                                        <i class="fas fa-check-circle text-white text-xs"></i>
                                                    </span>
                                                </div>
                                                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                                    <div>
                                                        <p class="text-sm text-gray-900 font-medium">{{ __('Transaction Completed') }}</p>
                                                        <p class="text-sm text-gray-500">{{ $transaction->completed_at->format('M j, Y H:i') }}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                @endif

                                @if($transaction->failed_at)
                                    <li>
                                        <div class="relative pb-8">
                                            <div class="relative flex space-x-3">
                                                <div>
                                                    <span class="h-8 w-8 rounded-full bg-red-500 flex items-center justify-center ring-8 ring-white">
                                                        <i class="fas fa-times text-white text-xs"></i>
                                                    </span>
                                                </div>
                                                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                                    <div>
                                                        <p class="text-sm text-gray-900 font-medium">{{ __('Transaction Failed') }}</p>
                                                        <p class="text-sm text-gray-500">{{ $transaction->failed_at->format('M j, Y H:i') }}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                @endif
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                @if($transaction->status->value === 'pending' && auth()->user() && $transaction->to_church_id === auth()->user()->church_id)
                    @can('approve-transactions')
                        <div class="bg-white shadow rounded-lg mb-6">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                                    <i class="fas fa-cog mr-2 text-blue-600"></i>
                                    {{ __('Actions') }}
                                </h3>
                            </div>
                            <div class="px-6 py-6">
                                <div class="space-y-3">
                                    <button type="button" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500" onclick="approveTransaction()">
                                        <i class="fas fa-check mr-2"></i>{{ __('Approve Transaction') }}
                                    </button>
                                    <button type="button" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" onclick="rejectTransaction()">
                                        <i class="fas fa-times mr-2"></i>{{ __('Reject Transaction') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endcan
                @endif

            @if($transaction->canBeCancelled() && $transaction->initiated_by_user_id === auth()->id())
                <div class="bg-white shadow rounded-lg mb-8">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-yellow-800 flex items-center">
                            <i class="fas fa-exclamation-triangle mr-2 text-yellow-600"></i>
                            {{ __('Cancel Transaction') }}
                        </h3>
                    </div>
                    <div class="px-6 py-6">
                        <p class="text-gray-600 mb-4">{{ __('You can cancel this transaction since it has not been processed yet.') }}</p>
                        <form method="POST" action="{{ route('transactions.cancel', $transaction) }}" onsubmit="return confirm('{{ __('Are you sure you want to cancel this transaction?') }}')">
                            @csrf
                            <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                                <i class="fas fa-ban mr-2"></i>{{ __('Cancel Transaction') }}
                            </button>
                        </form>
                    </div>
                </div>
            @endif

            <!-- Receipt Information -->
            @if($transaction->receipt)
                <div class="bg-white shadow rounded-lg mb-8">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                            <i class="fas fa-receipt mr-2 text-blue-600"></i>
                            {{ __('Receipt Information') }}
                        </h3>
                    </div>
                    <div class="px-6 py-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">{{ __('Receipt Number') }}</label>
                                <p class="text-lg font-semibold text-gray-900">{{ $transaction->receipt->receipt_number }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">{{ __('Generated') }}</label>
                                <p class="text-lg font-semibold text-gray-900">{{ $transaction->receipt->created_at->format('M j, Y H:i') }}</p>
                            </div>
                        </div>
                        <div class="flex space-x-3">
                            <a href="{{ route('receipts.show', $transaction->receipt) }}" class="inline-flex items-center px-4 py-2 border border-blue-300 rounded-md shadow-sm text-sm font-medium text-blue-700 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <i class="fas fa-eye mr-2"></i>{{ __('View Receipt') }}
                            </a>
                            <a href="{{ route('receipts.pdf', $transaction->receipt) }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                                <i class="fas fa-download mr-2"></i>{{ __('Download PDF') }}
                            </a>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Approval Modal -->
<div id="approvalModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="approval-modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onclick="closeApprovalModal()"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-check text-green-600"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900" id="approval-modal-title">{{ __('Approve Transaction') }}</h3>
                        <div class="mt-4">
                            <p class="text-sm text-gray-600 mb-4">{{ __('Are you sure you want to approve this transaction?') }}</p>
                            <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-info-circle text-blue-400"></i>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm text-blue-700">
                                            <p><strong>{{ __('Amount') }}:</strong> {{ number_format($transaction->amount, 2) }} {{ $transaction->currency }}</p>
                                            <p><strong>{{ __('From') }}:</strong> {{ $transaction->fromChurch->name }}</p>
                                            <p><strong>{{ __('To') }}:</strong> {{ $transaction->toChurch->name }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <form method="POST" action="{{ route('transactions.approve', $transaction) }}" class="inline">
                    @csrf
                    <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm">
                        {{ __('Approve') }}
                    </button>
                </form>
                <button type="button" onclick="closeApprovalModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    {{ __('Cancel') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Rejection Modal -->
<div id="rejectionModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="rejection-modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onclick="closeRejectionModal()"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <form method="POST" action="{{ route('transactions.reject', $transaction) }}">
                @csrf
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                            <i class="fas fa-times text-red-600"></i>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" id="rejection-modal-title">{{ __('Reject Transaction') }}</h3>
                            <div class="mt-4">
                                <label for="reason" class="block text-sm font-medium text-gray-700 mb-2">{{ __('Rejection Reason') }} <span class="text-red-500">*</span></label>
                                <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500" id="reason" name="reason" rows="3" required placeholder="{{ __('Please provide a reason for rejection...') }}"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                        {{ __('Reject') }}
                    </button>
                    <button type="button" onclick="closeRejectionModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        {{ __('Cancel') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
function approveTransaction() {
    document.getElementById('approvalModal').classList.remove('hidden');
}

function rejectTransaction() {
    document.getElementById('rejectionModal').classList.remove('hidden');
}

function closeApprovalModal() {
    document.getElementById('approvalModal').classList.add('hidden');
}

function closeRejectionModal() {
    document.getElementById('rejectionModal').classList.add('hidden');
}

// Close modals when pressing Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeApprovalModal();
        closeRejectionModal();
    }
});
</script>
@endpush
@endsection
