<?php

namespace App\Services;

use App\Models\Transaction;
use App\Models\Receipt;
use App\Models\User;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Mail;
use Barryvdh\DomPDF\Facade\Pdf;

class ReceiptService
{
    /**
     * Generate receipt for a transaction
     */
    public function generateReceipt(Transaction $transaction, ?User $issuedBy = null): Receipt
    {
        $receipt = Receipt::create([
            'receipt_number' => Receipt::generateReceiptNumber(),
            'transaction_id' => $transaction->id,
            'issued_to_church_id' => $transaction->from_church_id,
            'issued_by_church_id' => $transaction->to_church_id,
            'issued_by_user_id' => $issuedBy?->id ?? $transaction->approvedByUser?->id ?? $transaction->initiatedByUser->id,
            'amount' => $transaction->amount,
            'currency' => $transaction->currency,
            'description' => $transaction->description,
            'receipt_type' => $this->getReceiptType($transaction),
        ]);

        // Generate PDF
        $this->generateReceiptPDF($receipt);

        return $receipt;
    }

    /**
     * Generate PDF for receipt
     */
    public function generateReceiptPDF(Receipt $receipt): string
    {
        $transaction = $receipt->transaction;
        $fromChurch = $transaction->fromChurch;
        $toChurch = $transaction->toChurch;

        $data = [
            'receipt' => $receipt,
            'transaction' => $transaction,
            'fromChurch' => $fromChurch,
            'toChurch' => $toChurch,
            'issuedBy' => $receipt->issuedByUser,
            'generatedAt' => now(),
        ];

        $pdf = Pdf::loadView('receipts.transaction-receipt', $data);
        
        $filename = 'receipts/' . $receipt->receipt_number . '.pdf';
        $pdfContent = $pdf->output();
        
        Storage::disk('local')->put($filename, $pdfContent);
        
        $receipt->update(['pdf_path' => $filename]);
        
        return $filename;
    }

    /**
     * Email receipt to church leaders
     */
    public function emailReceipt(Receipt $receipt, array $emailAddresses = []): bool
    {
        try {
            if (empty($emailAddresses)) {
                // Get church leaders' emails
                $emailAddresses = $this->getChurchLeaderEmails($receipt->issuedToChurch);
            }

            if (empty($emailAddresses)) {
                \Log::warning('No email addresses provided for receipt', ['receipt_id' => $receipt->id]);
                return false;
            }

            // Ensure PDF exists, generate if missing
            if (!$receipt->pdf_path || !Storage::disk('local')->exists($receipt->pdf_path)) {
                \Log::info('PDF missing for receipt, generating...', ['receipt_id' => $receipt->id]);
                $this->generateReceiptPDF($receipt);
            }

            // Verify PDF exists before sending
            if (!$receipt->pdf_path || !Storage::disk('local')->exists($receipt->pdf_path)) {
                \Log::error('PDF generation failed for receipt', ['receipt_id' => $receipt->id]);
                throw new \Exception('Unable to generate PDF for receipt');
            }

            $pdfPath = storage_path('app/' . $receipt->pdf_path);

            // Send email with receipt attachment
            Mail::send('emails.receipt-notification', [
                'receipt' => $receipt,
                'transaction' => $receipt->transaction,
            ], function ($message) use ($receipt, $emailAddresses, $pdfPath) {
                $message->to($emailAddresses)
                        ->subject('Transaction Receipt - ' . $receipt->receipt_number);

                // Only attach PDF if file exists
                if (file_exists($pdfPath)) {
                    $message->attach($pdfPath, [
                        'as' => 'Receipt_' . $receipt->receipt_number . '.pdf',
                        'mime' => 'application/pdf',
                    ]);
                }
            });

            $receipt->markAsEmailed();

            \Log::info('Receipt emailed successfully', [
                'receipt_id' => $receipt->id,
                'email_count' => count($emailAddresses)
            ]);

            return true;

        } catch (\Exception $e) {
            \Log::error('Receipt email failed', [
                'receipt_id' => $receipt->id,
                'email_addresses' => $emailAddresses,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Get receipt type based on transaction
     */
    protected function getReceiptType(Transaction $transaction): string
    {
        return match ($transaction->type) {
            \App\Enums\TransactionType::CONTRIBUTION => 'contribution',
            \App\Enums\TransactionType::REVENUE_COLLECTION => 'payment',
            \App\Enums\TransactionType::TRANSFER => 'transfer',
            default => 'payment',
        };
    }

    /**
     * Get church leader email addresses
     */
    protected function getChurchLeaderEmails($church): array
    {
        return $church->leaders()
            ->with('user')
            ->get()
            ->pluck('user.email')
            ->filter()
            ->toArray();
    }

    /**
     * Generate bulk receipts for multiple transactions
     */
    public function generateBulkReceipts(array $transactionIds, ?User $issuedBy = null): array
    {
        $receipts = [];
        $errors = [];

        foreach ($transactionIds as $transactionId) {
            try {
                $transaction = Transaction::findOrFail($transactionId);
                $receipts[] = $this->generateReceipt($transaction, $issuedBy);
            } catch (\Exception $e) {
                $errors[] = [
                    'transaction_id' => $transactionId,
                    'error' => $e->getMessage()
                ];
            }
        }

        return [
            'receipts' => $receipts,
            'errors' => $errors,
            'success_count' => count($receipts),
            'error_count' => count($errors),
        ];
    }

    /**
     * Get receipt template data
     */
    public function getReceiptTemplateData(Receipt $receipt): array
    {
        $transaction = $receipt->transaction;
        
        return [
            'receipt_number' => $receipt->receipt_number,
            'date_issued' => $receipt->created_at->format('F j, Y'),
            'time_issued' => $receipt->created_at->format('g:i A'),
            'amount' => number_format($receipt->amount, 2),
            'currency' => $receipt->currency,
            'description' => $receipt->description,
            'payment_method' => $transaction->payment_method->getLabel(),
            'transaction_id' => $transaction->transaction_id,
            'reference_number' => $transaction->reference_number,
            'from_church' => [
                'name' => $transaction->fromChurch->name,
                'level' => $transaction->fromChurch->level->value,
                'location' => $transaction->fromChurch->location,
            ],
            'to_church' => [
                'name' => $transaction->toChurch->name,
                'level' => $transaction->toChurch->level->value,
                'location' => $transaction->toChurch->location,
            ],
            'issued_by' => [
                'name' => $receipt->issuedByUser->full_name,
                'role' => $receipt->issuedByUser->role,
                'church' => $receipt->issuedByChurch->name,
            ],
            'contribution' => $transaction->contribution ? [
                'name' => $transaction->contribution->name,
                'type' => $transaction->contribution->type,
            ] : null,
        ];
    }
}
