<?php

namespace App\Events;

use App\Models\Request;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;

class RequestStatusUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets;

    public $request;
    public $status;

    public function __construct(Request $request, string $status)
    {
        $this->request = $request;
        $this->status = $status;
    }

    public function broadcastOn()
    {
        return [
            new Channel('user.' . $this->request->user_id),
            new Channel('church.' . $this->request->church_id),
        ];
    }

    public function broadcastWith()
    {
        return [
            'request' => [
                'id' => $this->request->id,
                'type' => $this->request->type,
                'status' => $this->status,
                'church' => $this->request->church->name,
                'updated_at' => now()->toDateTimeString(),
            ],
        ];
    }
}
