<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Church;
use App\Models\Request;
use App\Models\Message;
use Illuminate\Console\Command;

class TestDashboard extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:dashboard';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test dashboard statistics';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('📊 Dashboard Statistics Test');
        $this->info('============================');

        // Get admin user
        $admin = User::where('email', '<EMAIL>')->first();

        if (!$admin) {
            $this->error('Admin user not found!');
            return 1;
        }

        $this->info("Testing for user: {$admin->full_name} ({$admin->email})");
        $this->info("User role: {$admin->role}");
        $this->info("User church: {$admin->church->name}");
        $this->info('');

        // Test basic counts
        $totalUsers = User::count();
        $activeUsers = User::where('is_active', true)->count();
        $totalChurches = Church::count();
        $pendingRequests = Request::where('status', 'pending')->count();
        $totalMessages = Message::count();
        $unreadMessages = $admin->unreadMessages()->count();

        $this->info('📈 Statistics:');
        $this->info("  Total Users: {$totalUsers}");
        $this->info("  Active Users: {$activeUsers}");
        $this->info("  Total Churches: {$totalChurches}");
        $this->info("  Pending Requests: {$pendingRequests}");
        $this->info("  Total Messages: {$totalMessages}");
        $this->info("  Unread Messages (Admin): {$unreadMessages}");

        $this->info('');
        $this->info('✅ Dashboard statistics are working correctly!');

        return 0;
    }
}
