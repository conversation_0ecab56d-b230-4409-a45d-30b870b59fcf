<?php

namespace App\Enums;

enum ChurchLevel: string
{
    case NATIONAL = 'National';
    case REGIONAL = 'Regional';
    case LOCAL = 'Local';
    case PARISH = 'Parish';
    case BRANCH = 'Branch';

    public static function getHierarchy(): array
    {
        return [
            self::NATIONAL->value => 1,
            self::REGIONAL->value => 2,
            self::LOCAL->value => 3,
            self::PARISH->value => 4,
            self::BRANCH->value => 5,
        ];
    }

    public function getLevel(): int
    {
        return self::getHierarchy()[$this->value];
    }

    public function canBeParentOf(ChurchLevel $childLevel): bool
    {
        return $this->getLevel() < $childLevel->getLevel();
    }

    public function getValidParentLevels(): array
    {
        $currentLevel = $this->getLevel();
        $validParents = [];
        
        foreach (self::getHierarchy() as $level => $order) {
            if ($order < $currentLevel) {
                $validParents[] = $level;
            }
        }
        
        return $validParents;
    }

    public function getValidChildLevels(): array
    {
        $currentLevel = $this->getLevel();
        $validChildren = [];
        
        foreach (self::getHierarchy() as $level => $order) {
            if ($order > $currentLevel) {
                $validChildren[] = $level;
            }
        }
        
        return $validChildren;
    }

    public function getImmediateParentLevel(): ?ChurchLevel
    {
        $currentLevel = $this->getLevel();
        
        foreach (self::cases() as $case) {
            if ($case->getLevel() === $currentLevel - 1) {
                return $case;
            }
        }
        
        return null;
    }

    public function getImmediateChildLevel(): ?ChurchLevel
    {
        $currentLevel = $this->getLevel();
        
        foreach (self::cases() as $case) {
            if ($case->getLevel() === $currentLevel + 1) {
                return $case;
            }
        }
        
        return null;
    }

    public static function getApprovalAuthority(ChurchLevel $requestingLevel): ?ChurchLevel
    {
        return match ($requestingLevel) {
            self::BRANCH, self::PARISH => self::LOCAL,
            self::LOCAL => self::REGIONAL,
            self::REGIONAL => self::NATIONAL,
            self::NATIONAL => null, // National level has no higher authority
        };
    }

    public function getRolesByLevel(): array
    {
        return match ($this) {
            self::NATIONAL => [
                'Archbishop',
                'National Assistant',
                'General Secretary',
                'National Treasurer',
                'National IT',
                'National HR'
            ],
            self::REGIONAL => [
                'Bishop',
                'Regional Secretary',
                'Regional Treasurer',
                'Regional IT'
            ],
            self::LOCAL => [
                'Pastor',
                'Local Secretary',
                'Local Treasurer',
                'Local IT'
            ],
            self::PARISH => [
                'Parish Pastor',
                'Parish Secretary',
                'Parish Treasurer',
                'Parish IT'
            ],
            self::BRANCH => [
                'Branch Pastor',
                'Branch Secretary',
                'Branch Treasurer',
                'Branch IT'
            ],
        };
    }

    /**
     * Get the next higher level for church upgrades
     */
    public function getNextLevel(): ?ChurchLevel
    {
        return match ($this) {
            self::BRANCH => self::PARISH,
            self::PARISH => self::LOCAL,
            self::LOCAL => self::REGIONAL,
            self::REGIONAL => self::NATIONAL,
            self::NATIONAL => null, // National is the highest level
        };
    }

    /**
     * Check if this level can create requests affecting the target level
     */
    public function canCreateRequestsFor(ChurchLevel $targetLevel): bool
    {
        // Can create requests for same level or lower levels
        return $this->getLevel() <= $targetLevel->getLevel();
    }

    /**
     * Check if this level can approve requests from the requesting level
     */
    public function canApproveRequestsFrom(ChurchLevel $requestingLevel): bool
    {
        // Can approve requests from lower levels only
        return $this->getLevel() < $requestingLevel->getLevel();
    }

    /**
     * Get role mapping for upgrading users when church level changes
     */
    public static function getRoleUpgradeMapping(): array
    {
        return [
            // From Branch to Parish
            'Branch Pastor' => 'Parish Pastor',
            'Branch Secretary' => 'Parish Secretary',
            'Branch Treasurer' => 'Parish Treasurer',
            'Branch IT' => 'Parish IT',

            // From Parish to Local
            'Parish Pastor' => 'Pastor',
            'Parish Secretary' => 'Local Secretary',
            'Parish Treasurer' => 'Local Treasurer',
            'Parish IT' => 'Local IT',

            // From Local to Regional
            'Pastor' => 'Bishop',
            'Local Secretary' => 'Regional Secretary',
            'Local Treasurer' => 'Regional Treasurer',
            'Local IT' => 'Regional IT',

            // From Regional to National
            'Bishop' => 'Archbishop',
            'Regional Secretary' => 'General Secretary',
            'Regional Treasurer' => 'National Treasurer',
            'Regional IT' => 'National IT',
        ];
    }
}
