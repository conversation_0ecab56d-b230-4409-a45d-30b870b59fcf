
<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['position' => 'top-right', 'size' => 'normal']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['position' => 'top-right', 'size' => 'normal']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $currentLocale = app()->getLocale();
    $locales = config('app.locale_names');
    $currentLocaleInfo = $locales[$currentLocale] ?? $locales['en'];
    
    $sizeClasses = match($size) {
        'small' => 'px-2 py-1 text-xs',
        'large' => 'px-4 py-3 text-base',
        default => 'px-3 py-2 text-sm'
    };
    
    $positionClasses = match($position) {
        'top-left' => 'left-0 top-full mt-2',
        'top-right' => 'right-0 top-full mt-2',
        'bottom-left' => 'left-0 bottom-full mb-2',
        'bottom-right' => 'right-0 bottom-full mb-2',
        default => 'right-0 top-full mt-2'
    };
?>

<div x-data="{ open: false }" class="relative inline-block text-left">
    <!-- Language Button -->
    <button @click="open = !open"
            type="button"
            class="inline-flex items-center justify-center <?php echo e($sizeClasses); ?> border border-gray-300 rounded-lg shadow-sm bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            aria-expanded="false"
            aria-haspopup="true">
        <span class="mr-2 text-lg"><?php echo e($currentLocaleInfo['flag']); ?></span>
        <span class="font-medium"><?php echo e($currentLocaleInfo['native']); ?></span>
        <i class="fas fa-chevron-down ml-2 text-xs text-gray-400" :class="{ 'rotate-180': open }"></i>
    </button>

    <!-- Dropdown Menu -->
    <div x-show="open"
         @click.away="open = false"
         x-transition:enter="transition ease-out duration-100"
         x-transition:enter-start="transform opacity-0 scale-95"
         x-transition:enter-end="transform opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="transform opacity-100 scale-100"
         x-transition:leave-end="transform opacity-0 scale-95"
         class="absolute <?php echo e($positionClasses); ?> w-56 bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 z-50"
         role="menu"
         aria-orientation="vertical">
        
        <div class="py-1" role="none">
            <div class="px-4 py-2 border-b border-gray-100">
                <p class="text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <?php echo e(__('common.select_language')); ?>

                </p>
            </div>
            
            <?php $__currentLoopData = $locales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $locale => $info): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <button onclick="switchLanguage('<?php echo e($locale); ?>')"
                        class="group flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200 <?php echo e($currentLocale === $locale ? 'bg-blue-50 text-blue-700' : ''); ?>"
                        role="menuitem">
                    <span class="mr-3 text-lg"><?php echo e($info['flag']); ?></span>
                    <div class="flex flex-col items-start flex-1">
                        <span class="font-medium"><?php echo e($info['native']); ?></span>
                        <span class="text-xs text-gray-500"><?php echo e($info['name']); ?></span>
                    </div>
                    <?php if($currentLocale === $locale): ?>
                        <i class="fas fa-check text-blue-600 ml-2"></i>
                    <?php endif; ?>
                </button>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</div>

<style>
    .rotate-180 {
        transform: rotate(180deg);
    }
</style>
<?php /**PATH C:\wamp64\www\fpct-system\resources\views/components/language-switcher.blade.php ENDPATH**/ ?>