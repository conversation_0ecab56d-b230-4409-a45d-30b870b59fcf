<?php

namespace App\Helpers;

class CustomIdHelper
{
    /**
     * Custom ID prefixes for different models
     */
    public const PREFIXES = [
        'User' => 'USR',
        'Church' => 'CHR',
        'Message' => 'MSG',
        'Request' => 'REQ',
        'Transaction' => 'TXN',
        'Contribution' => 'CTB',
        'Receipt' => 'RCP',
        'AuditLog' => 'AUD',
        'Otp' => 'OTP',
        'SmsLog' => 'SMS',
        'ApprovalWorkflow' => 'APW',
        'MessageRecipient' => 'MRC',
        'FinancialBalance' => 'FBL',
        'NotificationPreference' => 'NPF',
    ];

    /**
     * Get prefix for a model class
     */
    public static function getPrefixForModel(string $modelClass): string
    {
        $className = class_basename($modelClass);
        return self::PREFIXES[$className] ?? 'GEN';
    }

    /**
     * Generate a custom ID for a specific model
     */
    public static function generateId(string $modelClass): string
    {
        $prefix = self::getPrefixForModel($modelClass);
        $timestamp = now()->format('ymd');
        $random = strtoupper(\Illuminate\Support\Str::random(6));
        
        do {
            $customId = "{$prefix}-{$timestamp}-{$random}";
            $random = strtoupper(\Illuminate\Support\Str::random(6));
        } while ($modelClass::where('custom_id', $customId)->exists());
        
        return $customId;
    }

    /**
     * Validate custom ID format
     */
    public static function isValidFormat(string $customId): bool
    {
        // Format: PREFIX-YYMMDD-RANDOM6
        return preg_match('/^[A-Z]{3}-\d{6}-[A-Z0-9]{6}$/', $customId);
    }

    /**
     * Extract prefix from custom ID
     */
    public static function extractPrefix(string $customId): ?string
    {
        if (self::isValidFormat($customId)) {
            return substr($customId, 0, 3);
        }
        return null;
    }

    /**
     * Extract date from custom ID
     */
    public static function extractDate(string $customId): ?string
    {
        if (self::isValidFormat($customId)) {
            return substr($customId, 4, 6);
        }
        return null;
    }

    /**
     * Get model class from prefix
     */
    public static function getModelClassFromPrefix(string $prefix): ?string
    {
        $modelMap = array_flip(self::PREFIXES);
        return $modelMap[$prefix] ?? null;
    }
}
