<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\HudumaSMSService;
use App\Services\OTPService;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class TestSMSSandbox extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'sms:test-sandbox {phone?} {--message=} {--user-email=}';

    /**
     * The console command description.
     */
    protected $description = 'Test SMS functionality in Huduma SMS environment';

    protected $smsService;
    protected $otpService;

    public function __construct(HudumaSMSService $smsService, OTPService $otpService)
    {
        parent::__construct();
        $this->smsService = $smsService;
        $this->otpService = $otpService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Testing Huduma SMS Service');
        $this->info('=============================');
        
        // Test 1: Connection Test
        $this->info('1. Testing SMS Service Connection...');
        $connected = $this->smsService->testConnection();
        
        if ($connected) {
            $this->info('   ✅ SMS Service Connected Successfully');
        } else {
            $this->error('   ❌ SMS Service Connection Failed');
            return 1;
        }

        // Test 2: Basic SMS Test
        $phone = $this->argument('phone') ?? '+255712345678';
        $message = $this->option('message') ?? 'Test message from FPCT Church Management System (CMS) - ' . now()->format('H:i:s');
        
        $this->info("\n2. Testing Basic SMS Sending...");
        $this->info("   Phone: {$phone}");
        $this->info("   Message: {$message}");
        
        $sent = $this->smsService->sendSMS($phone, $message);
        
        if ($sent) {
            $this->info('   ✅ SMS Sent Successfully');
            $this->warn('   ⚠️  Note: In local development mode, SMS is simulated - no real SMS is delivered');
        } else {
            $this->error('   ❌ SMS Sending Failed');
        }

        // Test 3: OTP Generation and SMS
        $this->info("\n3. Testing OTP Generation and SMS...");
        
        $userEmail = $this->option('user-email') ?? '<EMAIL>';
        $user = User::where('email', $userEmail)->first();
        
        if (!$user) {
            $this->warn("   ⚠️  User with email {$userEmail} not found. Skipping OTP test.");
        } else {
            $this->info("   User: {$user->full_name} ({$user->email})");
            
            // Generate OTP
            $otp = $this->otpService->generatePasswordResetOTP($user);
            $this->info("   Generated OTP: {$otp}");
            
            // Send OTP via SMS
            $otpMessage = "Your FPCT password reset code is: {$otp}. Valid for 15 minutes.";
            $otpSent = $this->smsService->sendSMS($user->phone_number, $otpMessage);
            
            if ($otpSent) {
                $this->info('   ✅ OTP SMS Sent Successfully (Sandbox Mode)');
                
                // Test OTP validation
                $isValid = $this->otpService->validatePasswordResetOTP($user, $otp);
                $this->info("   ✅ OTP Validation: " . ($isValid ? 'Valid' : 'Invalid'));
            } else {
                $this->error('   ❌ OTP SMS Sending Failed');
            }
        }

        // Test 4: Configuration Check
        $this->info("\n4. Configuration Check...");
        $this->info("   Username: " . config('services.africastalking.username'));
        $this->info("   From: " . config('services.africastalking.from'));
        $this->info("   Callback URL: " . (config('services.africastalking.callback_url') ?? 'Not set'));
        
        // Test 5: Sandbox Information
        $this->info("\n📋 Sandbox Information:");
        $this->info("   • Sandbox Mode: Messages are simulated, not delivered to real phones");
        $this->info("   • Your Shortcode: 2673");
        $this->info("   • Callback URL: https://de629df8a3e5.ngrok-free.app/api/sms/callback");
        $this->info("   • To test real SMS: Switch to production account with real API key");
        
        // Test 6: Log Check
        $this->info("\n5. Checking Recent Logs...");
        $logFile = storage_path('logs/laravel.log');
        
        if (file_exists($logFile)) {
            $logs = file_get_contents($logFile);
            $recentLogs = substr($logs, -2000); // Last 2000 characters
            
            if (strpos($recentLogs, 'Africa\'s Talking') !== false) {
                $this->info('   ✅ SMS-related logs found');
            } else {
                $this->info('   ℹ️  No recent SMS logs found');
            }
        }

        $this->info("\n🎉 SMS Sandbox Test Completed!");
        $this->info("Check the logs for detailed information about SMS operations.");
        
        return 0;
    }
}
