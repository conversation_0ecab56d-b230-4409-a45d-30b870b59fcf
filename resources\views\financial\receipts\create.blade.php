@extends('layouts.app')

@section('title', __('Generate Receipt'))

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ __('Generate Receipt') }}</h1>
                    <p class="mt-2 text-gray-600">{{ __('Create receipts for completed transactions') }}</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('receipts.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-arrow-left mr-2"></i>{{ __('Back to Receipts') }}
                    </a>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Receipt Generation Form -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                            <i class="fas fa-receipt mr-2 text-blue-600"></i>
                            {{ __('Receipt Generation') }}
                        </h3>
                        <p class="mt-1 text-sm text-gray-500">{{ __('Select transactions to generate receipts for') }}</p>
                    </div>
                    <div class="px-6 py-6">
                        @if($transactions->count() > 0)
                            <form method="POST" action="{{ route('receipts.store') }}" id="receiptForm">
                                @csrf
                                
                                <!-- Bulk Selection -->
                                <div class="mb-6">
                                    <div class="flex items-center justify-between">
                                        <label class="text-sm font-medium text-gray-700">{{ __('Select Transactions') }}</label>
                                        <div class="flex space-x-2">
                                            <button type="button" onclick="selectAll()" class="text-sm text-blue-600 hover:text-blue-800">{{ __('Select All') }}</button>
                                            <button type="button" onclick="selectNone()" class="text-sm text-gray-600 hover:text-gray-800">{{ __('Select None') }}</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Transactions List -->
                                <div class="space-y-4">
                                    @foreach($transactions as $transaction)
                                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                                            <div class="flex items-start">
                                                <div class="flex items-center h-5">
                                                    <input type="checkbox" name="transaction_ids[]" value="{{ $transaction->id }}" class="transaction-checkbox focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                                </div>
                                                <div class="ml-3 flex-1">
                                                    <div class="flex items-center justify-between">
                                                        <div>
                                                            <h4 class="text-sm font-medium text-gray-900">{{ $transaction->transaction_id }}</h4>
                                                            <p class="text-sm text-gray-500">{{ $transaction->created_at->format('M j, Y H:i') }}</p>
                                                        </div>
                                                        <div class="text-right">
                                                            <p class="text-lg font-semibold text-green-600">{{ number_format($transaction->amount, 2) }} {{ $transaction->currency }}</p>
                                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                                {{ $transaction->status->getLabel() }}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="mt-2 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                                                        <div>
                                                            <span class="font-medium">{{ __('From') }}:</span> {{ $transaction->fromChurch->name }}
                                                        </div>
                                                        <div>
                                                            <span class="font-medium">{{ __('To') }}:</span> {{ $transaction->toChurch->name }}
                                                        </div>
                                                        @if($transaction->contribution)
                                                            <div class="md:col-span-2">
                                                                <span class="font-medium">{{ __('Contribution') }}:</span> {{ $transaction->contribution->name }}
                                                            </div>
                                                        @endif
                                                        @if($transaction->description)
                                                            <div class="md:col-span-2">
                                                                <span class="font-medium">{{ __('Description') }}:</span> {{ $transaction->description }}
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>

                                <!-- Submit Button -->
                                <div class="mt-8 flex justify-end">
                                    <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        <i class="fas fa-receipt mr-2"></i>{{ __('Generate Receipts') }}
                                    </button>
                                </div>
                            </form>
                        @else
                            <div class="text-center py-12">
                                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-receipt text-gray-400 text-3xl"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">{{ __('No Transactions Available') }}</h3>
                                <p class="text-gray-500 mb-6">{{ __('All completed transactions already have receipts generated.') }}</p>
                                <a href="{{ route('transactions.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                    <i class="fas fa-list mr-2"></i>{{ __('View Transactions') }}
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Receipt Information -->
                <div class="bg-white shadow rounded-lg mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                            <i class="fas fa-info-circle mr-2 text-blue-600"></i>
                            {{ __('Receipt Information') }}
                        </h3>
                    </div>
                    <div class="px-6 py-6">
                        <div class="space-y-4">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">{{ __('Church Information') }}</h4>
                                <p class="text-sm text-gray-600 mt-1">{{ $church->name }}</p>
                                <p class="text-sm text-gray-500">{{ $church->level->value }} Level</p>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">{{ __('Available Transactions') }}</h4>
                                <p class="text-sm text-gray-600 mt-1">{{ $transactions->count() }} {{ __('transactions ready for receipt generation') }}</p>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">{{ __('Receipt Format') }}</h4>
                                <p class="text-sm text-gray-600 mt-1">{{ __('PDF receipts will be generated automatically') }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                            <i class="fas fa-bolt mr-2 text-blue-600"></i>
                            {{ __('Quick Actions') }}
                        </h3>
                    </div>
                    <div class="px-6 py-6">
                        <div class="space-y-3">
                            <a href="{{ route('receipts.index') }}" class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                <i class="fas fa-list mr-2"></i>{{ __('View All Receipts') }}
                            </a>
                            <a href="{{ route('transactions.index') }}" class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                <i class="fas fa-exchange-alt mr-2"></i>{{ __('View Transactions') }}
                            </a>
                            <a href="{{ route('financial.dashboard') }}" class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                <i class="fas fa-chart-line mr-2"></i>{{ __('Financial Dashboard') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function selectAll() {
    const checkboxes = document.querySelectorAll('.transaction-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
}

function selectNone() {
    const checkboxes = document.querySelectorAll('.transaction-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
}

// Form validation
document.getElementById('receiptForm').addEventListener('submit', function(e) {
    const checkedBoxes = document.querySelectorAll('.transaction-checkbox:checked');
    if (checkedBoxes.length === 0) {
        e.preventDefault();
        alert('{{ __("Please select at least one transaction to generate receipts for.") }}');
    }
});
</script>
@endpush
@endsection
