<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FinancialReport extends Model
{
    protected $fillable = [
        'report_name',
        'report_type',
        'church_id',
        'generated_by_user_id',
        'period_start',
        'period_end',
        'report_data',
        'filters',
        'pdf_path',
        'excel_path',
        'status',
    ];

    protected $casts = [
        'period_start' => 'date',
        'period_end' => 'date',
        'report_data' => 'array',
        'filters' => 'array',
    ];

    // Relationships
    public function church(): BelongsTo
    {
        return $this->belongsTo(Church::class);
    }

    public function generatedByUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'generated_by_user_id');
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('report_type', $type);
    }

    public function scopeForPeriod($query, $startDate, $endDate)
    {
        return $query->where('period_start', '>=', $startDate)
                    ->where('period_end', '<=', $endDate);
    }

    // Business Logic Methods
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    public function hasPdf(): bool
    {
        return !empty($this->pdf_path) && file_exists(storage_path('app/' . $this->pdf_path));
    }

    public function hasExcel(): bool
    {
        return !empty($this->excel_path) && file_exists(storage_path('app/' . $this->excel_path));
    }

    public function getReportTypeLabel(): string
    {
        return match ($this->report_type) {
            'monthly' => 'Monthly Report',
            'quarterly' => 'Quarterly Report',
            'annual' => 'Annual Report',
            'custom' => 'Custom Report',
            'contribution_summary' => 'Contribution Summary',
            default => 'Financial Report',
        };
    }

    public function getPeriodLabel(): string
    {
        if ($this->period_start->format('Y-m') === $this->period_end->format('Y-m')) {
            return $this->period_start->format('F Y');
        }
        
        return $this->period_start->format('M j, Y') . ' - ' . $this->period_end->format('M j, Y');
    }
}
