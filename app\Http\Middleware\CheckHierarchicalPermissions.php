<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\ChurchHierarchyService;
use App\Models\User;
use App\Models\Church;

class CheckHierarchicalPermissions
{
    protected $hierarchyService;

    public function __construct(ChurchHierarchyService $hierarchyService)
    {
        $this->hierarchyService = $hierarchyService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $resource = 'general'): Response
    {
        $user = $request->user();

        if (!$user) {
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }
            return redirect()->route('login');
        }

        // Get the target resource ID from route parameters
        $resourceId = $this->getResourceId($request, $resource);

        if ($resourceId && !$this->canAccessResource($user, $resource, $resourceId)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Access denied. You do not have permission to access this resource within your church hierarchy.'
                ], 403);
            }
            
            abort(403, 'You do not have permission to access this resource within your church hierarchy.');
        }

        return $next($request);
    }

    /**
     * Get the resource ID from route parameters
     */
    private function getResourceId(Request $request, string $resource): ?int
    {
        // Try different parameter names based on resource type
        $parameterNames = match ($resource) {
            'church' => ['church', 'church_id'],
            'user' => ['user', 'user_id'],
            'request' => ['request', 'request_id'],
            'message' => ['message', 'message_id'],
            default => ['id']
        };

        foreach ($parameterNames as $paramName) {
            if ($request->route($paramName)) {
                $param = $request->route($paramName);
                return is_object($param) ? $param->id : (int) $param;
            }
        }

        return null;
    }

    /**
     * Check if user can access the specific resource
     */
    private function canAccessResource(User $user, string $resource, int $resourceId): bool
    {
        return match ($resource) {
            'church' => $this->canAccessChurch($user, $resourceId),
            'user' => $this->canAccessUser($user, $resourceId),
            'request' => $this->canAccessRequest($user, $resourceId),
            'message' => $this->canAccessMessage($user, $resourceId),
            default => true
        };
    }

    /**
     * Check if user can access a specific church
     */
    private function canAccessChurch(User $user, int $churchId): bool
    {
        $accessibleChurches = $this->hierarchyService->getChurchesUserCanAccess($user);
        return $accessibleChurches->contains('id', $churchId);
    }

    /**
     * Check if user can access a specific user
     */
    private function canAccessUser(User $user, int $userId): bool
    {
        $targetUser = User::find($userId);
        if (!$targetUser) {
            return false;
        }

        $accessibleChurches = $this->hierarchyService->getChurchesUserCanAccess($user);
        return $accessibleChurches->contains('id', $targetUser->church_id);
    }

    /**
     * Check if user can access a specific request
     */
    private function canAccessRequest(User $user, int $requestId): bool
    {
        $request = \App\Models\Request::find($requestId);
        if (!$request) {
            return false;
        }

        $accessibleChurches = $this->hierarchyService->getChurchesUserCanAccess($user);
        return $accessibleChurches->contains('id', $request->church_id);
    }

    /**
     * Check if user can access a specific message
     */
    private function canAccessMessage(User $user, int $messageId): bool
    {
        $message = \App\Models\Message::find($messageId);
        if (!$message) {
            return false;
        }

        // Check if user is sender or recipient
        if ($message->sender_id === $user->id) {
            return true;
        }

        // Check if user is in recipient churches
        $recipientChurches = $message->recipientChurches->pluck('id');
        $accessibleChurches = $this->hierarchyService->getChurchesUserCanAccess($user);
        
        return $accessibleChurches->intersect($recipientChurches)->isNotEmpty();
    }
}
