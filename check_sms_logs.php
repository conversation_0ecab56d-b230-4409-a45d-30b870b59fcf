<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Recent SMS Logs ===\n\n";

$logs = \App\Models\SmsLog::latest()->take(10)->get();

foreach ($logs as $log) {
    echo "Phone: {$log->phone_number}\n";
    echo "Status: {$log->status}\n";
    echo "Type: {$log->type}\n";
    echo "Created: {$log->created_at}\n";
    echo "Message: " . substr($log->message, 0, 50) . "...\n";
    
    if ($log->provider_response) {
        $response = is_array($log->provider_response) ? $log->provider_response : json_decode($log->provider_response, true);
        if (isset($response['data']['outgoings'][0]['status'])) {
            echo "Provider Status: " . $response['data']['outgoings'][0]['status'] . "\n";
        }
    }
    
    if ($log->error_message) {
        echo "Error: {$log->error_message}\n";
    }
    
    echo "---\n\n";
}
