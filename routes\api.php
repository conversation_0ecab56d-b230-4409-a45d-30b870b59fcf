<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\SMSCallbackController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// SMS Callback routes for Africa's Talking
Route::post('/sms/callback', [SMSCallbackController::class, 'handleDeliveryReport']);
Route::post('/sms/incoming', [SMSCallbackController::class, 'handleIncomingSMS']);
