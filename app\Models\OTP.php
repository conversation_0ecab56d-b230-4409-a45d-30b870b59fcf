<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Traits\HasCustomId;

class OTP extends Model
{
    use HasCustomId;

    protected $table = 'otps';

    protected $fillable = [
        'user_id', 'otp', 'type', 'expires_at', 'used_at', 'custom_id',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'used_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function isExpired(): bool
    {
        return $this->expires_at < now();
    }

    public function isUsed(): bool
    {
        return $this->used_at !== null;
    }

    public function isValid(): bool
    {
        return !$this->isExpired() && !$this->isUsed();
    }

    public function markAsUsed(): void
    {
        $this->update(['used_at' => now()]);
    }

    public static function generateForUser(User $user, string $type = 'general', int $expiryMinutes = 30): self
    {
        // Invalidate any existing OTPs of the same type for this user
        static::where('user_id', $user->id)
              ->where('type', $type)
              ->whereNull('used_at')
              ->update(['used_at' => now()]);

        $otp = str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);

        return static::create([
            'user_id' => $user->id,
            'otp' => $otp,
            'type' => $type,
            'expires_at' => now()->addMinutes($expiryMinutes),
        ]);
    }

    public static function validateOTP(User $user, string $otp, string $type = 'general'): bool
    {
        $otpRecord = static::where('user_id', $user->id)
                          ->where('otp', $otp)
                          ->where('type', $type)
                          ->first();

        if (!$otpRecord || !$otpRecord->isValid()) {
            return false;
        }

        $otpRecord->markAsUsed();
        return true;
    }

    public static function cleanupExpired(): int
    {
        return static::where('expires_at', '<', now())->delete();
    }

    /**
     * Get the custom ID prefix for OTP model
     */
    protected function getCustomIdPrefix(): string
    {
        return 'OTP';
    }
}
