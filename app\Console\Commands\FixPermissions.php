<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use <PERSON>tie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class FixPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix missing permissions for existing roles';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 Fixing Permissions...');

        // Create missing permissions
        $missingPermissions = [
            'manage-users',
            'manage-churches',
            'delete-messages',
        ];

        foreach ($missingPermissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
            $this->info("✓ Created permission: {$permission}");
        }

        // Update Archbishop role with all permissions
        $archbishop = Role::where('name', 'Archbishop')->first();
        if ($archbishop) {
            $archbishop->syncPermissions([
                'create-users', 'edit-users', 'view-users', 'activate-users', 'deactivate-users', 'manage-users',
                'create-churches', 'edit-churches', 'view-churches', 'manage-church-hierarchy', 'manage-churches',
                'create-requests', 'view-requests', 'approve-requests', 'reject-requests', 'manage-requests',
                'send-messages', 'view-messages', 'send-bulk-messages', 'send-announcements', 'delete-messages',
                'assign-leaders', 'remove-leaders', 'view-leaders',
                'manage-system', 'view-reports', 'manage-permissions', 'audit-logs'
            ]);
            $this->info("✓ Updated Archbishop permissions");
        }

        // Update National Assistant role
        $nationalAssistant = Role::where('name', 'National Assistant')->first();
        if ($nationalAssistant) {
            $nationalAssistant->syncPermissions([
                'create-users', 'edit-users', 'view-users', 'manage-users',
                'view-churches', 'edit-churches', 'manage-churches',
                'view-requests', 'manage-requests',
                'send-messages', 'view-messages', 'send-announcements',
                'view-leaders', 'view-reports'
            ]);
            $this->info("✓ Updated National Assistant permissions");
        }

        // Update General Secretary role
        $generalSecretary = Role::where('name', 'General Secretary')->first();
        if ($generalSecretary) {
            $generalSecretary->syncPermissions([
                'create-users', 'edit-users', 'view-users', 'manage-users',
                'create-churches', 'edit-churches', 'view-churches', 'manage-churches',
                'view-requests', 'approve-requests', 'manage-requests',
                'send-messages', 'view-messages', 'send-bulk-messages', 'send-announcements',
                'assign-leaders', 'view-leaders', 'view-reports'
            ]);
            $this->info("✓ Updated General Secretary permissions");
        }

        $this->info('✅ Permissions fixed successfully!');
        return 0;
    }
}
