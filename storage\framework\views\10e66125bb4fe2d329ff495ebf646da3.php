

<?php $__env->startSection('title', 'Users Management'); ?>
<?php $__env->startSection('page-title', 'Users Management'); ?>

<?php $__env->startSection('breadcrumbs'); ?>
    <li>
        <span class="mx-2">/</span>
        <span class="font-medium text-gray-900">Users</span>
    </li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-actions'); ?>
    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create-users')): ?>
    <a href="<?php echo e(route('users.create')); ?>"
       class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <i class="fas fa-plus mr-2"></i>
        Add User
    </a>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">

    <!-- Filters and Search -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <form method="GET" action="<?php echo e(route('users.index')); ?>" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
                <!-- Search -->
                <div class="flex-1">
                    <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                    <div class="mt-1 relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text"
                               name="search"
                               id="search"
                               value="<?php echo e(request('search')); ?>"
                               placeholder="Search by name, email, or phone..."
                               class="focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md">
                    </div>
                </div>

                <!-- Church Filter -->
                <div class="min-w-0 flex-1 md:max-w-xs">
                    <label for="church" class="block text-sm font-medium text-gray-700">Church</label>
                    <select name="church"
                            id="church"
                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                        <option value="">All Churches</option>
                        <?php $__currentLoopData = $churches ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $church): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($church->id); ?>" <?php echo e(request('church') == $church->id ? 'selected' : ''); ?>>
                                <?php echo e($church->name); ?> (<?php echo e($church->level->value); ?>)
                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Role Filter -->
                <div class="min-w-0 flex-1 md:max-w-xs">
                    <label for="role" class="block text-sm font-medium text-gray-700">Role</label>
                    <select name="role"
                            id="role"
                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                        <option value="">All Roles</option>
                        <?php $__currentLoopData = $roles ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($role->name); ?>" <?php echo e(request('role') == $role->name ? 'selected' : ''); ?>>
                                <?php echo e($role->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Status Filter -->
                <div class="min-w-0 flex-1 md:max-w-xs">
                    <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                    <select name="status"
                            id="status"
                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                        <option value="">All Status</option>
                        <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Active</option>
                        <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                        <option value="first_login" <?php echo e(request('status') == 'first_login' ? 'selected' : ''); ?>>First Login</option>
                    </select>
                </div>

                <!-- Buttons -->
                <div class="flex space-x-2">
                    <button type="submit"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-search mr-2"></i>
                        Filter
                    </button>
                    <a href="<?php echo e(route('users.index')); ?>"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-times mr-2"></i>
                        Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    Users List
                </h3>
                <p class="text-sm text-gray-500">
                    <?php echo e($users->total()); ?> total users
                </p>
            </div>
        </div>

        <?php if($users->count() > 0): ?>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            User
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Contact
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Church & Role
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Last Login
                        </th>
                        <th scope="col" class="relative px-6 py-3">
                            <span class="sr-only">Actions</span>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                        <span class="text-sm font-medium text-blue-600">
                                            <?php echo e(substr($user->full_name, 0, 1)); ?>

                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">
                                        <?php echo e($user->full_name); ?>

                                    </div>
                                    <div class="text-sm text-gray-500">
                                        ID: <?php echo e($user->custom_id); ?>

                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                <div class="flex items-center">
                                    <i class="fas fa-envelope mr-2 text-gray-400"></i>
                                    <?php echo e($user->email); ?>

                                </div>
                                <div class="flex items-center mt-1">
                                    <i class="fas fa-phone mr-2 text-gray-400"></i>
                                    <?php echo e($user->phone_number); ?>

                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                <div class="font-medium"><?php echo e($user->church->name ?? 'N/A'); ?></div>
                                <div class="text-gray-500">
                                    <?php echo e($user->church && $user->church->level ? $user->church->level->value : 'N/A'); ?> Level
                                </div>
                                <?php if($user->getRoleNames()->isNotEmpty()): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mt-1">
                                    <?php echo e($user->getRoleNames()->first()); ?>

                                </span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex flex-col space-y-1">
                                <?php if($user->is_active): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        Active
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-times-circle mr-1"></i>
                                        Inactive
                                    </span>
                                <?php endif; ?>

                                <?php if($user->is_first_login): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-exclamation-triangle mr-1"></i>
                                        First Login
                                    </span>
                                <?php endif; ?>

                                <?php
                                    $hasExpiredOTPs = $user->otps()
                                        ->whereNull('used_at')
                                        ->where('expires_at', '<', now())
                                        ->exists();
                                ?>
                                <?php if($hasExpiredOTPs): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                        <i class="fas fa-key mr-1"></i>
                                        Expired OTP
                                    </span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <?php if($user->last_login_at): ?>
                                <?php echo e($user->last_login_at->diffForHumans()); ?>

                            <?php else: ?>
                                <span class="text-gray-400">Never</span>
                            <?php endif; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center space-x-2">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-users')): ?>
                                <a href="<?php echo e(route('users.show', $user)); ?>"
                                   class="text-blue-600 hover:text-blue-900"
                                   title="View User">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <?php endif; ?>

                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit-users')): ?>
                                <a href="<?php echo e(route('users.edit', $user)); ?>"
                                   class="text-indigo-600 hover:text-indigo-900"
                                   title="Edit User">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <?php endif; ?>

                                <?php if(Auth::user()->hasRole('National IT') || Auth::user()->hasPermissionTo('manage-system')): ?>
                                <!-- OTP Status Button -->
                                <button onclick="checkOTPStatus(<?php echo e($user->id); ?>)"
                                        class="text-purple-600 hover:text-purple-900"
                                        title="Check OTP Status">
                                    <i class="fas fa-key"></i>
                                </button>

                                <!-- Resend OTP Dropdown -->
                                <div class="relative inline-block text-left" x-data="{ open: false }">
                                    <button @click="open = !open"
                                            class="text-orange-600 hover:text-orange-900"
                                            title="Resend OTP">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                    <div x-show="open"
                                         @click.away="open = false"
                                         x-transition
                                         class="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                                        <div class="py-1">
                                            <button onclick="resendOTP(<?php echo e($user->id); ?>, 'registration')"
                                                    class="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100">
                                                Registration OTP
                                            </button>
                                            <button onclick="resendOTP(<?php echo e($user->id); ?>, 'password_reset')"
                                                    class="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100">
                                                Password Reset OTP
                                            </button>
                                            <button onclick="resendOTP(<?php echo e($user->id); ?>, 'login')"
                                                    class="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100">
                                                Login OTP
                                            </button>
                                            <button onclick="resendOTP(<?php echo e($user->id); ?>, 'general')"
                                                    class="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100">
                                                General OTP
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete-users')): ?>
                                <form action="<?php echo e(route('users.destroy', $user)); ?>"
                                      method="POST"
                                      class="inline"
                                      onsubmit="return confirm('Are you sure you want to delete this user?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit"
                                            class="text-red-600 hover:text-red-900"
                                            title="Delete User">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <?php echo e($users->appends(request()->query())->links()); ?>

        </div>
        <?php else: ?>
        <div class="text-center py-12">
            <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No users found</h3>
            <p class="text-gray-500 mb-4">
                <?php if(request()->hasAny(['search', 'church', 'role', 'status'])): ?>
                    No users match your current filters.
                <?php else: ?>
                    Get started by adding your first user.
                <?php endif; ?>
            </p>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create-users')): ?>
            <a href="<?php echo e(route('users.create')); ?>"
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <i class="fas fa-plus mr-2"></i>
                Add User
            </a>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- OTP Status Modal -->
<div id="otpStatusModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 lg:w-1/3 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">OTP Status</h3>
                <button onclick="closeOTPModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="otpStatusContent">
                <div class="flex justify-center">
                    <i class="fas fa-spinner fa-spin text-blue-500 text-2xl"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    // Check OTP status for a user
    function checkOTPStatus(userId) {
        document.getElementById('otpStatusModal').classList.remove('hidden');
        document.getElementById('otpStatusContent').innerHTML = `
            <div class="flex justify-center">
                <i class="fas fa-spinner fa-spin text-blue-500 text-2xl"></i>
            </div>
        `;

        fetch(`/users/${userId}/otp-status`)
            .then(response => response.json())
            .then(data => {
                let content = '';

                if (data.active_otps.length > 0) {
                    content += '<div class="mb-4"><h4 class="font-medium text-green-600 mb-2">Active OTPs:</h4>';
                    data.active_otps.forEach(otp => {
                        content += `
                            <div class="bg-green-50 border border-green-200 rounded p-3 mb-2">
                                <div class="flex justify-between items-center">
                                    <span class="font-medium">${otp.type.toUpperCase()}</span>
                                    <span class="text-sm text-green-600">Expires ${otp.expires_in}</span>
                                </div>
                            </div>
                        `;
                    });
                    content += '</div>';
                }

                if (data.expired_otps.length > 0) {
                    content += '<div class="mb-4"><h4 class="font-medium text-red-600 mb-2">Expired OTPs:</h4>';
                    data.expired_otps.forEach(otp => {
                        content += `
                            <div class="bg-red-50 border border-red-200 rounded p-3 mb-2">
                                <div class="flex justify-between items-center">
                                    <span class="font-medium">${otp.type.toUpperCase()}</span>
                                    <span class="text-sm text-red-600">Expired ${otp.expired_ago}</span>
                                </div>
                            </div>
                        `;
                    });
                    content += '</div>';
                }

                if (data.active_otps.length === 0 && data.expired_otps.length === 0) {
                    content = '<div class="text-center text-gray-500">No OTPs found for this user.</div>';
                }

                document.getElementById('otpStatusContent').innerHTML = content;
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('otpStatusContent').innerHTML =
                    '<div class="text-center text-red-500">Error loading OTP status.</div>';
            });
    }

    // Resend OTP for a user
    function resendOTP(userId, type) {
        if (!confirm(`Are you sure you want to resend ${type} OTP for this user?`)) {
            return;
        }

        const formData = new FormData();
        formData.append('type', type);
        formData.append('_token', '<?php echo e(csrf_token()); ?>');

        fetch(`/users/${userId}/resend-otp`, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.text();
            }
            throw new Error('Network response was not ok');
        })
        .then(data => {
            // Show success message
            showToast(`${type.toUpperCase()} OTP sent successfully!`, 'success');

            // Reload the page to show updated status
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Failed to send OTP. Please try again.', 'error');
        });
    }

    // Close OTP modal
    function closeOTPModal() {
        document.getElementById('otpStatusModal').classList.add('hidden');
    }

    // Simple toast notification function
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white ${
            type === 'success' ? 'bg-green-500' :
            type === 'error' ? 'bg-red-500' : 'bg-blue-500'
        }`;
        toast.textContent = message;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    // Close modal when clicking outside
    document.getElementById('otpStatusModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeOTPModal();
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\fpct-system\resources\views/users/index.blade.php ENDPATH**/ ?>