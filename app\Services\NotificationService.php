<?php

namespace App\Services;

use App\Models\User;
use App\Models\Church;
use App\Models\Message;
use App\Models\Transaction;
use App\Events\UserNotification;
use App\Services\HudumaSMSService;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    protected HudumaSMSService $smsService;

    public function __construct(HudumaSMSService $smsService)
    {
        $this->smsService = $smsService;
    }

    public function sendWelcomeEmail(User $user, string $temporaryPassword, string $otp): void
    {
        try {
            Mail::send('emails.welcome', [
                'user' => $user,
                'temporaryPassword' => $temporaryPassword,
                'otp' => $otp,
                'church' => $user->church,
            ], function ($message) use ($user) {
                $message->to($user->email, $user->full_name)
                       ->subject('Welcome to FPCT Church Management System (CMS)');
            });

            Log::info("Welcome email sent to {$user->email}");
        } catch (\Exception $e) {
            Log::error("Failed to send welcome email to {$user->email}: " . $e->getMessage());
        }
    }

    public function sendWelcomeSMS(User $user, string $temporaryPassword, string $otp): void
    {
        if (!$user->getNotificationPreferences()->shouldSendSMS('system')) {
            return;
        }

        if (!$this->isValidPhoneNumber($user->phone_number)) {
            Log::warning("Invalid phone number for user {$user->id}: {$user->phone_number}");
            return;
        }

        try {
            $success = $this->smsService->sendWelcomeSMS(
                $user->phone_number,
                $user->full_name,
                $user->email, // Use email as username since no username field exists
                $temporaryPassword,
                $otp,
                $user->id
            );

            if ($success) {
                Log::info("Welcome SMS sent to {$user->phone_number}");
            } else {
                Log::error("Failed to send welcome SMS to {$user->phone_number}");
            }
        } catch (\Exception $e) {
            Log::error("Failed to send welcome SMS to {$user->phone_number}: " . $e->getMessage());
        }
    }

    public function sendPasswordResetNotification(User $user, string $temporaryPassword, string $otp): void
    {
        $preferences = $user->getNotificationPreferences();

        if ($preferences->shouldSendEmail('system')) {
            $this->sendPasswordResetEmail($user, $temporaryPassword, $otp);
        }

        if ($preferences->shouldSendSMS('system')) {
            $this->sendPasswordResetSMS($user, $temporaryPassword, $otp);
        }

        if ($preferences->shouldSendPush('system')) {
            event(new UserNotification(
                $user,
                'password_reset',
                'Password Reset',
                'Your password has been reset. Please check your email/SMS for new credentials.',
                ['requires_action' => true]
            ));
        }
    }

    public function sendUserActivationNotification(User $user): void
    {
        $preferences = $user->getNotificationPreferences();

        if ($preferences->shouldSendEmail('system')) {
            Mail::send('emails.user-activated', ['user' => $user], function ($message) use ($user) {
                $message->to($user->email, $user->full_name)
                       ->subject('Account Activated - FPCT Church Management System (CMS)');
            });
        }

        if ($preferences->shouldSendSMS('system')) {
            $this->smsService->sendSMS(
                $user->phone_number,
                "Your FPCT Church Management System (CMS) account has been activated. You can now login to the system."
            );
        }

        if ($preferences->shouldSendPush('system')) {
            event(new UserNotification(
                $user,
                'account_activated',
                'Account Activated',
                'Your account has been activated and you can now access the system.',
                []
            ));
        }
    }

    public function sendUserDeactivationNotification(User $user, string $reason = null): void
    {
        $preferences = $user->getNotificationPreferences();
        $message = 'Your FPCT Church Management System (CMS) account has been deactivated.';
        
        if ($reason) {
            $message .= " Reason: {$reason}";
        }

        if ($preferences->shouldSendEmail('system')) {
            Mail::send('emails.user-deactivated', [
                'user' => $user,
                'reason' => $reason
            ], function ($mail) use ($user) {
                $mail->to($user->email, $user->full_name)
                     ->subject('Account Deactivated - FPCT Church Management System (CMS)');
            });
        }

        if ($preferences->shouldSendSMS('system')) {
            $this->smsService->sendSMS($user->phone_number, $message);
        }
    }

    public function sendMessageNotification(Message $message, User $recipient): void
    {
        $preferences = $recipient->getNotificationPreferences();

        if ($preferences->shouldSendPush('message')) {
            event(new UserNotification(
                $recipient,
                'new_message',
                'New Message',
                "You have a new message from {$message->sender->full_name}",
                [
                    'message_id' => $message->id,
                    'sender' => $message->sender->full_name,
                    'preview' => substr($message->content, 0, 100)
                ]
            ));
        }

        // Only send email/SMS for important messages or if user is offline
        if ($message->is_announcement || $message->priority === 'high') {
            if ($preferences->shouldSendEmail('message')) {
                $this->sendMessageEmail($message, $recipient);
            }

            if ($preferences->shouldSendSMS('message')) {
                $this->sendMessageSMS($message, $recipient);
            }
        }
    }

    public function sendBulkAnnouncement(string $title, string $content, array $recipients, User $sender): void
    {
        foreach ($recipients as $recipient) {
            $preferences = $recipient->getNotificationPreferences();

            if ($preferences->shouldSendEmail('announcement')) {
                Mail::send('emails.announcement', [
                    'title' => $title,
                    'content' => $content,
                    'sender' => $sender,
                    'recipient' => $recipient
                ], function ($message) use ($recipient, $title) {
                    $message->to($recipient->email, $recipient->full_name)
                           ->subject("FPCT Announcement: {$title}");
                });
            }

            if ($preferences->shouldSendSMS('announcement')) {
                try {
                    $this->smsService->sendAnnouncementSMS(
                        $recipient->phone_number,
                        $title,
                        $content
                    );
                } catch (\Exception $e) {
                    Log::error("Failed to send announcement SMS to {$recipient->phone_number}: " . $e->getMessage());
                }
            }

            if ($preferences->shouldSendPush('announcement')) {
                event(new UserNotification(
                    $recipient,
                    'announcement',
                    $title,
                    $content,
                    ['sender' => $sender->full_name]
                ));
            }
        }
    }

    public function sendRequestNotification(User $user, string $type, array $data): void
    {
        $preferences = $user->getNotificationPreferences();

        if ($preferences->shouldSendPush('request')) {
            event(new UserNotification(
                $user,
                $type,
                $this->getRequestNotificationTitle($type),
                $this->getRequestNotificationMessage($type, $data),
                $data
            ));
        }

        if ($preferences->shouldSendEmail('request')) {
            Mail::send('emails.request-notification', [
                'user' => $user,
                'type' => $type,
                'data' => $data
            ], function ($message) use ($user, $type) {
                $message->to($user->email, $user->full_name)
                       ->subject("FPCT Request: {$this->getRequestNotificationTitle($type)}");
            });
        }
    }

    private function sendPasswordResetEmail(User $user, string $temporaryPassword, string $otp): void
    {
        Mail::send('emails.password-reset', [
            'user' => $user,
            'temporaryPassword' => $temporaryPassword,
            'otp' => $otp
        ], function ($message) use ($user) {
            $message->to($user->email, $user->full_name)
                   ->subject('Password Reset - FPCT Church Management System (CMS)');
        });
    }

    private function sendPasswordResetSMS(User $user, string $temporaryPassword, string $otp): void
    {
        if (!$this->isValidPhoneNumber($user->phone_number)) {
            Log::warning("Invalid phone number for password reset SMS to user {$user->id}: {$user->phone_number}");
            return;
        }

        try {
            $success = $this->smsService->sendPasswordResetSMS(
                $user->phone_number,
                $user->email, // Use email as username
                $temporaryPassword,
                $otp, // Include OTP in SMS
                $user->id
            );

            if ($success) {
                Log::info("Password reset SMS sent to {$user->phone_number}");
            } else {
                Log::error("Failed to send password reset SMS to {$user->phone_number}");
            }
        } catch (\Exception $e) {
            Log::error("Failed to send password reset SMS to {$user->phone_number}: " . $e->getMessage());
        }
    }

    private function sendMessageEmail(Message $message, User $recipient): void
    {
        Mail::send('emails.message-notification', [
            'message' => $message,
            'recipient' => $recipient
        ], function ($mail) use ($recipient, $message) {
            $mail->to($recipient->email, $recipient->full_name)
                 ->subject("New Message from {$message->sender->full_name}");
        });
    }

    private function sendMessageSMS(Message $message, User $recipient): void
    {
        $content = "New message from {$message->sender->full_name}: " . substr($message->content, 0, 100);
        if (strlen($message->content) > 100) {
            $content .= '...';
        }

        $this->smsService->sendSMS($recipient->phone_number, $content);
    }

    private function getRequestNotificationTitle(string $type): string
    {
        return match ($type) {
            'request_created' => 'New Request Created',
            'request_approved' => 'Request Approved',
            'request_rejected' => 'Request Rejected',
            'approval_required' => 'Approval Required',
            default => 'Request Update',
        };
    }

    private function getRequestNotificationMessage(string $type, array $data): string
    {
        return match ($type) {
            'request_created' => "A new {$data['request_type']} request has been created.",
            'request_approved' => "Your {$data['request_type']} request has been approved.",
            'request_rejected' => "Your {$data['request_type']} request has been rejected.",
            'approval_required' => "A {$data['request_type']} request requires your approval.",
            default => 'Your request status has been updated.',
        };
    }

    /**
     * Check if user is currently online (for real-time messaging)
     */
    public function isUserOnline(User $user): bool
    {
        // Check if user has been active in the last 5 minutes
        return $user->last_activity_at && $user->last_activity_at->gt(now()->subMinutes(5));
    }

    /**
     * Validate phone number format
     */
    private function isValidPhoneNumber(?string $phoneNumber): bool
    {
        if (empty($phoneNumber)) {
            return false;
        }

        // Check if it's an email (common data issue)
        if (filter_var($phoneNumber, FILTER_VALIDATE_EMAIL)) {
            return false;
        }

        // Check if it contains only digits, +, spaces, and hyphens
        if (!preg_match('/^[\d\s\+\-\(\)]+$/', $phoneNumber)) {
            return false;
        }

        // Remove all non-digit characters for length check
        $digitsOnly = preg_replace('/\D/', '', $phoneNumber);

        // Tanzania phone numbers should have 9-12 digits
        return strlen($digitsOnly) >= 9 && strlen($digitsOnly) <= 12;
    }

    /**
     * Send notification with SMS fallback for offline users
     */
    public function sendNotificationWithSMSFallback(User $user, string $title, string $message, string $type = 'general'): void
    {
        // Always try real-time notification first
        $this->sendRealTimeNotification($user, $title, $message, $type);

        // If user is offline and has SMS enabled, send SMS
        if (!$this->isUserOnline($user) &&
            $user->getNotificationPreferences()->shouldSendSMS($type) &&
            $this->isValidPhoneNumber($user->phone_number)) {

            $smsMessage = "{$title}: {$message}";
            $this->smsService->sendSMS($user->phone_number, $smsMessage, $type, $user->id);

            Log::info("SMS fallback sent to offline user {$user->id}");
        }
    }

    /**
     * Send transaction completed notification
     */
    public function sendTransactionCompletedNotification(Transaction $transaction, User $user): void
    {
        $preferences = $user->getNotificationPreferences();

        // Send real-time notification
        if ($preferences->shouldSendPush('transaction')) {
            event(new UserNotification(
                $user,
                'transaction_completed',
                'Transaction Completed',
                $this->getTransactionCompletedMessage($transaction),
                [
                    'transaction_id' => $transaction->transaction_id,
                    'amount' => $transaction->amount,
                    'currency' => $transaction->currency,
                    'from_church' => $transaction->fromChurch->name,
                    'to_church' => $transaction->toChurch->name,
                ]
            ));
        }

        // Send email notification
        if ($preferences->shouldSendEmail('transaction')) {
            $this->sendTransactionCompletedEmail($transaction, $user);
        }

        // Always send SMS for transaction confirmations (important financial notification)
        if ($preferences->shouldSendSMS('transaction') && $this->isValidPhoneNumber($user->phone_number)) {
            $this->sendTransactionCompletedSMS($transaction, $user);
        }
    }

    /**
     * Send real-time notification via WebSocket
     */
    private function sendRealTimeNotification(User $user, string $title, string $message, string $type): void
    {
        try {
            event(new UserNotification($user, $type, $title, $message, [
                'timestamp' => now()->toISOString(),
            ]));
        } catch (\Exception $e) {
            Log::error("Failed to send real-time notification to user {$user->id}: " . $e->getMessage());
        }
    }

    /**
     * Get transaction completed message for notifications
     */
    private function getTransactionCompletedMessage(Transaction $transaction): string
    {
        $amount = number_format((float) $transaction->amount, 2);
        $currency = $transaction->currency;
        $fromChurch = $transaction->fromChurch->name;
        $toChurch = $transaction->toChurch->name;
        $type = $transaction->type->getLabel();

        return "Your {$type} of {$currency} {$amount} from {$fromChurch} to {$toChurch} has been completed successfully.";
    }

    /**
     * Send transaction completed email
     */
    private function sendTransactionCompletedEmail(Transaction $transaction, User $user): void
    {
        try {
            Mail::send('emails.transaction-completed', [
                'user' => $user,
                'transaction' => $transaction
            ], function ($message) use ($user, $transaction) {
                $message->to($user->email, $user->full_name)
                       ->subject("Transaction Completed - {$transaction->transaction_id}");
            });

            Log::info("Transaction completion email sent to {$user->email}");
        } catch (\Exception $e) {
            Log::error("Failed to send transaction completion email to {$user->email}: " . $e->getMessage());
        }
    }

    /**
     * Send transaction completed SMS
     */
    private function sendTransactionCompletedSMS(Transaction $transaction, User $user): void
    {
        try {
            $amount = number_format((float) $transaction->amount, 2);
            $currency = $transaction->currency;
            $fromChurch = $transaction->fromChurch->name;
            $toChurch = $transaction->toChurch->name;
            $type = $transaction->type->getLabel();
            $transactionId = $transaction->transaction_id;
            $referenceNumber = $transaction->reference_number;

            // Create comprehensive SMS message with all required details
            $message = "FPCT Transaction Completed!\n";
            $message .= "Type: {$type}\n";
            $message .= "Amount: {$currency} {$amount}\n";
            $message .= "From: {$fromChurch}\n";
            $message .= "To: {$toChurch}\n";
            $message .= "Purpose: {$transaction->description}\n";
            $message .= "Ref: {$referenceNumber}\n";
            $message .= "ID: {$transactionId}";

            $success = $this->smsService->sendSMS(
                $user->phone_number,
                $message,
                'transaction',
                $user->id
            );

            if ($success) {
                Log::info("Transaction completion SMS sent to {$user->phone_number}", [
                    'transaction_id' => $transaction->transaction_id,
                    'user_id' => $user->id
                ]);
            } else {
                Log::error("Failed to send transaction completion SMS to {$user->phone_number}", [
                    'transaction_id' => $transaction->transaction_id,
                    'user_id' => $user->id
                ]);
            }
        } catch (\Exception $e) {
            Log::error("Exception sending transaction completion SMS", [
                'transaction_id' => $transaction->transaction_id,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send OTP notification via email and SMS
     */
    public function sendOTPNotification(User $user, string $otp, string $type = 'general'): void
    {
        $this->sendOTPEmail($user, $otp, $type);
        $this->sendOTPSMS($user, $otp, $type);
    }

    /**
     * Send OTP via email
     */
    private function sendOTPEmail(User $user, string $otp, string $type): void
    {
        try {
            $subject = match($type) {
                'registration' => 'FPCT Registration OTP',
                'password_reset' => 'FPCT Password Reset OTP',
                'login' => 'FPCT Login OTP',
                default => 'FPCT Verification OTP'
            };

            Mail::send('emails.otp', [
                'user' => $user,
                'otp' => $otp,
                'type' => $type,
            ], function ($message) use ($user, $subject) {
                $message->to($user->email, $user->full_name)
                       ->subject($subject);
            });

            Log::info("OTP email sent to {$user->email} for {$type}");
        } catch (\Exception $e) {
            Log::error("Failed to send OTP email to {$user->email}: " . $e->getMessage());
        }
    }

    /**
     * Send OTP via SMS
     */
    private function sendOTPSMS(User $user, string $otp, string $type): void
    {
        if (!$this->isValidPhoneNumber($user->phone_number)) {
            Log::warning("Invalid phone number for OTP SMS to user {$user->id}: {$user->phone_number}");
            return;
        }

        try {
            $success = $this->smsService->sendOTP(
                $user->phone_number,
                $otp,
                'FPCT',
                $user->id
            );

            if ($success) {
                Log::info("OTP SMS sent to {$user->phone_number} for {$type}");
            } else {
                Log::error("Failed to send OTP SMS to {$user->phone_number} for {$type}");
            }
        } catch (\Exception $e) {
            Log::error("Exception sending OTP SMS to {$user->phone_number}: " . $e->getMessage());
        }
    }
}
