<?php

namespace App\Events;

use App\Models\Transaction;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TransactionCompleted implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Transaction $transaction;

    public function __construct(Transaction $transaction)
    {
        $this->transaction = $transaction;
    }

    public function broadcastOn()
    {
        return [
            new Channel('user.' . $this->transaction->initiated_by_user_id),
            new Channel('church.' . $this->transaction->from_church_id),
            new Channel('church.' . $this->transaction->to_church_id),
        ];
    }

    public function broadcastWith()
    {
        return [
            'transaction' => [
                'id' => $this->transaction->id,
                'transaction_id' => $this->transaction->transaction_id,
                'reference_number' => $this->transaction->reference_number,
                'amount' => $this->transaction->amount,
                'currency' => $this->transaction->currency,
                'type' => $this->transaction->type->getLabel(),
                'status' => $this->transaction->status->getLabel(),
                'from_church' => $this->transaction->fromChurch->name,
                'to_church' => $this->transaction->toChurch->name,
                'description' => $this->transaction->description,
                'completed_at' => $this->transaction->completed_at->toDateTimeString(),
            ],
        ];
    }
}
