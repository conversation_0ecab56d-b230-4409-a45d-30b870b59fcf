<?php

namespace App\Services;

use App\Models\Church;
use App\Models\User;
use App\Enums\ChurchLevel;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;

class ChurchHierarchyService
{
    public function getChurchHierarchy(Church $rootChurch = null): array
    {
        $cacheKey = 'church_hierarchy_' . ($rootChurch ? $rootChurch->id : 'all');
        
        return Cache::remember($cacheKey, now()->addHours(1), function () use ($rootChurch) {
            if (!$rootChurch) {
                $rootChurch = $this->getNationalChurch();
            }

            return $this->buildHierarchyTree($rootChurch);
        });
    }

    public function getNationalChurch(): ?Church
    {
        return Church::where('level', ChurchLevel::NATIONAL)
                    ->whereNull('parent_church_id')
                    ->first();
    }

    public function getRegionalChurches(Church $nationalChurch = null): Collection
    {
        if (!$nationalChurch) {
            $nationalChurch = $this->getNationalChurch();
        }

        return $nationalChurch ? $nationalChurch->childChurches()
                                              ->where('level', ChurchLevel::REGIONAL)
                                              ->with(['leaders.user'])
                                              ->get() : collect();
    }

    public function getLocalChurches(Church $regionalChurch): Collection
    {
        return $regionalChurch->childChurches()
                             ->where('level', ChurchLevel::LOCAL)
                             ->with(['leaders.user'])
                             ->get();
    }

    public function getParishes(Church $localChurch): Collection
    {
        return $localChurch->childChurches()
                          ->where('level', ChurchLevel::PARISH)
                          ->with(['leaders.user'])
                          ->get();
    }

    public function getBranches(Church $parishChurch): Collection
    {
        return $parishChurch->childChurches()
                           ->where('level', ChurchLevel::BRANCH)
                           ->with(['leaders.user'])
                           ->get();
    }

    public function getChurchPath(Church $church): array
    {
        $path = [];
        $current = $church;

        while ($current) {
            array_unshift($path, [
                'id' => $current->id,
                'name' => $current->name,
                'level' => $current->level->value,
                'location' => $current->location,
            ]);
            $current = $current->parentChurch;
        }

        return $path;
    }

    public function getChurchesUserCanAccess(User $user): Collection
    {
        $userChurch = $user->church()->first();
        if (!$userChurch) {
            return new Collection();
        }

        $accessibleChurches = new Collection([$userChurch]);

        // Add all descendant churches
        $descendants = $this->getAllDescendants($userChurch);
        $accessibleChurches = $accessibleChurches->merge($descendants);

        // Only Super Admin or users with manage-system permission can access ancestor churches
        if ($user->hasRole('Super Admin') || $user->hasPermissionTo('manage-system')) {
            $ancestors = $this->getAncestors($userChurch);
            $accessibleChurches = $accessibleChurches->merge($ancestors);
        }

        return $accessibleChurches->unique('id');
    }

    public function getChurchesUserCanManage(User $user): Collection
    {
        $userChurch = $user->church()->first();
        $manageableChurches = new Collection();

        if (!$userChurch) {
            return $manageableChurches;
        }

        // Users can manage their own church if they have edit permissions
        if ($user->hasPermissionTo('edit-churches')) {
            $manageableChurches->push($userChurch);
        }

        // Users can manage descendant churches based on their level
        if ($user->hasPermissionTo('manage-church-hierarchy')) {
            $descendants = $this->getAllDescendants($userChurch);
            $manageableChurches = $manageableChurches->merge($descendants);
        }

        return $manageableChurches->unique('id');
    }

    public function canCreateChurchAtLevel(User $user, ChurchLevel $level, Church $parentChurch = null): bool
    {
        // Check basic permission
        if (!$user->hasPermissionTo('create-churches')) {
            return false;
        }

        // If no parent specified, only national level can be created by system admins
        if (!$parentChurch) {
            return $level === ChurchLevel::NATIONAL && 
                   $user->hasPermissionTo('manage-system');
        }

        // Check if the level is valid for the parent
        if (!$parentChurch->canHaveChildLevel($level)) {
            return false;
        }

        // Check if user has authority over the parent church
        $userChurch = $user->church;
        
        return $userChurch->id === $parentChurch->id || 
               $userChurch->isAncestorOf($parentChurch);
    }

    public function validateChurchHierarchy(Church $church): array
    {
        $errors = [];

        // Check if church level is appropriate for its parent
        if (!$church->validateHierarchy()) {
            $errors[] = 'Invalid church hierarchy structure';
        }

        // Check for circular references
        if ($this->hasCircularReference($church)) {
            $errors[] = 'Circular reference detected in church hierarchy';
        }

        // Check if church has appropriate leadership for its level
        $requiredRoles = $church->level->getRolesByLevel();
        $currentLeaders = $church->leaders()->with('user.roles')->get();
        $currentRoles = $currentLeaders->flatMap(function ($leader) {
            return $leader->user->getRoleNames();
        })->unique()->toArray();

        $missingRoles = array_diff($requiredRoles, $currentRoles);
        if (!empty($missingRoles)) {
            $errors[] = 'Missing required leadership roles: ' . implode(', ', $missingRoles);
        }

        return $errors;
    }

    public function getChurchStatistics(Church $church = null): array
    {
        if (!$church) {
            $church = $this->getNationalChurch();
        }

        $stats = [
            'total_churches' => 0,
            'by_level' => [],
            'total_users' => 0,
            'active_users' => 0,
            'total_leaders' => 0,
        ];

        $allChurches = collect([$church])->merge($this->getAllDescendants($church));

        $stats['total_churches'] = $allChurches->count();

        // Count by level
        foreach (ChurchLevel::cases() as $level) {
            $stats['by_level'][$level->value] = $allChurches->filter(function ($church) use ($level) {
                return $church->level === $level;
            })->count();
        }

        // User statistics
        $churchIds = $allChurches->pluck('id');
        $stats['total_users'] = User::whereIn('church_id', $churchIds)->count();
        $stats['active_users'] = User::whereIn('church_id', $churchIds)
                                    ->where('is_active', true)
                                    ->count();

        // Leadership statistics
        $stats['total_leaders'] = $allChurches->sum(function ($church) {
            return $church->leaders()->count();
        });

        return $stats;
    }

    public function searchChurches(string $query, User $user = null): Collection
    {
        $queryBuilder = Church::where(function ($q) use ($query) {
            $q->where('name', 'ILIKE', "%{$query}%")
              ->orWhere('location', 'ILIKE', "%{$query}%");
        });

        // If user is provided, filter by accessible churches
        if ($user) {
            $accessibleChurchIds = $this->getChurchesUserCanAccess($user)->pluck('id');
            $queryBuilder->whereIn('id', $accessibleChurchIds);
        }

        return $queryBuilder->with(['parentChurch', 'leaders.user'])
                           ->orderBy('level')
                           ->orderBy('name')
                           ->get();
    }

    public function getChurchLeadershipChain(Church $church): array
    {
        $chain = [];
        $current = $church;

        while ($current) {
            $leaders = $current->leaders()
                              ->with(['user.roles'])
                              ->get()
                              ->map(function ($leader) {
                                  return [
                                      'user_id' => $leader->user->id,
                                      'name' => $leader->user->full_name,
                                      'role' => $leader->role,
                                      'email' => $leader->user->email,
                                      'phone' => $leader->user->phone_number,
                                  ];
                              });

            $chain[] = [
                'church_id' => $current->id,
                'church_name' => $current->name,
                'level' => $current->level->value,
                'leaders' => $leaders->toArray(),
            ];

            $current = $current->parentChurch;
        }

        return $chain;
    }

    private function buildHierarchyTree(Church $church): array
    {
        $children = $church->childChurches()
                          ->with(['leaders.user'])
                          ->orderBy('level')
                          ->orderBy('name')
                          ->get();

        return [
            'id' => $church->id,
            'name' => $church->name,
            'level' => $church->level->value,
            'location' => $church->location,
            'date_established' => $church->date_established?->format('Y-m-d'),
            'leaders' => $church->leaders()->with('user')->get()->map(function ($leader) {
                return [
                    'name' => $leader->user->full_name,
                    'role' => $leader->role,
                    'email' => $leader->user->email,
                ];
            })->toArray(),
            'children' => $children->map(function ($child) {
                return $this->buildHierarchyTree($child);
            })->toArray(),
        ];
    }

    function getAllDescendants(Church $church): Collection
    {
        return $church->getAllDescendants();
    }

    private function getAncestors(Church $church): Collection
    {
        $ancestors = new Collection();
        $current = $church->parentChurch;

        while ($current) {
            $ancestors->push($current);
            $current = $current->parentChurch;
        }

        return $ancestors;
    }

    private function hasCircularReference(Church $church): bool
    {
        $visited = collect();
        $current = $church;

        while ($current && !$visited->contains($current->id)) {
            $visited->push($current->id);
            $current = $current->parentChurch;
        }

        return $current !== null;
    }

    public function clearHierarchyCache(): void
    {
        $keys = [
            'church_hierarchy_all',
        ];

        // Clear cache for all churches
        Church::all()->each(function ($church) use (&$keys) {
            $keys[] = 'church_hierarchy_' . $church->id;
        });

        foreach ($keys as $key) {
            Cache::forget($key);
        }
    }
}
