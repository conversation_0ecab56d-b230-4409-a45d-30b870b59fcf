<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ __('auth.login_title') }}</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Header -->
            <div class="text-center">
                <div class="mx-auto h-32 w-32 rounded-full flex items-center justify-center mb-1">
                    {{-- <i class="fas fa-church text-white text-2xl"></i> --}}
                    <img src="/logo/fpct-logo.png" alt="fpct-logo.png">
                </div>
                <h2 class="text-3xl font-extrabold text-gray-900">
                    {{ __('auth.login_header') }}
                </h2>
                <p class="mt-2 text-sm text-gray-600">
                    {{ __('auth.sign_in_to_account') }}
                </p>
            </div>

            <!-- Login Form -->
            <div class="bg-white py-8 px-6 shadow-xl rounded-lg">
                <!-- Flash Messages -->
                @if (session('success'))
                    <div class="mb-4 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle mr-2"></i>
                            {{ session('success') }}
                        </div>
                    </div>
                @endif

                @if (session('info'))
                    <div class="mb-4 bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-md">
                        <div class="flex items-center">
                            <i class="fas fa-info-circle mr-2"></i>
                            <div>
                                <div class="font-medium">{{ __('auth.login_required') }}</div>
                                <div class="text-sm mt-1">{{ session('info') }}</div>
                                @if (session('url.intended'))
                                    <div class="text-xs mt-1 text-blue-600">{{ __('auth.redirect_after_login') }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endif

                @if (session('error'))
                    <div class="mb-4 {{ session('session_expired') ? 'bg-yellow-50 border border-yellow-200 text-yellow-700' : 'bg-red-50 border border-red-200 text-red-700' }} px-4 py-3 rounded-md">
                        <div class="flex items-center">
                            <i class="fas {{ session('session_expired') ? 'fa-clock' : 'fa-exclamation-circle' }} mr-2"></i>
                            <div>
                                @if (session('session_expired'))
                                    <div class="font-medium">{{ __('auth.session_expired') }}</div>
                                    <div class="text-sm mt-1">{{ session('error') }}</div>
                                    @if (session('url.intended'))
                                        <div class="text-xs mt-1 text-yellow-600">{{ __('auth.redirect_after_login') }}</div>
                                    @endif
                                @else
                                    {{ session('error') }}
                                @endif
                            </div>
                        </div>
                    </div>
                @endif

                @if ($errors->any())
                    <div class="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <span class="font-medium">{{ __('auth.please_correct_errors') }}</span>
                        </div>
                        <ul class="list-disc list-inside text-sm">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <form method="POST" action="{{ route('login') }}" class="space-y-6">
                    @csrf

                    <!-- Email Field -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ __('auth.email_address') }}
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-envelope text-gray-400"></i>
                            </div>
                            <input type="email"
                                   name="email"
                                   id="email"
                                   value="{{ old('email') }}"
                                   class="appearance-none relative block w-full pl-10 pr-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm @error('email') border-red-300 @enderror"
                                   placeholder="{{ __('auth.enter_email') }}"
                                   required
                                   autofocus>
                        </div>
                        @error('email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Password Field -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ __('auth.password_field') }}
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-lock text-gray-400"></i>
                            </div>
                            <input type="password"
                                   name="password"
                                   id="password"
                                   class="appearance-none relative block w-full pl-10 pr-12 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm @error('password') border-red-300 @enderror"
                                   placeholder="{{ __('auth.enter_password') }}"
                                   required>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <button type="button" onclick="togglePassword('password')" class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 transition-colors duration-200">
                                    <i class="fas fa-eye" id="password_icon"></i>
                                </button>
                            </div>
                        </div>
                        @error('password')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Remember Me & Forgot Password -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <input id="remember_me"
                                   name="remember"
                                   type="checkbox"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="remember_me" class="ml-2 block text-sm text-gray-700">
                                {{ __('auth.remember_me') }}
                            </label>
                        </div>

                        <div class="text-sm">
                            <a href="{{ route('password.request') }}"
                               class="font-medium text-blue-600 hover:text-blue-500 transition-colors duration-200">
                                {{ __('auth.forgot_password') }}
                            </a>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div>
                        <button type="submit"
                                class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                <i class="fas fa-sign-in-alt text-blue-500 group-hover:text-blue-400"></i>
                            </span>
                            {{ __('auth.sign_in') }}
                        </button>
                    </div>

                    <!-- First Time Login Help -->
                    <div class="mt-6 border-t border-gray-200 pt-6">
                        <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-info-circle text-blue-400"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-blue-800">
                                        {{ __('auth.first_time_login') }}
                                    </h3>
                                    <div class="mt-2 text-sm text-blue-700">
                                        <p>
                                            {{ __('auth.credentials_help') }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Support -->
                    <div class="text-center">
                        <p class="text-sm text-gray-600">
                            {{ __('auth.need_help') }}
                            <a href="mailto:<EMAIL>" class="font-medium text-blue-600 hover:text-blue-500">
                                {{ __('auth.contact_support') }}
                            </a>
                        </p>
                    </div>
                </form>
            </div>

            <!-- Language Selector -->
            <div class="mt-6 text-center">
                <div class="inline-flex items-center space-x-2 bg-white px-4 py-2 rounded-lg shadow-sm">
                    <i class="fas fa-globe text-gray-500"></i>
                    <select onchange="switchLanguage(this.value)"
                            class="border-none bg-transparent text-sm text-gray-700 focus:outline-none cursor-pointer">
                        <option value="en" {{ app()->getLocale() == 'en' ? 'selected' : '' }}>English</option>
                        <option value="sw" {{ app()->getLocale() == 'sw' ? 'selected' : '' }}>Kiswahili</option>
                    </select>
                </div>
            </div>

            <!-- Footer -->
            <div class="text-center">
                <p class="text-xs text-gray-500">
                    © {{ date('Y') }} {{ __('auth.rights_reserved') }}
                </p>
            </div>
        </div>
    </div>

    <!-- JavaScript for Password Visibility Toggle -->
    <script>
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const icon = document.getElementById(fieldId + '_icon');

            if (field.type === 'password') {
                field.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Show intended URL information if available
        document.addEventListener('DOMContentLoaded', function() {
            @if (session('url.intended'))
                const intendedUrl = '{{ session('url.intended') }}';
                console.log('User will be redirected to:', intendedUrl);

                // Add a subtle indicator showing where user will be redirected
                const form = document.querySelector('form');
                if (form && intendedUrl) {
                    const urlParts = intendedUrl.split('/');
                    const lastPart = urlParts[urlParts.length - 1] || urlParts[urlParts.length - 2];
                    const redirectInfo = document.createElement('div');
                    redirectInfo.className = 'mt-2 text-xs text-blue-600 text-center';
                    redirectInfo.innerHTML = '<i class="fas fa-arrow-right mr-1"></i>Redirecting to: ' + lastPart;
                    form.appendChild(redirectInfo);
                }
            @endif
        });

        // Language switching function (if needed)
        function switchLanguage(locale) {
            // Show loading state
            const loadingToast = showToast ? showToast('{{ __("common.please_wait") }}...', 'info') : null;

            // Make AJAX request to switch language
            fetch('{{ route("language.switch") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    locale: locale
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload the page to apply new language
                    window.location.reload();
                } else {
                    console.error('Language switch failed:', data.message);
                    if (loadingToast && hideToast) hideToast(loadingToast);
                }
            })
            .catch(error => {
                console.error('Error switching language:', error);
                if (loadingToast && hideToast) hideToast(loadingToast);
            });
        }
    </script>
</body>
</html>