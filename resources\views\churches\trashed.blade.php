@extends('layouts.app')

@section('title', 'Trashed Churches')
@section('page-title', 'Trashed Churches')

@section('breadcrumbs')
    <li>
        <span class="mx-2">/</span>
        <a href="{{ route('churches.index') }}" class="hover:text-gray-700">Churches</a>
    </li>
    <li>
        <span class="mx-2">/</span>
        <span class="font-medium text-gray-900">Trashed</span>
    </li>
@endsection

@section('page-actions')
    <a href="{{ route('churches.index') }}"
       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
        <i class="fas fa-arrow-left mr-2"></i>
        Back to Churches
    </a>
@endsection

@section('content')
<div class="space-y-6">
    <!-- Search and Filter -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Search Trashed Churches</h3>
        </div>
        <div class="p-6">
            <form method="GET" action="{{ route('churches.trashed') }}" class="flex items-center space-x-4">
                <div class="flex-1">
                    <input type="text"
                           name="search"
                           value="{{ request('search') }}"
                           placeholder="Search by name, location, contact info..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                </div>
                <button type="submit"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-search mr-2"></i>
                    Search
                </button>
                @if(request('search'))
                    <a href="{{ route('churches.trashed') }}"
                       class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Clear
                    </a>
                @endif
            </form>
        </div>
    </div>

    <!-- Trashed Churches List -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">
                Trashed Churches
                <span class="text-sm text-gray-500">({{ $churches->total() }} total)</span>
            </h3>
        </div>

        @if($churches->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Church</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Level</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parent</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deleted At</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($churches as $church)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-church text-gray-400"></i>
                                        </div>
                                        <div class="ml-3">
                                            <div class="text-sm font-medium text-gray-900">{{ $church->name }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($church->level->value == 'National') bg-purple-100 text-purple-800
                                        @elseif($church->level->value == 'Regional') bg-blue-100 text-blue-800
                                        @elseif($church->level->value == 'Local') bg-green-100 text-green-800
                                        @elseif($church->level->value == 'Parish') bg-yellow-100 text-yellow-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        {{ $church->level->value }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $church->location }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $church->parent ? $church->parent->name : 'None' }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $church->deleted_at->format('M d, Y H:i') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                    <form action="{{ route('churches.restore', $church->id) }}" method="POST" class="inline">
                                        @csrf
                                        <button type="submit"
                                                class="text-green-600 hover:text-green-900 font-medium"
                                                onclick="return confirm('Are you sure you want to restore this church?')">
                                            <i class="fas fa-undo mr-1"></i>
                                            Restore
                                        </button>
                                    </form>

                                    <form action="{{ route('churches.force-delete', $church->id) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                                class="text-red-600 hover:text-red-900 font-medium ml-3"
                                                onclick="return confirm('Are you sure you want to permanently delete this church? This action cannot be undone.')">
                                            <i class="fas fa-trash mr-1"></i>
                                            Delete
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($churches->hasPages())
                <div class="px-6 py-4 border-t border-gray-200">
                    {{ $churches->links() }}
                </div>
            @endif
        @else
            <div class="px-6 py-12 text-center">
                <i class="fas fa-trash-alt text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Trashed Churches</h3>
                <p class="text-gray-500">
                    @if(request('search'))
                        No trashed churches found matching "{{ request('search') }}".
                    @else
                        There are no churches in the trash.
                    @endif
                </p>
                @if(request('search'))
                    <a href="{{ route('churches.trashed') }}"
                       class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-100 hover:bg-blue-200">
                        View All Trashed Churches
                    </a>
                @endif
            </div>
        @endif
    </div>
</div>
@endsection