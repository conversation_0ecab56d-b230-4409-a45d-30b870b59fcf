<?php

return [
    // General
    'permissions' => 'Permissions',
    'permission' => 'Permission',
    'permission_details' => 'Permission Details',
    'edit_permission' => 'Edit Permission',
    'create_permission' => 'Create Permission',
    'update_permission' => 'Update Permission',
    'delete_permission' => 'Delete Permission',
    'permission_created' => 'Permission created successfully',
    'permission_updated' => 'Permission updated successfully',
    'permission_deleted' => 'Permission deleted successfully',

    // Fields
    'permission_name' => 'Permission Name',
    'permission_id' => 'Permission ID',
    'guard_name' => 'Guard Name',
    'guard' => 'Guard',
    'basic_information' => 'Basic Information',
    'permission_information' => 'Permission Information',
    'editing_permission' => 'Editing Permission',

    // Form placeholders and help text
    'enter_permission_name' => 'Enter permission name (e.g., edit-users)',
    'permission_name_help' => 'Use lowercase letters, numbers, and hyphens. Example: edit-users, view-reports',
    'guard_name_help' => 'Select the guard that will handle this permission',
    'web_guard' => 'Web (Default)',
    'api_guard' => 'API',

    // Relationships
    'roles_with_permission' => 'Roles with this Permission',
    'no_roles_assigned' => 'No roles have this permission',
    'users_with_permission' => 'Users with this Permission',
    'no_users_assigned' => 'No users have this permission directly',

    // Actions
    'assign_to_role' => 'Assign to Role',
    'remove_from_role' => 'Remove from Role',
    'view_roles' => 'View Roles',
    'manage_permissions' => 'Manage Permissions',

    // Messages
    'permission_required' => 'Permission name is required',
    'permission_exists' => 'A permission with this name already exists',
    'permission_in_use' => 'This permission is currently assigned to roles and cannot be deleted',
    'confirm_delete' => 'Are you sure you want to delete this permission?',
    'delete_warning' => 'This action cannot be undone and will remove the permission from all roles.',

    // Status
    'active' => 'Active',
    'inactive' => 'Inactive',
    'system_permission' => 'System Permission',
    'custom_permission' => 'Custom Permission',
];
