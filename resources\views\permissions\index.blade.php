@extends('layouts.app')

@section('title', 'Permission Management')

@section('content')
<div class="min-h-screen bg-gray-50 py-8" x-data="{ showFilters: false }">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Permission Management</h1>
                    <p class="mt-2 text-sm text-gray-600">Manage system permissions and their assignments</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="{{ route('roles.index') }}" 
                       class="inline-flex items-center px-4 py-2 border border-blue-300 rounded-lg text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <i class="fas fa-user-shield mr-2"></i>
                        Manage Roles
                    </a>
                    @can('manage-permissions')
                    <a href="{{ route('permissions.create') }}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5">
                        <i class="fas fa-plus mr-2"></i>
                        Create Permission
                    </a>
                    @endcan
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden mb-8">
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Search & Filter</h3>
                    <button @click="showFilters = !showFilters" 
                            class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-filter"></i>
                    </button>
                </div>
            </div>
            
            <div class="p-6" x-show="showFilters" x-transition>
                <form method="GET" action="{{ route('permissions.index') }}" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search Permissions</label>
                        <input type="text" 
                               id="search"
                               name="search" 
                               value="{{ request('search') }}"
                               placeholder="Search by permission name..."
                               class="block w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    </div>
                    
                    <div>
                        <label for="role" class="block text-sm font-medium text-gray-700 mb-2">Filter by Role</label>
                        <select name="role" 
                                id="role"
                                class="block w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500">
                            <option value="">All Roles</option>
                            @foreach($roles as $role)
                                <option value="{{ $role->name }}" {{ request('role') == $role->name ? 'selected' : '' }}>
                                    {{ $role->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div class="flex items-end space-x-2">
                        <button type="submit" 
                                class="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors duration-200">
                            <i class="fas fa-search mr-2"></i>
                            Search
                        </button>
                        <a href="{{ route('permissions.index') }}" 
                           class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Grouped Permissions -->
        @if(isset($groupedPermissions) && count($groupedPermissions) > 0)
            <div class="space-y-8">
                @foreach($groupedPermissions as $resource => $resourcePermissions)
                    <div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50">
                            <h3 class="text-lg font-semibold text-gray-900 capitalize flex items-center">
                                <i class="fas fa-{{ $resource === 'users' ? 'users' : ($resource === 'churches' ? 'church' : ($resource === 'messages' ? 'envelope' : ($resource === 'requests' ? 'clipboard-list' : 'cog'))) }} mr-3 text-green-600"></i>
                                {{ ucfirst($resource) }} Permissions
                                <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    {{ count($resourcePermissions) }}
                                </span>
                            </h3>
                        </div>
                        
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                @foreach($resourcePermissions as $permission)
                                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                        <div class="flex items-center justify-between mb-3">
                                            <h4 class="text-sm font-medium text-gray-900">{{ $permission->name }}</h4>
                                            <div class="flex items-center space-x-2">
                                                @can('manage-permissions')
                                                <a href="{{ route('permissions.edit', $permission) }}" 
                                                   class="text-green-600 hover:text-green-800 transition-colors duration-200"
                                                   title="Edit Permission">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="{{ route('permissions.destroy', $permission) }}" 
                                                      method="POST" 
                                                      class="inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this permission?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" 
                                                            class="text-red-600 hover:text-red-800 transition-colors duration-200"
                                                            title="Delete Permission">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                                @endcan
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <p class="text-xs text-gray-500 mb-2">Assigned to {{ $permission->roles_count }} roles:</p>
                                            <div class="flex flex-wrap gap-1">
                                                @forelse($permission->roles->take(3) as $role)
                                                    <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800">
                                                        {{ $role->name }}
                                                    </span>
                                                @empty
                                                    <span class="text-xs text-gray-500">No roles assigned</span>
                                                @endforelse
                                                @if($permission->roles->count() > 3)
                                                    <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-600">
                                                        +{{ $permission->roles->count() - 3 }} more
                                                    </span>
                                                @endif
                                            </div>
                                        </div>
                                        
                                        <div class="flex items-center justify-between">
                                            <div class="text-xs text-gray-500">
                                                <i class="fas fa-user-shield mr-1"></i>
                                                {{ $permission->roles_count }} roles
                                            </div>
                                            <a href="{{ route('permissions.show', $permission) }}" 
                                               class="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                                                View
                                                <i class="fas fa-arrow-right ml-1"></i>
                                            </a>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <!-- Fallback: Regular permissions list -->
            <div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                    <h3 class="text-lg font-semibold text-gray-900">All Permissions</h3>
                </div>
                
                <div class="p-6">
                    @if($permissions->count() > 0)
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @foreach($permissions as $permission)
                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                    <div class="flex items-center justify-between mb-3">
                                        <h4 class="text-sm font-medium text-gray-900">{{ $permission->name }}</h4>
                                        <div class="flex items-center space-x-2">
                                            @can('manage-permissions')
                                            <a href="{{ route('permissions.edit', $permission) }}" 
                                               class="text-green-600 hover:text-green-800 transition-colors duration-200">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @endcan
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="flex flex-wrap gap-1">
                                            @forelse($permission->roles->take(3) as $role)
                                                <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800">
                                                    {{ $role->name }}
                                                </span>
                                            @empty
                                                <span class="text-xs text-gray-500">No roles assigned</span>
                                            @endforelse
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between">
                                        <div class="text-xs text-gray-500">
                                            {{ $permission->roles_count }} roles
                                        </div>
                                        <a href="{{ route('permissions.show', $permission) }}" 
                                           class="text-green-600 hover:text-green-800 transition-colors duration-200">
                                            View Details
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-12">
                            <i class="fas fa-key text-4xl text-gray-400 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No permissions found</h3>
                            <p class="text-gray-600 mb-4">Get started by creating your first permission.</p>
                            @can('manage-permissions')
                            <a href="{{ route('permissions.create') }}" 
                               class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                <i class="fas fa-plus mr-2"></i>
                                Create Permission
                            </a>
                            @endcan
                        </div>
                    @endif
                </div>
            </div>
        @endif

        <!-- Pagination -->
        @if($permissions->hasPages())
            <div class="mt-8 bg-white px-4 py-3 border border-gray-200 rounded-lg">
                {{ $permissions->links() }}
            </div>
        @endif
    </div>
</div>
@endsection
