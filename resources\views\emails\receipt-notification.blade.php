<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt - {{ $receipt->receipt_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        .header p {
            margin: 5px 0 0 0;
            font-size: 14px;
            opacity: 0.9;
        }
        .receipt-number {
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 6px;
            display: inline-block;
            margin-top: 15px;
            font-weight: bold;
            font-size: 16px;
        }
        .content {
            padding: 30px;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 20px;
        }
        .transaction-details {
            background-color: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #2563eb;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .detail-label {
            font-weight: 600;
            color: #4a5568;
        }
        .detail-value {
            color: #2d3748;
            font-weight: 500;
        }
        .amount-highlight {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
            margin: 20px 0;
        }
        .amount-highlight .amount {
            font-size: 24px;
            font-weight: bold;
            color: #155724;
            margin: 0;
        }
        .amount-highlight .status {
            color: #0f5132;
            font-size: 14px;
            margin: 5px 0 0 0;
        }
        .church-info {
            background-color: #f1f5f9;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .church-info h4 {
            margin: 0 0 10px 0;
            color: #1e293b;
            font-size: 14px;
            font-weight: 600;
        }
        .church-info p {
            margin: 3px 0;
            font-size: 13px;
            color: #475569;
        }
        .notes {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .notes h4 {
            margin: 0 0 10px 0;
            color: #92400e;
            font-size: 14px;
        }
        .notes ul {
            margin: 0;
            padding-left: 20px;
            color: #78350f;
            font-size: 13px;
        }
        .notes li {
            margin-bottom: 5px;
        }
        .footer {
            background-color: #f8fafc;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
            font-size: 12px;
            color: #6b7280;
        }
        .footer p {
            margin: 5px 0;
        }
        .signature-section {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #e2e8f0;
            text-align: right;
        }
        .signature-section p {
            margin: 3px 0;
            font-size: 14px;
        }
        .signature-name {
            font-weight: bold;
            color: #1e293b;
        }
        .signature-role {
            color: #64748b;
            font-size: 13px;
        }
        .attachment-notice {
            background-color: #dbeafe;
            border: 1px solid #3b82f6;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        .attachment-notice .icon {
            font-size: 20px;
            color: #2563eb;
            margin-bottom: 5px;
        }
        .attachment-notice p {
            margin: 0;
            color: #1e40af;
            font-size: 14px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Official Receipt</h1>
            <p>Free Pentecostal Church of Tanzania</p>
            <div class="receipt-number">{{ $receipt->receipt_number }}</div>
        </div>

        <div class="content">
            <div class="greeting">
                <p>Dear Church Leader,</p>
                <p>Please find attached the official receipt for the completed transaction. Below are the transaction details:</p>
            </div>

            <div class="transaction-details">
                <div class="detail-row">
                    <span class="detail-label">Transaction ID:</span>
                    <span class="detail-value">{{ $transaction->transaction_id }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Transaction Date:</span>
                    <span class="detail-value">{{ $transaction->created_at->format('M j, Y H:i') }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Payment Method:</span>
                    <span class="detail-value">{{ $transaction->payment_method->getLabel() }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Reference Number:</span>
                    <span class="detail-value">{{ $transaction->reference_number }}</span>
                </div>
                @if($transaction->description)
                <div class="detail-row">
                    <span class="detail-label">Description:</span>
                    <span class="detail-value">{{ $transaction->description }}</span>
                </div>
                @endif
            </div>

            <div class="amount-highlight">
                <p class="amount">{{ $receipt->getFormattedAmount() }}</p>
                <p class="status">Payment Status: Completed</p>
            </div>

            <div style="display: flex; gap: 20px;">
                <div style="flex: 1;">
                    <div class="church-info">
                        <h4>Issued To:</h4>
                        <p><strong>{{ $receipt->issuedToChurch->name }}</strong></p>
                        <p>{{ $receipt->issuedToChurch->level->value }} Level</p>
                        <p>{{ $receipt->issuedToChurch->location }}</p>
                    </div>
                </div>
                <div style="flex: 1;">
                    <div class="church-info">
                        <h4>Issued By:</h4>
                        <p><strong>{{ $receipt->issuedByChurch->name }}</strong></p>
                        <p>{{ $receipt->issuedByChurch->level->value }} Level</p>
                        <p>{{ $receipt->issuedByChurch->location }}</p>
                    </div>
                </div>
            </div>

            @if($receipt->description)
            <div class="transaction-details">
                <div class="detail-row">
                    <span class="detail-label">Additional Information:</span>
                    <span class="detail-value">{{ $receipt->description }}</span>
                </div>
            </div>
            @endif

            <div class="attachment-notice">
                <div class="icon">📎</div>
                <p>The official PDF receipt is attached to this email</p>
            </div>

            <div class="notes">
                <h4>Important Notes:</h4>
                <ul>
                    <li>This is an official receipt issued by the Free Pentecostal Church of Tanzania</li>
                    <li>Keep this receipt for your records and tax purposes</li>
                    <li>For any inquiries regarding this receipt, contact the issuing church</li>
                    <li>This receipt confirms the successful completion of the transaction</li>
                </ul>
            </div>

            <div class="signature-section">
                <p class="signature-name">{{ $receipt->issuedByUser->full_name }}</p>
                <p class="signature-role">{{ $receipt->issuedByUser->role }}</p>
                <p class="signature-role">{{ $receipt->issuedByChurch->name }}</p>
                <p style="font-size: 12px; color: #9ca3af; margin-top: 10px;">
                    Generated on: {{ $receipt->created_at->format('M j, Y H:i') }}
                </p>
            </div>
        </div>

        <div class="footer">
            <p>&copy; {{ date('Y') }} Free Pentecostal Church of Tanzania. All rights reserved.</p>
            <p>This is an automated message from the FPCT Management System.</p>
            <p>If you have any questions, please contact your system administrator.</p>
        </div>
    </div>
</body>
</html>
