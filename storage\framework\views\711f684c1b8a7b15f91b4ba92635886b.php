<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset - FPCT Church Management System (CMS)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 10px 10px 0 0;
        }
        .content {
            background: #f8fafc;
            padding: 30px;
            border-radius: 0 0 10px 10px;
            border: 1px solid #e2e8f0;
        }
        .credentials-box {
            background: #fff;
            border: 2px solid #dc2626;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .credential-item {
            margin: 15px 0;
            padding: 10px;
            background: #fef2f2;
            border-radius: 5px;
        }
        .credential-label {
            font-weight: bold;
            color: #b91c1c;
            display: block;
            margin-bottom: 5px;
        }
        .credential-value {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            font-family: monospace;
            background: #fff;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #d1d5db;
            display: inline-block;
        }
        .warning {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .warning-icon {
            color: #d97706;
            font-weight: bold;
        }
        .security-notice {
            background: #fef2f2;
            border: 1px solid #dc2626;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .steps {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .step {
            margin: 10px 0;
            padding: 10px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .step:last-child {
            border-bottom: none;
        }
        .step-number {
            background: #dc2626;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔐 Password Reset</h1>
        <p>FPCT Church Management System (CMS) Security Alert</p>
    </div>

    <div class="content">
        <h2>Hello <?php echo e($user->full_name); ?>!</h2>
        
        <p>Your password has been reset as requested. Below are your new login credentials.</p>

        <div class="security-notice">
            <strong>🚨 Security Notice:</strong><br>
            If you did not request this password reset, please contact your system administrator immediately.
        </div>

        <div class="credentials-box">
            <h3>🔐 Your New Login Credentials</h3>
            
            <div class="credential-item">
                <span class="credential-label">Email Address:</span>
                <span class="credential-value"><?php echo e($user->email); ?></span>
            </div>
            
            <div class="credential-item">
                <span class="credential-label">New Temporary Password:</span>
                <span class="credential-value"><?php echo e($temporaryPassword); ?></span>
            </div>
            
            <div class="credential-item">
                <span class="credential-label">Verification Code (OTP):</span>
                <span class="credential-value"><?php echo e($otp); ?></span>
            </div>
        </div>

        <div class="warning">
            <span class="warning-icon">⚠️</span>
            <strong>Important Security Information:</strong>
            <ul>
                <li>This is a temporary password - you must change it immediately after login</li>
                <li>The OTP expires in 15 minutes</li>
                <li>Never share these credentials with anyone</li>
                <li>Use a strong, unique password when changing it</li>
            </ul>
        </div>

        <div class="steps">
            <h3>📋 Next Steps:</h3>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>Login:</strong> Visit the FPCT Church Management System (CMS) and login with your email and new temporary password
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>Verify OTP:</strong> Enter the verification code provided above
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>Change Password:</strong> Immediately set a new, secure password for your account
            </div>
        </div>

        <p>If you continue to have issues accessing your account, please contact your system administrator.</p>
        
        <p><strong>FPCT Church Management System (CMS) Security Team</strong></p>
    </div>

    <div class="footer">
        <p>This email was sent automatically by the FPCT Management System.</p>
        <p>For security reasons, this email contains sensitive information. Please delete it after use.</p>
    </div>
</body>
</html>
<?php /**PATH C:\wamp64\www\fpct-system\resources\views/emails/password-reset.blade.php ENDPATH**/ ?>