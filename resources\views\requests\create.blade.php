@extends('layouts.app')

@section('title', 'Create Request')

@section('content')
    <div class="container mx-auto p-6">
        <div class="mb-6">
            <h1 class="text-3xl font-bold mb-2">Create Request</h1>
            <p class="text-gray-600">Submit a new request for church operations and approvals.</p>
        </div>

        @if(session('error'))
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <strong>Error:</strong> {{ session('error') }}
            </div>
        @endif

        <form method="POST" action="{{ route('requests.store') }}" class="bg-white p-6 rounded-lg shadow-md">
            @csrf
            <div class="mb-4">
                <label class="block text-sm font-medium">Church</label>
                <select name="church_id" class="border p-2 rounded w-full" required>
                    <option value="">Select Church</option>
                    @foreach ($churches as $church)
                        <option value="{{ $church->id }}">{{ $church->name }} ({{ $church->level }})</option>
                    @endforeach
                </select>
                @error('church_id') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium">Request Type</label>
                <select name="type" class="border p-2 rounded w-full" required>
                    <option value="">Select Type</option>
                    @foreach ($request_types as $type)
                        <option value="{{ $type }}">{{ ucwords(str_replace('_', ' ', $type)) }}</option>
                    @endforeach
                </select>
                @error('type') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium">Details (Optional)</label>
                <textarea name="details[description]" class="border p-2 rounded w-full" placeholder="Additional details..."></textarea>
                @error('details') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
            </div>
            <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded">Create Request</button>
        </form>
    </div>
@endsection