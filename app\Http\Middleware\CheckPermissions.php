<?php

namespace App\Http\Middleware;

use App\Models\AuditLog;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckPermissions
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$permissions): Response
    {
        $user = $request->user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        // Check if user has any of the required permissions
        $hasPermission = false;

        if (empty($permissions)) {
            // No specific permissions required, just need to be authenticated
            $hasPermission = true;
        } else {
            foreach ($permissions as $permission) {
                if ($user->hasPermissionTo($permission)) {
                    $hasPermission = true;
                    break;
                }
            }
        }

        if (!$hasPermission) {
            // Log unauthorized access attempt
            AuditLog::log(
                'unauthorized_access_attempt',
                null,
                [],
                [
                    'user_id' => $user->id,
                    'required_permissions' => $permissions,
                    'user_permissions' => $user->getAllPermissions()->pluck('name')->toArray(),
                    'route' => $request->route()?->getName(),
                    'url' => $request->url(),
                    'method' => $request->method(),
                ],
                "Unauthorized access attempt by {$user->full_name}"
            );

            return response()->json([
                'error' => 'Access denied. Required permissions: ' . implode(', ', $permissions)
            ], 403);
        }

        return $next($request);
    }
}
