<?php

namespace App\Providers;

use App\Services\HudumaSMSService;
use Illuminate\Support\ServiceProvider;

class SMSServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(HudumaSMSService::class, fn() => new HudumaSMSService());

        // Create an alias for backward compatibility during transition
        $this->app->alias(HudumaSMSService::class, 'sms.service');
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
