<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use App\Models\AuditLog;

class RoleController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'permission:manage-permissions']);
    }

    // List all roles
    public function index(Request $request)
    {
        $query = Role::with(['permissions', 'users']);
        
        // Search functionality
        if ($request->search) {
            $query->where('name', 'like', "%{$request->search}%");
        }
        
        // Filter by permission
        if ($request->permission) {
            $query->whereHas('permissions', function ($q) use ($request) {
                $q->where('name', $request->permission);
            });
        }
        
        $roles = $query->withCount(['users', 'permissions'])
                      ->orderBy('name')
                      ->paginate(15);
        
        $permissions = Permission::orderBy('name')->get();
        
        return view('roles.index', compact('roles', 'permissions'));
    }

    // Show role details
    public function show(Role $role)
    {
        $role->load(['permissions', 'users.church']);
        
        return view('roles.show', compact('role'));
    }

    // Show create role form
    public function create()
    {
        $permissions = Permission::orderBy('name')->get();
        $groupedPermissions = $this->groupPermissions($permissions);
        
        return view('roles.create', compact('permissions', 'groupedPermissions'));
    }

    // Store new role
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles,name',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,name'
        ]);

        DB::beginTransaction();
        try {
            $role = Role::create(['name' => $request->name]);
            
            if ($request->permissions) {
                $role->syncPermissions($request->permissions);
            }
            
            AuditLog::log(
                'role_created',
                $role,
                [],
                $role->toArray(),
                "Role '{$role->name}' created by " . Auth::user()->full_name
            );
            
            DB::commit();
            
            return redirect()->route('roles.index')
                           ->with('success', 'Role created successfully.');
                           
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to create role: ' . $e->getMessage()]);
        }
    }

    // Show edit role form
    public function edit(Role $role)
    {
        $permissions = Permission::orderBy('name')->get();
        $groupedPermissions = $this->groupPermissions($permissions);
        $rolePermissions = $role->permissions->pluck('name')->toArray();
        
        return view('roles.edit', compact('role', 'permissions', 'groupedPermissions', 'rolePermissions'));
    }

    // Update role
    public function update(Request $request, Role $role)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles,name,' . $role->id,
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,name'
        ]);

        DB::beginTransaction();
        try {
            $oldData = $role->toArray();
            $oldPermissions = $role->permissions->pluck('name')->toArray();
            
            $role->update(['name' => $request->name]);
            
            $newPermissions = $request->permissions ?? [];
            $role->syncPermissions($newPermissions);
            
            AuditLog::log(
                'role_updated',
                $role,
                array_merge($oldData, ['permissions' => $oldPermissions]),
                array_merge($role->fresh()->toArray(), ['permissions' => $newPermissions]),
                "Role '{$role->name}' updated by " . Auth::user()->full_name
            );
            
            DB::commit();
            
            return redirect()->route('roles.index')
                           ->with('success', 'Role updated successfully.');
                           
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to update role: ' . $e->getMessage()]);
        }
    }

    // Delete role
    public function destroy(Role $role)
    {
        // Prevent deletion of roles that have users
        if ($role->users()->count() > 0) {
            return back()->withErrors(['error' => 'Cannot delete role that has assigned users.']);
        }

        DB::beginTransaction();
        try {
            $roleName = $role->name;
            $roleData = $role->toArray();
            
            $role->delete();
            
            AuditLog::log(
                'role_deleted',
                null,
                $roleData,
                [],
                "Role '{$roleName}' deleted by " . Auth::user()->full_name
            );
            
            DB::commit();
            
            return redirect()->route('roles.index')
                           ->with('success', 'Role deleted successfully.');
                           
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to delete role: ' . $e->getMessage()]);
        }
    }

    // Assign role to user
    public function assignUser(Request $request, Role $role)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id'
        ]);

        $user = User::findOrFail($request->user_id);
        
        if ($user->hasRole($role->name)) {
            return back()->withErrors(['error' => 'User already has this role.']);
        }

        $user->assignRole($role->name);
        
        AuditLog::log(
            'role_assigned',
            $user,
            [],
            ['role' => $role->name],
            "Role '{$role->name}' assigned to {$user->full_name} by " . Auth::user()->full_name
        );
        
        return back()->with('success', 'Role assigned successfully.');
    }

    // Remove role from user
    public function removeUser(Request $request, Role $role)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id'
        ]);

        $user = User::findOrFail($request->user_id);
        
        if (!$user->hasRole($role->name)) {
            return back()->withErrors(['error' => 'User does not have this role.']);
        }

        $user->removeRole($role->name);
        
        AuditLog::log(
            'role_removed',
            $user,
            ['role' => $role->name],
            [],
            "Role '{$role->name}' removed from {$user->full_name} by " . Auth::user()->full_name
        );
        
        return back()->with('success', 'Role removed successfully.');
    }

    // Helper method to group permissions
    private function groupPermissions($permissions)
    {
        $grouped = [];
        
        foreach ($permissions as $permission) {
            $parts = explode('-', $permission->name);
            $action = $parts[0];
            $resource = isset($parts[1]) ? $parts[1] : 'general';
            
            if (!isset($grouped[$resource])) {
                $grouped[$resource] = [];
            }
            
            $grouped[$resource][] = $permission;
        }
        
        return $grouped;
    }
}
