<?php

namespace App\Services;

use App\Models\User;
use App\Models\Church;
use App\Models\NotificationPreference;
use App\Models\AuditLog;
use App\Models\OTP;
use App\Services\NotificationService;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class RegistrationService
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    public function registerUser(array $userData, User $admin): User
    {
        return DB::transaction(function () use ($userData, $admin) {
            // Validate admin permissions
            $this->validateAdminPermissions($admin, $userData['church_id']);

            // Generate temporary password
            $temporaryPassword = $this->generateTemporaryPassword();

            // Create user
            $user = User::create([
                'full_name' => $userData['full_name'],
                'email' => $userData['email'],
                'phone_number' => $userData['phone_number'],
                'password' => Hash::make($temporaryPassword),
                'church_id' => $userData['church_id'],
                'is_first_login' => true,
                'is_active' => true,
            ]);

            // Assign role
            if (isset($userData['role'])) {
                $user->assignRole($userData['role']);
            }

            // Create notification preferences
            $user->notificationPreferences()->create(
                NotificationPreference::getDefaultPreferences()
            );

            // Generate OTP for first login
            $otp = $this->generateOTP($user);

            // Send welcome notifications
            $this->sendWelcomeNotifications($user, $temporaryPassword, $otp);

            // Log the registration
            AuditLog::log(
                'user_registered',
                $user,
                [],
                $user->toArray(),
                "User registered by admin: {$admin->full_name}"
            );

            return $user;
        });
    }

    public function handleFirstLogin(User $user, string $newPassword, string $otp): bool
    {
        return DB::transaction(function () use ($user, $newPassword, $otp) {
            // Validate OTP
            if (!$this->validateOTP($user, $otp)) {
                throw new \Exception('Invalid or expired OTP.');
            }

            // Update password
            $user->update([
                'password' => Hash::make($newPassword),
                'is_first_login' => false,
            ]);

            // Mark OTP as used
            $user->otps()->where('otp', $otp)->update(['used_at' => now()]);

            // Log first login completion
            AuditLog::log(
                'first_login_completed',
                $user,
                ['is_first_login' => true],
                ['is_first_login' => false],
                'User completed first login and set new password'
            );

            return true;
        });
    }

    public function updateUserProfile(User $user, array $data, User $updater = null): User
    {
        return DB::transaction(function () use ($user, $data, $updater) {
            $oldData = $user->toArray();

            // Update allowed fields
            $allowedFields = ['full_name', 'email', 'phone_number'];
            $updateData = array_intersect_key($data, array_flip($allowedFields));

            $user->update($updateData);

            // Log the update
            AuditLog::log(
                'user_profile_updated',
                $user,
                array_intersect_key($oldData, $updateData),
                $updateData,
                $updater ? "Profile updated by: {$updater->full_name}" : 'Profile self-updated'
            );

            return $user->fresh();
        });
    }

    public function changeUserRole(User $user, string $newRole, User $admin): bool
    {
        return DB::transaction(function () use ($user, $newRole, $admin) {
            // Validate admin permissions
            $this->validateRoleChangePermissions($admin, $user, $newRole);

            $oldRoles = $user->getRoleNames()->toArray();

            // Remove all current roles and assign new role
            $user->syncRoles([$newRole]);

            // Send role change notification
            $this->sendRoleChangeNotification($user, $oldRoles, $newRole, $admin);

            // Log the role change
            AuditLog::log(
                'user_role_changed',
                $user,
                ['roles' => $oldRoles],
                ['roles' => [$newRole]],
                "Role changed by admin: {$admin->full_name}"
            );

            return true;
        });
    }

    public function activateUser(User $user, User $admin): bool
    {
        if ($user->is_active) {
            return true;
        }

        $user->activate();

        // Send activation notification
        $this->notificationService->sendUserActivationNotification($user);

        return true;
    }

    public function deactivateUser(User $user, User $admin, string $reason = null): bool
    {
        if (!$user->is_active) {
            return true;
        }

        $user->deactivate();

        // Send deactivation notification
        $this->notificationService->sendUserDeactivationNotification($user, $reason);

        return true;
    }

    public function resetPassword(User $user, User $admin = null): string
    {
        return DB::transaction(function () use ($user, $admin) {
            $temporaryPassword = $this->generateTemporaryPassword();
            $otp = $this->generateOTP($user);

            $user->update([
                'password' => Hash::make($temporaryPassword),
                'is_first_login' => true,
            ]);

            // Send password reset notifications
            $this->notificationService->sendPasswordResetNotification($user, $temporaryPassword, $otp);

            // Log password reset
            AuditLog::log(
                'password_reset',
                $user,
                [],
                [],
                $admin ? "Password reset by admin: {$admin->full_name}" : 'Password reset requested'
            );

            return $temporaryPassword;
        });
    }

    private function validateAdminPermissions(User $admin, int $churchId): void
    {
        if (!$admin->hasPermissionTo('create-users')) {
            throw new \Exception('Admin does not have permission to create users.');
        }

        $targetChurch = Church::findOrFail($churchId);

        // Admin can only create users in their church or descendant churches
        if ($admin->church_id !== $churchId && !$admin->church->isAncestorOf($targetChurch)) {
            throw new \Exception('Admin can only create users in their church hierarchy.');
        }
    }

    private function validateRoleChangePermissions(User $admin, User $user, string $newRole): void
    {
        if (!$admin->hasPermissionTo('manage-permissions')) {
            throw new \Exception('Admin does not have permission to change user roles.');
        }

        // Admin can only change roles for users in their hierarchy
        if (!$admin->church->isAncestorOf($user->church) && $admin->church_id !== $user->church_id) {
            throw new \Exception('Admin can only change roles for users in their church hierarchy.');
        }

        // Validate role is appropriate for the user's church level
        $validRoles = $user->church->level->getRolesByLevel();
        if (!in_array($newRole, $validRoles)) {
            throw new \Exception("Role '{$newRole}' is not valid for {$user->church->level->value} level church.");
        }
    }

    private function generateTemporaryPassword(): string
    {
        return 'FPCT' . Str::random(8) . rand(10, 99);
    }

    private function generateOTP(User $user): string
    {
        return OTP::generateForUser($user, 'registration', 30)->otp;
    }

    private function validateOTP(User $user, string $otp): bool
    {
        return $user->otps()
                   ->where('otp', $otp)
                   ->where('expires_at', '>', now())
                   ->whereNull('used_at')
                   ->exists();
    }

    private function sendWelcomeNotifications(User $user, string $temporaryPassword, string $otp): void
    {
        $this->notificationService->sendWelcomeEmail($user, $temporaryPassword, $otp);
        $this->notificationService->sendWelcomeSMS($user, $temporaryPassword, $otp);
    }

    private function sendRoleChangeNotification(User $user, array $oldRoles, string $newRole, User $admin): void
    {
        $this->notificationService->sendRoleChangeNotification($user, $oldRoles, $newRole, $admin);
    }

    public function getUsersByChurch(Church $church, User $admin): \Illuminate\Database\Eloquent\Collection
    {
        // Admin can view users in their church and descendant churches
        $allowedChurchIds = [$church->id];
        
        if ($admin->church->isAncestorOf($church) || $admin->church_id === $church->id) {
            $descendants = $church->getAllDescendants();
            $allowedChurchIds = array_merge($allowedChurchIds, $descendants->pluck('id')->toArray());
        }

        return User::whereIn('church_id', $allowedChurchIds)
                   ->with(['church', 'roles'])
                   ->get();
    }

    public function searchUsers(string $query, User $admin): \Illuminate\Database\Eloquent\Collection
    {
        // Get all churches the admin can access
        $allowedChurchIds = [$admin->church_id];
        $descendants = $admin->church->getAllDescendants();
        $allowedChurchIds = array_merge($allowedChurchIds, $descendants->pluck('id')->toArray());

        return User::whereIn('church_id', $allowedChurchIds)
                   ->where(function ($q) use ($query) {
                       $q->where('full_name', 'ILIKE', "%{$query}%")
                         ->orWhere('email', 'ILIKE', "%{$query}%")
                         ->orWhere('phone_number', 'LIKE', "%{$query}%");
                   })
                   ->with(['church', 'roles'])
                   ->get();
    }
}
