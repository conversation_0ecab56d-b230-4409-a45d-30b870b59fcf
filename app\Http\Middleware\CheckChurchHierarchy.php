<?php

namespace App\Http\Middleware;

use App\Models\Church;
use App\Models\User;
use App\Services\ChurchHierarchyService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckChurchHierarchy
{
    protected ChurchHierarchyService $hierarchyService;

    public function __construct(ChurchHierarchyService $hierarchyService)
    {
        $this->hierarchyService = $hierarchyService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ?string $resource = null): Response
    {
        $user = $request->user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        // Get the target church ID from route parameters or request
        $churchId = $this->getTargetChurchId($request);

        if ($churchId && !$this->canAccessChurch($user, $churchId, $resource)) {
            return response()->json([
                'error' => 'Access denied. You do not have permission to access this church resource.'
            ], 403);
        }

        return $next($request);
    }

    private function getTargetChurchId(Request $request): ?int
    {
        // Check route parameters
        if ($request->route('church')) {
            return (int) $request->route('church');
        }

        if ($request->route('church_id')) {
            return (int) $request->route('church_id');
        }

        // Check request data
        if ($request->has('church_id')) {
            return (int) $request->input('church_id');
        }

        // For user-related operations, check the user's church
        if ($request->route('user')) {
            $targetUser = User::find($request->route('user'));
            return $targetUser?->church_id;
        }

        return null;
    }

    private function canAccessChurch(User $user, int $churchId, ?string $resource): bool
    {
        $targetChurch = Church::find($churchId);

        if (!$targetChurch) {
            return false;
        }

        $accessibleChurches = $this->hierarchyService->getChurchesUserCanAccess($user);
        $canAccess = $accessibleChurches->contains('id', $churchId);

        // Additional checks based on resource type
        if ($resource && $canAccess) {
            return $this->checkResourceSpecificPermissions($user, $targetChurch, $resource);
        }

        return $canAccess;
    }

    private function checkResourceSpecificPermissions(User $user, Church $targetChurch, string $resource): bool
    {
        return match ($resource) {
            'manage' => $this->hierarchyService->getChurchesUserCanManage($user)->contains('id', $targetChurch->id),
            'users' => $user->hasPermissionTo('view-users') && $this->canAccessChurchUsers($user, $targetChurch),
            'requests' => $user->hasPermissionTo('view-requests'),
            'messages' => $user->hasPermissionTo('view-messages'),
            'leaders' => $user->hasPermissionTo('view-leaders'),
            default => true,
        };
    }

    private function canAccessChurchUsers(User $user, Church $targetChurch): bool
    {
        // Users can access user data in their church and descendant churches
        return $user->church_id === $targetChurch->id ||
               $user->church->isAncestorOf($targetChurch);
    }
}
