@extends('layouts.app')

@section('title', __('permissions.permission_details'))
@section('page-title', __('permissions.permission_details'))

@section('breadcrumbs')
    <li>
        <span class="mx-2">/</span>
        <a href="{{ route('permissions.index') }}" class="hover:text-gray-700">{{ __('permissions.permissions') }}</a>
    </li>
    <li>
        <span class="mx-2">/</span>
        <span class="font-medium text-gray-900">{{ $permission->name }}</span>
    </li>
@endsection

@section('page-actions')
    <div class="flex items-center space-x-3">
        <!-- Language Switcher -->
        <x-language-switcher position="bottom-right" size="normal" />
        
        <a href="{{ route('permissions.edit', $permission) }}"
           class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <i class="fas fa-edit mr-2"></i>
            {{ __('common.edit') }}
        </a>
        <a href="{{ route('permissions.index') }}"
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <i class="fas fa-arrow-left mr-2"></i>
            {{ __('common.back') }}
        </a>
    </div>
@endsection

@section('content')
    <div class="max-w-6xl mx-auto space-y-6">
        <!-- Quick Language Switcher -->
        <div class="flex justify-end">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-2">
                <div class="flex items-center space-x-2 text-sm">
                    <i class="fas fa-globe text-gray-400"></i>
                    <span class="text-gray-600">{{ __('common.language') }}:</span>
                    <div class="flex space-x-1">
                        @foreach(config('app.locale_names') as $locale => $info)
                            <button onclick="switchLanguage('{{ $locale }}')"
                                    class="px-2 py-1 rounded text-xs font-medium transition-colors duration-200 {{ app()->getLocale() === $locale ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100' }}">
                                {{ $info['flag'] }} {{ $info['native'] }}
                            </button>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <!-- Permission Header -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="bg-gradient-to-r from-purple-600 to-purple-700 px-6 py-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-white">{{ $permission->name }}</h1>
                        <div class="flex items-center mt-2 space-x-4">
                            <span class="text-purple-100 flex items-center">
                                <i class="fas fa-key mr-2"></i>
                                {{ __('permissions.permission') }}
                            </span>
                            @if($permission->guard_name)
                                <span class="text-purple-100 flex items-center">
                                    <i class="fas fa-shield-alt mr-2"></i>
                                    {{ __('permissions.guard') }}: {{ $permission->guard_name }}
                                </span>
                            @endif
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-purple-100 text-sm">{{ __('permissions.permission_id') }}</div>
                        <div class="text-white text-2xl font-bold">#{{ $permission->id }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permission Information -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Basic Information -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-info-circle mr-2 text-blue-600"></i>
                        {{ __('permissions.basic_information') }}
                    </h3>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-600">{{ __('permissions.permission_name') }}</label>
                        <p class="mt-1 text-lg font-semibold text-gray-900">{{ $permission->name }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">{{ __('permissions.guard_name') }}</label>
                        <p class="mt-1 text-lg text-gray-900">{{ $permission->guard_name ?? __('common.not_specified') }}</p>
                    </div>
                    <div class="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
                        <div>
                            <label class="block text-sm font-medium text-gray-600">{{ __('common.created_at') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $permission->created_at->format('M d, Y') }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">{{ __('common.updated_at') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $permission->updated_at->format('M d, Y') }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Roles with this Permission -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-users-cog mr-2 text-green-600"></i>
                        {{ __('permissions.roles_with_permission') }}
                        <span class="ml-2 bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                            {{ $permission->roles->count() }}
                        </span>
                    </h3>
                </div>
                <div class="p-6">
                    @if($permission->roles->count() > 0)
                        <div class="space-y-3">
                            @foreach($permission->roles as $role)
                                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{{ $role->name }}</p>
                                        <p class="text-xs text-gray-500">{{ $role->users->count() }} {{ __('common.users') }}</p>
                                    </div>
                                    <a href="{{ route('roles.show', $role) }}" 
                                       class="text-green-600 hover:text-green-800 text-sm font-medium">
                                        {{ __('common.view') }} →
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-6">
                            <i class="fas fa-users-cog text-gray-300 text-3xl mb-3"></i>
                            <p class="text-gray-500">{{ __('permissions.no_roles_assigned') }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection
