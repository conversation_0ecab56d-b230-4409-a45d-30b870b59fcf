<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Church;
use App\Models\Transaction;
use App\Services\HudumaSMSService;
use App\Services\NotificationService;
use App\Enums\TransactionStatus;
use App\Enums\TransactionType;
use App\Enums\PaymentMethod;
use App\Events\TransactionCompleted;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Mockery;

class TransactionSMSNotificationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock the SMS service to avoid actual API calls during testing
        $this->mockSMSService = Mockery::mock(HudumaSMSService::class);
        $this->app->instance(HudumaSMSService::class, $this->mockSMSService);
    }

    public function test_sms_notification_sent_when_transaction_completed()
    {
        Event::fake();

        // Create test data
        $user = User::factory()->create([
            'phone_number' => '+255712345678',
            'full_name' => '<PERSON>'
        ]);

        $fromChurch = Church::factory()->create(['name' => 'Test Branch Church']);
        $toChurch = Church::factory()->create(['name' => 'Test Parish Church']);

        $transaction = Transaction::factory()->create([
            'transaction_id' => 'TXN-TEST123-********120000',
            'reference_number' => 'REF-TEST123-********',
            'from_church_id' => $fromChurch->id,
            'to_church_id' => $toChurch->id,
            'initiated_by_user_id' => $user->id,
            'amount' => 50000.00,
            'currency' => 'TZS',
            'type' => TransactionType::REVENUE_COLLECTION,
            'status' => TransactionStatus::PROCESSING,
            'description' => 'Monthly revenue collection',
            'payment_method' => PaymentMethod::MOBILE_MONEY,
        ]);

        // Mock SMS service to expect a call
        $this->mockSMSService
            ->shouldReceive('sendSMS')
            ->once()
            ->with(
                '+255712345678',
                Mockery::pattern('/FPCT Transaction Completed!.*Revenue Collection.*TZS 50,000\.00.*Test Branch Church.*Test Parish Church.*Monthly revenue collection.*REF-TEST123-********.*TXN-TEST123-********120000/s'),
                'transaction',
                $user->id
            )
            ->andReturn(true);

        // Update transaction status to completed (this should trigger the observer)
        $transaction->update([
            'status' => TransactionStatus::COMPLETED,
            'completed_at' => now(),
        ]);

        // Assert that the TransactionCompleted event was fired
        Event::assertDispatched(TransactionCompleted::class, function ($event) use ($transaction) {
            return $event->transaction->id === $transaction->id;
        });
    }

    public function test_sms_notification_contains_all_required_information()
    {
        // Create test data
        $user = User::factory()->create([
            'phone_number' => '+************',
            'full_name' => 'Jane Smith'
        ]);

        $fromChurch = Church::factory()->create(['name' => 'St. John Branch']);
        $toChurch = Church::factory()->create(['name' => 'Dar es Salaam Parish']);

        $transaction = Transaction::factory()->create([
            'transaction_id' => 'TXN-ABC789-**************',
            'reference_number' => 'REF-ABC789-********',
            'from_church_id' => $fromChurch->id,
            'to_church_id' => $toChurch->id,
            'initiated_by_user_id' => $user->id,
            'amount' => 75000.50,
            'currency' => 'TZS',
            'type' => TransactionType::CONTRIBUTION,
            'status' => TransactionStatus::PROCESSING,
            'description' => 'Special building fund contribution',
            'payment_method' => PaymentMethod::BANK_TRANSFER,
        ]);

        // Mock SMS service and capture the message content
        $capturedMessage = null;
        $this->mockSMSService
            ->shouldReceive('sendSMS')
            ->once()
            ->with(
                '+************',
                Mockery::capture($capturedMessage),
                'transaction',
                $user->id
            )
            ->andReturn(true);

        // Complete the transaction
        $transaction->update([
            'status' => TransactionStatus::COMPLETED,
            'completed_at' => now(),
        ]);

        // Verify the SMS message contains all required information
        $this->assertStringContainsString('FPCT Transaction Completed!', $capturedMessage);
        $this->assertStringContainsString('Type: Contribution', $capturedMessage);
        $this->assertStringContainsString('Amount: TZS 75,000.50', $capturedMessage);
        $this->assertStringContainsString('From: St. John Branch', $capturedMessage);
        $this->assertStringContainsString('To: Dar es Salaam Parish', $capturedMessage);
        $this->assertStringContainsString('Purpose: Special building fund contribution', $capturedMessage);
        $this->assertStringContainsString('Ref: REF-ABC789-********', $capturedMessage);
        $this->assertStringContainsString('ID: TXN-ABC789-**************', $capturedMessage);
    }

    public function test_sms_notification_not_sent_for_non_completed_transactions()
    {
        // Create test data
        $user = User::factory()->create(['phone_number' => '+255712345678']);
        $fromChurch = Church::factory()->create();
        $toChurch = Church::factory()->create();

        $transaction = Transaction::factory()->create([
            'from_church_id' => $fromChurch->id,
            'to_church_id' => $toChurch->id,
            'initiated_by_user_id' => $user->id,
            'status' => TransactionStatus::PENDING,
        ]);

        // Mock SMS service should not be called
        $this->mockSMSService
            ->shouldNotReceive('sendSMS');

        // Update transaction to failed status
        $transaction->update(['status' => TransactionStatus::FAILED]);

        // Update transaction to cancelled status
        $transaction->update(['status' => TransactionStatus::CANCELLED]);
    }

    public function test_notification_service_handles_sms_failure_gracefully()
    {
        // Create test data
        $user = User::factory()->create(['phone_number' => '+255712345678']);
        $fromChurch = Church::factory()->create();
        $toChurch = Church::factory()->create();

        $transaction = Transaction::factory()->create([
            'from_church_id' => $fromChurch->id,
            'to_church_id' => $toChurch->id,
            'initiated_by_user_id' => $user->id,
            'status' => TransactionStatus::PROCESSING,
        ]);

        // Mock SMS service to return false (failure)
        $this->mockSMSService
            ->shouldReceive('sendSMS')
            ->once()
            ->andReturn(false);

        // Complete the transaction - should not throw exception even if SMS fails
        $transaction->update([
            'status' => TransactionStatus::COMPLETED,
            'completed_at' => now(),
        ]);

        // Transaction should still be completed
        $this->assertEquals(TransactionStatus::COMPLETED, $transaction->fresh()->status);
    }

    public function test_sms_notification_not_sent_for_user_without_phone_number()
    {
        // Create test data with user without phone number
        $user = User::factory()->create(['phone_number' => null]);
        $fromChurch = Church::factory()->create();
        $toChurch = Church::factory()->create();

        $transaction = Transaction::factory()->create([
            'from_church_id' => $fromChurch->id,
            'to_church_id' => $toChurch->id,
            'initiated_by_user_id' => $user->id,
            'status' => TransactionStatus::PROCESSING,
        ]);

        // Mock SMS service should not be called
        $this->mockSMSService
            ->shouldNotReceive('sendSMS');

        // Complete the transaction
        $transaction->update([
            'status' => TransactionStatus::COMPLETED,
            'completed_at' => now(),
        ]);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
