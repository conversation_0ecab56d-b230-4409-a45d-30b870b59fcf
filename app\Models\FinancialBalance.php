<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FinancialBalance extends Model
{
    protected $fillable = [
        'church_id',
        'available_balance',
        'pending_balance',
        'reserved_balance',
        'total_received',
        'total_sent',
        'currency',
        'last_updated_at',
    ];

    protected $casts = [
        'available_balance' => 'decimal:2',
        'pending_balance' => 'decimal:2',
        'reserved_balance' => 'decimal:2',
        'total_received' => 'decimal:2',
        'total_sent' => 'decimal:2',
        'last_updated_at' => 'datetime',
    ];

    // Relationships
    public function church(): BelongsTo
    {
        return $this->belongsTo(Church::class);
    }

    // Business Logic Methods
    public function getTotalBalance(): float
    {
        return $this->available_balance + $this->pending_balance + $this->reserved_balance;
    }

    public function getNetBalance(): float
    {
        return $this->total_received - $this->total_sent;
    }

    public function add(float $amount, string $type = 'available'): void
    {
        $field = $type . '_balance';
        
        $this->increment($field, $amount);
        $this->increment('total_received', $amount);
        $this->update(['last_updated_at' => now()]);
    }

    public function deduct(float $amount, string $type = 'available'): bool
    {
        $field = $type . '_balance';
        
        if ($this->{$field} < $amount) {
            return false; // Insufficient funds
        }

        $this->decrement($field, $amount);
        $this->increment('total_sent', $amount);
        $this->update(['last_updated_at' => now()]);

        return true;
    }

    public function reserve(float $amount): bool
    {
        if ($this->available_balance < $amount) {
            return false; // Insufficient available funds
        }

        $this->decrement('available_balance', $amount);
        $this->increment('reserved_balance', $amount);
        $this->update(['last_updated_at' => now()]);

        return true;
    }

    public function unreserve(float $amount): bool
    {
        if ($this->reserved_balance < $amount) {
            return false; // Insufficient reserved funds
        }

        $this->decrement('reserved_balance', $amount);
        $this->increment('available_balance', $amount);
        $this->update(['last_updated_at' => now()]);

        return true;
    }

    public function movePendingToAvailable(float $amount): bool
    {
        if ($this->pending_balance < $amount) {
            return false; // Insufficient pending funds
        }

        $this->decrement('pending_balance', $amount);
        $this->increment('available_balance', $amount);
        $this->update(['last_updated_at' => now()]);

        return true;
    }

    public function hasMinimumBalance(float $minimumAmount): bool
    {
        return $this->available_balance >= $minimumAmount;
    }

    public function getBalanceStatus(): string
    {
        $balance = $this->available_balance;
        
        if ($balance <= 0) {
            return 'critical';
        } elseif ($balance <= 100000) { // 100,000 TZS
            return 'low';
        } elseif ($balance <= 1000000) { // 1,000,000 TZS
            return 'moderate';
        } else {
            return 'healthy';
        }
    }

    public function getBalanceStatusColor(): string
    {
        return match ($this->getBalanceStatus()) {
            'critical' => 'danger',
            'low' => 'warning',
            'moderate' => 'info',
            'healthy' => 'success',
        };
    }

    // Static Methods
    public static function getOrCreateForChurch(int $churchId): self
    {
        return self::firstOrCreate(
            ['church_id' => $churchId],
            [
                'available_balance' => 0,
                'pending_balance' => 0,
                'reserved_balance' => 0,
                'total_received' => 0,
                'total_sent' => 0,
                'currency' => 'TZS',
                'last_updated_at' => now(),
            ]
        );
    }
}
