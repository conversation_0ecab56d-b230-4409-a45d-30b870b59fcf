<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('financial_reports', function (Blueprint $table) {
            $table->id();
            $table->string('report_name');
            $table->string('report_type'); // monthly, quarterly, annual, custom, contribution_summary
            $table->foreignId('church_id')->constrained('churches')->onDelete('cascade');
            $table->foreignId('generated_by_user_id')->constrained('users')->onDelete('cascade');
            
            $table->date('period_start');
            $table->date('period_end');
            $table->json('report_data'); // Serialized report data
            $table->json('filters')->nullable(); // Applied filters
            
            // File storage
            $table->string('pdf_path')->nullable();
            $table->string('excel_path')->nullable();
            $table->string('status')->default('generating'); // generating, completed, failed
            
            $table->timestamps();

            $table->index(['church_id', 'report_type']);
            $table->index(['period_start', 'period_end']);
            $table->index(['status']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('financial_reports');
    }
};
