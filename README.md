# FPCT Church Management System

<p align="center">
  <img src="https://img.shields.io/badge/Laravel-12.x-red.svg" alt="Laravel Version">
  <img src="https://img.shields.io/badge/PHP-8.2+-blue.svg" alt="PHP Version">
  <img src="https://img.shields.io/badge/PostgreSQL-13+-green.svg" alt="PostgreSQL Version">
  <img src="https://img.shields.io/badge/License-MIT-yellow.svg" alt="License">
</p>

## 📋 Table of Contents

- [Overview](#overview)
- [Features](#features)
- [System Architecture](#system-architecture)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Configuration](#configuration)
- [Usage](#usage)
- [API Documentation](#api-documentation)
- [Testing](#testing)
- [Deployment](#deployment)
- [Contributing](#contributing)
- [Support](#support)
- [License](#license)

## 🏢 Overview

The **FPCT (Free Pentecostal Church of Tanzania) Management System** is a comprehensive church administration platform designed to streamline operations across a hierarchical church structure. Built with Laravel 12 and modern web technologies, it provides robust tools for managing congregations, communications, and administrative workflows.

### Mission Statement
To provide a unified, scalable, and user-friendly platform that empowers church leaders at all levels to effectively manage their congregations, facilitate communication, and maintain organizational oversight across the entire FPCT network.

## ✨ Features

### 🏛️ Church Hierarchy Management
- **5-Level Hierarchical Structure**: National → Regional → Local → Parish → Branch
- **Dynamic Church Creation**: Add new churches with automatic hierarchy validation
- **Leadership Management**: Assign and manage church leaders with role-based permissions
- **Statistical Reporting**: Real-time statistics for users and sub-churches

### 👥 User Management
- **Role-Based Access Control**: Hierarchical permissions system with 15+ predefined roles
- **User Onboarding**: Streamlined registration and profile completion workflow
- **Profile Management**: Comprehensive user profiles with photos and personal information
- **Account Security**: Multi-factor authentication with OTP verification

### 💬 Communication System
- **Internal Messaging**: Secure messaging between users and groups
- **Announcements**: Broadcast important information across church levels
- **SMS Integration**: Huduma SMS service for Tanzania and African markets
- **Real-time Notifications**: WebSocket-powered instant notifications

### 💰 Revenue Management System
- **Hierarchical Financial Flow**: Revenue collection from lower to upper church levels
- **AzamPay Integration**: Secure mobile money and bank transfer payments
- **Multiple Payment Methods**: Mobile money, bank transfers, and cash transactions
- **Treasurer Roles**: Dedicated financial management roles at all church levels
- **Financial Dashboards**: Real-time financial statistics and reporting
- **Receipt Generation**: Automatic receipt creation for all transactions
- **Transaction Tracking**: Complete audit trail for all financial activities
- **Balance Management**: Church-level financial balance tracking
- **Contribution Management**: Special contributions and fundraising campaigns
- **SMS Notifications**: Automatic SMS confirmations for all successful transactions

### 📋 Request & Approval System
- **Workflow Management**: Multi-step approval processes for church requests
- **Request Types**: Support for various administrative requests
- **Audit Trail**: Complete tracking of all approval workflows
- **Automated Notifications**: Email and SMS alerts for pending approvals

### 🌍 Multilingual Support
- **Dual Language**: Full English and Swahili translation support
- **Dynamic Language Switching**: Users can change language preferences
- **Localized Content**: All UI elements and system messages translated

### 🔐 Security & Compliance
- **Advanced Authentication**: Laravel Sanctum with role-based middleware
- **Audit Logging**: Comprehensive activity tracking for compliance
- **Data Protection**: GDPR-compliant data handling and user privacy
- **Security Monitoring**: Real-time security event logging

### 📊 Analytics & Reporting
- **Dashboard Analytics**: Real-time statistics and key performance indicators
- **Church Statistics**: Member counts, growth metrics, and activity reports
- **User Activity**: Detailed user engagement and system usage analytics
- **Export Capabilities**: Data export for external reporting tools

## 🏗️ System Architecture

### Technology Stack
- **Backend**: Laravel 12 (PHP 8.2+)
- **Database**: PostgreSQL 13+
- **Frontend**: Blade Templates with Tailwind CSS 4.0
- **Real-time**: Laravel Echo with Pusher/WebSockets
- **SMS Service**: Huduma SMS API
- **Payment Gateway**: AzamPay API for Tanzania
- **Authentication**: Laravel Sanctum with Spatie Permissions
- **Build Tools**: Vite 6.2+ with modern JavaScript

### Database Schema
```
├── users (User management and authentication)
├── churches (Hierarchical church structure)
├── church_leaders (Leadership assignments)
├── messages (Internal communication system)
├── message_recipients (Message delivery tracking)
├── requests (Administrative request system)
├── approval_workflows (Multi-step approval processes)
├── transactions (Financial transaction records)
├── financial_balances (Church-level balance tracking)
├── contributions (Special contribution campaigns)
├── receipts (Transaction receipt generation)
├── sms_logs (SMS delivery tracking)
├── roles & permissions (RBAC system)
├── audit_logs (Activity tracking)
├── otps (Two-factor authentication)
└── notification_preferences (User communication settings)
```

## 📋 Prerequisites

### System Requirements
- **PHP**: 8.2 or higher
- **Database**: PostgreSQL 13+ (recommended) or MySQL 8.0+
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **Node.js**: 18+ (for asset compilation)
- **Composer**: 2.0+

### Development Environment
- **Operating System**: Windows 10+, macOS 10.15+, or Ubuntu 20.04+
- **Memory**: 4GB RAM minimum, 8GB recommended
- **Storage**: 2GB free space for application and dependencies

### External Services
- **Huduma SMS Account**: For SMS functionality (Tanzania/Africa)
- **AzamPay Account**: For mobile money and bank transfer payments
- **Email Service**: SMTP server for email notifications
- **Broadcasting Service**: Pusher or Redis for real-time features (optional)

## 🚀 Installation

### 1. Clone the Repository
```bash
git clone https://github.com/your-organization/fpct-system.git
cd fpct-system
```

### 2. Install Dependencies
```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
```

### 3. Environment Configuration
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### 4. Database Setup
```bash
# Create database (PostgreSQL example)
createdb fpct_system

# Run migrations and seeders
php artisan migrate --seed
```

### 5. Build Assets
```bash
# Development build
npm run dev

# Production build
npm run build
```

### 6. Start Development Server
```bash
# Start all services (recommended)
composer run dev

# Or start individually
php artisan serve
php artisan queue:work
npm run dev
```

## ⚙️ Configuration

### Environment Variables

#### Database Configuration
```env
DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=fpct_system
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

#### SMS Service (Huduma SMS)
```env
HUDUMASMS_API_TOKEN=your_api_token_here
HUDUMASMS_SENDER_ID=255787504956
HUDUMASMS_BASE_URL=https://sms-api.huduma.cloud/api/v3
HUDUMASMS_CALLBACK_URL=https://your-domain.com/api/sms/callback
```

#### Payment Gateway (AzamPay)
```env
AZAMPAY_BASE_URL=https://sandbox.azampay.co.tz
AZAMPAY_APP_NAME=FPCT
AZAMPAY_CLIENT_ID=your_client_id
AZAMPAY_CLIENT_SECRET=your_client_secret
AZAMPAY_API_KEY=your_api_key
AZAMPAY_CALLBACK_URL=https://your-domain.com/api/azampay/callback
```

#### Email Configuration
```env
MAIL_MAILER=smtp
MAIL_HOST=your_smtp_host
MAIL_PORT=587
MAIL_USERNAME=your_email
MAIL_PASSWORD=your_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="FPCT Church Management System (CMS)"
```

#### Broadcasting (Real-time Features)
```env
BROADCAST_CONNECTION=pusher
PUSHER_APP_ID=your_app_id
PUSHER_APP_KEY=your_app_key
PUSHER_APP_SECRET=your_app_secret
PUSHER_APP_CLUSTER=your_cluster
```

### System Configuration Commands

#### Check System Status
```bash
php artisan system:status
```

#### Reset Admin Password
```bash
php artisan admin:reset-password
# or with custom password
php artisan admin:reset-password --password=newpassword123
```

#### Test SMS Service
```bash
php artisan sms:test +************ --message="Test message"
```

## 👤 Default Admin Credentials

### Super Administrator
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Role**: Archbishop (Full System Access)
- **Church**: FPCT National Headquarters

### Test Users
- **National Assistant**: `<EMAIL>` / `assistant123`
- **General Secretary**: `<EMAIL>` / `secretary123`

> ⚠️ **Security Notice**: Change default passwords immediately in production environments.

## 📖 Usage

### Getting Started

1. **Access the System**
   ```bash
   php artisan serve
   # Navigate to http://localhost:8000
   ```

2. **First Login**
   - Use admin credentials to access the system
   - Complete profile setup during first login
   - Change default password for security

3. **Church Hierarchy Setup**
   - Create regional churches under national headquarters
   - Add local churches under regional churches
   - Assign leadership roles to appropriate users

### User Management

#### Creating New Users
1. Navigate to **Users** → **Create User**
2. Fill in basic information (admin registration)
3. User completes profile on first login
4. Assign appropriate roles and church affiliation

#### Role Assignment
- **Archbishop**: Full system access
- **Bishop**: Diocese church management
- **Pastor**: Local church administration
- **Parish Pastor**: Parish-level operations
- **Branch Pastor**: Branch-level management

### Church Administration

#### Adding New Churches
1. Go to **Churches** → **Create Church**
2. Select appropriate level in hierarchy
3. Choose parent church (if applicable)
4. Assign church leaders
5. Configure church-specific settings

#### Managing Church Hierarchy
- View hierarchical structure in church dashboard
- Monitor statistics for each church level
- Track user distribution across churches
- Generate reports for administrative oversight

### Communication Features

#### Internal Messaging
- Send direct messages to individual users
- Create group messages for teams
- Broadcast announcements to church levels
- Track message delivery and read status

#### SMS Integration
- Send welcome SMS to new users
- OTP verification for security
- Bulk SMS for announcements
- Delivery reports and balance monitoring

### Request Management

#### Submitting Requests
1. Navigate to **Requests** → **New Request**
2. Select request type and provide details
3. Submit for approval workflow
4. Track request status and updates

#### Approval Workflows
- Multi-step approval processes
- Automatic routing to appropriate approvers
- Email and SMS notifications
- Complete audit trail

### Revenue Management

#### Financial Hierarchy
The FPCT Church Management System (CMS) implements a hierarchical financial flow where:
- **Branch churches** send revenue to **Parish churches**
- **Parish churches** send revenue to **Local churches**
- **Local churches** send revenue to **Regional churches**
- **Regional churches** send revenue to **National headquarters**

#### Transaction Types
1. **Revenue Collection**: Regular revenue flow between church levels
2. **Contributions**: Special fundraising campaigns and contributions
3. **Transfers**: General transfers between church accounts
4. **Refunds**: Refund of previous transactions

#### Payment Methods
- **Mobile Money**: M-Pesa, Tigo Pesa, Airtel Money via AzamPay
- **Bank Transfer**: Direct bank transfers via AzamPay
- **Cash**: Manual cash transactions recorded in the system

#### Creating Transactions
1. Navigate to **Revenue** → **New Transaction**
2. Select transaction type and amount
3. Choose payment method and provide details
4. Add description/purpose for the transaction
5. Submit for processing

#### Transaction Notifications
Every successful transaction triggers:
- **Real-time notification** in the system
- **Email confirmation** with transaction details
- **SMS notification** with complete transaction information including:
  - Transaction type and amount
  - Source and destination churches
  - Transaction purpose/description
  - Reference number and transaction ID

#### Financial Dashboards
- Real-time balance tracking for each church
- Transaction history and reporting
- Revenue statistics by period (week/month/quarter/year)
- Export capabilities for financial reports
- Receipt generation and management

## 🔧 API Documentation

### Authentication Endpoints

#### Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password"
}
```

#### User Profile
```http
GET /api/user/profile
Authorization: Bearer {token}
```

### Church Management API

#### List Churches
```http
GET /api/churches
Authorization: Bearer {token}
```

#### Create Church
```http
POST /api/churches
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Church Name",
  "level": "Parish",
  "parent_church_id": 1,
  "location": "Location",
  "date_established": "2024-01-01"
}
```

### Messaging API

#### Send Message
```http
POST /api/messages
Authorization: Bearer {token}
Content-Type: application/json

{
  "content": "Message content",
  "recipients": [1, 2, 3],
  "is_announcement": false
}
```

#### Get Messages
```http
GET /api/messages
Authorization: Bearer {token}
```

### SMS API

#### Send SMS
```http
POST /api/sms/send
Authorization: Bearer {token}
Content-Type: application/json

{
  "phone_number": "+************",
  "message": "SMS content"
}
```

### Revenue Management API

#### Create Transaction
```http
POST /api/transactions
Authorization: Bearer {token}
Content-Type: application/json

{
  "amount": 50000.00,
  "payment_method": "mobile_money",
  "description": "Monthly revenue collection",
  "contribution_id": null,
  "payment_details": {
    "phone_number": "+************",
    "provider": "mpesa"
  }
}
```

#### Get Transactions
```http
GET /api/transactions
Authorization: Bearer {token}
```

#### Get Transaction Details
```http
GET /api/transactions/{id}
Authorization: Bearer {token}
```

#### Get Financial Statistics
```http
GET /api/churches/{id}/revenue-statistics?period=month
Authorization: Bearer {token}
```

#### AzamPay Callback
```http
POST /api/azampay/callback
Content-Type: application/json

{
  "externalId": "TXN-ABC123-20240101120000",
  "transactionId": "azampay_txn_123",
  "status": "success",
  "amount": 50000.00
}
```

## 🧪 Testing

### Running Tests

#### Unit Tests
```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Unit

# Run with coverage
php artisan test --coverage
```

#### Feature Tests
```bash
# Test specific features
php artisan test --testsuite=Feature

# Test with database refresh
php artisan test --env=testing
```

### Test Database Setup
```bash
# Create test database
createdb fpct_system_test

# Configure test environment
cp .env .env.testing
# Update database name in .env.testing

# Run migrations for testing
php artisan migrate --env=testing
```

### SMS Testing
```bash
# Test SMS connectivity
php artisan sms:test +************

# Test with custom message
php artisan sms:test +************ --message="Test message"
```

## 🚀 Deployment

### Production Environment Setup

#### Server Requirements
- **PHP**: 8.2+ with required extensions
- **Database**: PostgreSQL 13+ or MySQL 8.0+
- **Web Server**: Nginx or Apache with SSL
- **Process Manager**: Supervisor for queue workers
- **Caching**: Redis (recommended)

#### Deployment Steps

1. **Server Preparation**
   ```bash
   # Update system packages
   sudo apt update && sudo apt upgrade -y

   # Install required packages
   sudo apt install php8.2 php8.2-fpm nginx postgresql redis-server
   ```

2. **Application Deployment**
   ```bash
   # Clone repository
   git clone https://github.com/your-org/fpct-system.git
   cd fpct-system

   # Install dependencies
   composer install --optimize-autoloader --no-dev
   npm install && npm run build
   ```

3. **Environment Configuration**
   ```bash
   # Configure production environment
   cp .env.example .env
   php artisan key:generate

   # Set production values
   APP_ENV=production
   APP_DEBUG=false
   ```

4. **Database Migration**
   ```bash
   # Run migrations
   php artisan migrate --force

   # Seed production data
   php artisan db:seed --class=ProductionSeeder
   ```

5. **Queue Workers**
   ```bash
   # Configure Supervisor
   sudo nano /etc/supervisor/conf.d/fpct-worker.conf

   # Start queue workers
   sudo supervisorctl reread
   sudo supervisorctl update
   sudo supervisorctl start fpct-worker:*
   ```

### SSL Configuration

#### Let's Encrypt SSL
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Performance Optimization

#### Caching Configuration
```bash
# Configure caching
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Set up Redis caching
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis
```

#### Database Optimization
```bash
# Optimize database
php artisan optimize

# Index optimization (PostgreSQL)
VACUUM ANALYZE;
```

## 🤝 Contributing

### Development Workflow

1. **Fork the Repository**
   ```bash
   git fork https://github.com/your-org/fpct-system.git
   ```

2. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Development Standards**
   - Follow PSR-12 coding standards
   - Write comprehensive tests
   - Update documentation
   - Use conventional commit messages

4. **Testing Requirements**
   ```bash
   # Run all tests
   php artisan test

   # Check code style
   ./vendor/bin/pint

   # Static analysis
   ./vendor/bin/phpstan analyse
   ```

5. **Submit Pull Request**
   - Provide clear description
   - Include test coverage
   - Update relevant documentation
   - Request code review

### Code Style Guidelines

#### PHP Standards
- Follow PSR-12 coding standard
- Use type declarations
- Write descriptive method names
- Include comprehensive docblocks

#### Database Conventions
- Use snake_case for table and column names
- Include foreign key constraints
- Add appropriate indexes
- Use migrations for schema changes

#### Frontend Guidelines
- Use Tailwind CSS utility classes
- Follow component-based structure
- Implement responsive design
- Ensure accessibility compliance

## 📞 Support

### Documentation
- **System Documentation**: `/docs` directory
- **API Documentation**: Available at `/api/documentation`
- **SMS Integration Guide**: `docs/SMS_INTEGRATION.md`
- **Admin Guide**: `ADMIN_LOGIN.md`

### Troubleshooting

#### Common Issues

1. **Database Connection Errors**
   ```bash
   # Check database status
   php artisan system:status

   # Test connection
   php artisan tinker
   >>> DB::connection()->getPdo();
   ```

2. **SMS Service Issues**
   ```bash
   # Test SMS connectivity
   php artisan sms:test +************

   # Check balance
   php artisan tinker
   >>> app(App\Services\AfricasTalkingSMSService::class)->getBalance();
   ```

3. **Permission Errors**
   ```bash
   # Reset permissions
   sudo chown -R www-data:www-data storage bootstrap/cache
   sudo chmod -R 775 storage bootstrap/cache
   ```

#### Log Files
- **Application Logs**: `storage/logs/laravel.log`
- **Web Server Logs**: `/var/log/nginx/` or `/var/log/apache2/`
- **Database Logs**: Check PostgreSQL/MySQL logs

### Getting Help

#### Community Support
- **GitHub Issues**: Report bugs and feature requests
- **Discussions**: Community Q&A and feature discussions
- **Wiki**: Additional documentation and guides

#### Professional Support
- **Email**: <EMAIL>
- **Phone**: +255 XXX XXX XXX
- **Business Hours**: Monday-Friday, 8:00 AM - 5:00 PM EAT

### System Monitoring

#### Health Checks
```bash
# System status
php artisan system:status

# Application health
curl http://your-domain.com/up

# Database health
php artisan db:monitor
```

#### Performance Monitoring
- Monitor response times
- Track database query performance
- Monitor SMS delivery rates
- Check queue processing times

## 📄 License

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

### Third-Party Licenses
- **Laravel Framework**: MIT License
- **Spatie Laravel Permission**: MIT License
- **Africa's Talking PHP SDK**: MIT License
- **Tailwind CSS**: MIT License

## 🙏 Acknowledgments

### Development Team
- **Lead Developer**: [Your Name]
- **Backend Team**: Laravel specialists
- **Frontend Team**: UI/UX designers
- **QA Team**: Testing and quality assurance

### Special Thanks
- **FPCT Leadership**: For vision and requirements
- **Laravel Community**: For framework and packages
- **Africa's Talking**: For SMS service integration
- **Open Source Contributors**: For various packages and tools

### Technology Partners
- **Laravel**: Web application framework
- **PostgreSQL**: Database management system
- **Africa's Talking**: SMS and communication services
- **Tailwind CSS**: Utility-first CSS framework

---

<p align="center">
  <strong>Built with ❤️ for the Free Pentecostal Church of Tanzania</strong>
</p>

<p align="center">
  <em>Empowering church administration through modern technology</em>
</p>
