<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class TestPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test user permissions and roles';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔐 Permissions & Roles Test');
        $this->info('===========================');

        // Get admin user
        $admin = User::where('email', '<EMAIL>')->first();

        if (!$admin) {
            $this->error('Admin user not found!');
            return 1;
        }

        $this->info("Testing permissions for: {$admin->full_name}");
        $this->info("User role: {$admin->role}");
        $this->info('');

        // Test role assignment
        $roles = $admin->getRoleNames();
        $this->info('📋 Assigned Roles:');
        foreach ($roles as $role) {
            $this->info("  ✓ {$role}");
        }

        if ($roles->isEmpty()) {
            $this->warn('  ⚠ No roles assigned!');
        }

        $this->info('');

        // Test key permissions
        $keyPermissions = [
            'manage-users',
            'manage-churches',
            'manage-requests',
            'approve-requests',
            'send-messages',
            'view-messages',
            'send-announcements',
        ];

        $this->info('🔑 Key Permissions:');
        foreach ($keyPermissions as $permission) {
            $hasPermission = $admin->hasPermissionTo($permission);
            $status = $hasPermission ? '✓' : '✗';
            $this->info("  {$status} {$permission}");
        }

        $this->info('');

        // Test all permissions
        $allPermissions = $admin->getAllPermissions();
        $this->info("📊 Total Permissions: {$allPermissions->count()}");

        if ($allPermissions->count() > 0) {
            $this->info('✅ User has permissions assigned!');
        } else {
            $this->warn('⚠ User has no permissions assigned!');
        }

        return 0;
    }
}
