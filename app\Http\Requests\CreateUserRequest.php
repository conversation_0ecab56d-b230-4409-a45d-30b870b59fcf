<?php

namespace App\Http\Requests;

use App\Models\Church;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Spatie\Permission\Models\Role;

class CreateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasPermissionTo('create-users');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'full_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'unique:users,email'],
            'phone_number' => ['required', 'string', 'unique:users,phone_number'],
            'church_id' => ['required', 'exists:churches,id'],
            'role' => ['required', 'string', Rule::exists('roles', 'name')],
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $this->validateChurchAccess($validator);
            $this->validateRoleForChurch($validator);
        });
    }

    /**
     * Validate that the user can create users in the specified church.
     */
    protected function validateChurchAccess($validator)
    {
        $churchId = $this->input('church_id');
        $targetChurch = Church::find($churchId);
        $userChurch = $this->user()->church;

        if (!$targetChurch) {
            return;
        }

        // User can only create users in their church or descendant churches
        if ($userChurch->id !== $churchId && !$userChurch->isAncestorOf($targetChurch)) {
            $validator->errors()->add('church_id', 'You can only create users in your church hierarchy.');
        }
    }

    /**
     * Validate that the role is appropriate for the church level.
     */
    protected function validateRoleForChurch($validator)
    {
        $churchId = $this->input('church_id');
        $roleName = $this->input('role');

        $church = Church::find($churchId);
        $role = Role::where('name', $roleName)->first();

        if (!$church || !$role) {
            return;
        }

        $validRoles = $church->level->getRolesByLevel();

        if (!in_array($roleName, $validRoles)) {
            $validator->errors()->add('role', "Role '{$roleName}' is not valid for {$church->level->value} level church.");
        }
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'full_name.required' => 'Full name is required.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please provide a valid email address.',
            'email.unique' => 'This email address is already registered.',
            'phone_number.required' => 'Phone number is required.',
            'phone_number.unique' => 'This phone number is already registered.',
            'church_id.required' => 'Church selection is required.',
            'church_id.exists' => 'Selected church does not exist.',
            'role.required' => 'Role selection is required.',
            'role.exists' => 'Selected role does not exist.',
        ];
    }
}
