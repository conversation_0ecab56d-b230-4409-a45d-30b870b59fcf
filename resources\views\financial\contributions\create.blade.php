@extends('layouts.app')

@section('title', __('Create Contribution'))

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ __('Create Contribution Campaign') }}</h1>
                    <p class="mt-2 text-gray-600">{{ __('Set up a new fundraising campaign for lower-level churches') }}</p>
                </div>
                <a href="{{ route('contributions.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-arrow-left mr-2"></i>{{ __('Back to Contributions') }}
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-2">
                <!-- Contribution Form -->
                <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-plus-circle mr-2 text-blue-600"></i>
                            {{ __('Campaign Details') }}
                        </h3>
                    </div>
                    <div class="px-6 py-6">
                        <form method="POST" action="{{ route('contributions.store') }}" class="space-y-6">
                            @csrf

                            <!-- Campaign Name -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700">{{ __('Campaign Name') }} <span class="text-red-500">*</span></label>
                                <input type="text"
                                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-300 @enderror"
                                       id="name"
                                       name="name"
                                       value="{{ old('name') }}"
                                       required
                                       placeholder="{{ __('Enter campaign name...') }}">
                                @error('name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Description -->
                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-700">{{ __('Description') }}</label>
                                <textarea class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('description') border-red-300 @enderror"
                                          id="description"
                                          name="description"
                                          rows="4"
                                          placeholder="{{ __('Describe the purpose and goals of this campaign...') }}">{{ old('description') }}</textarea>
                                @error('description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Campaign Type and Target Amount -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="type" class="block text-sm font-medium text-gray-700">{{ __('Campaign Type') }} <span class="text-red-500">*</span></label>
                                    <select class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('type') border-red-300 @enderror"
                                            id="type"
                                            name="type"
                                            required>
                                        <option value="">{{ __('Select campaign type') }}</option>
                                        <option value="general" {{ old('type') == 'general' ? 'selected' : '' }}>{{ __('General') }}</option>
                                        <option value="special" {{ old('type') == 'special' ? 'selected' : '' }}>{{ __('Special') }}</option>
                                        <option value="emergency" {{ old('type') == 'emergency' ? 'selected' : '' }}>{{ __('Emergency') }}</option>
                                        <option value="project" {{ old('type') == 'project' ? 'selected' : '' }}>{{ __('Project') }}</option>
                                    </select>
                                    @error('type')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div>
                                    <label for="target_amount" class="block text-sm font-medium text-gray-700">{{ __('Target Amount (TZS)') }}</label>
                                    <div class="mt-1 relative rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500 sm:text-sm">TZS</span>
                                        </div>
                                        <input type="number"
                                               class="block w-full pl-12 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('target_amount') border-red-300 @enderror"
                                               id="target_amount"
                                               name="target_amount"
                                               value="{{ old('target_amount') }}"
                                               min="1"
                                               step="0.01"
                                               placeholder="{{ __('Optional target amount') }}">
                                        @error('target_amount')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>
                                    <p class="mt-1 text-sm text-gray-500">{{ __('Leave empty for open-ended campaigns') }}</p>
                                </div>
                            </div>

                            <!-- Campaign Dates -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="start_date" class="block text-sm font-medium text-gray-700">{{ __('Start Date') }} <span class="text-red-500">*</span></label>
                                    <input type="date"
                                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('start_date') border-red-300 @enderror"
                                           id="start_date"
                                           name="start_date"
                                           value="{{ old('start_date', now()->format('Y-m-d')) }}"
                                           required>
                                    @error('start_date')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div>
                                    <label for="end_date" class="block text-sm font-medium text-gray-700">{{ __('End Date') }}</label>
                                    <input type="date"
                                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('end_date') border-red-300 @enderror"
                                           id="end_date"
                                           name="end_date"
                                           value="{{ old('end_date') }}"
                                           placeholder="dd/mm/yyyy">
                                    @error('end_date')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                    <p class="mt-1 text-sm text-gray-500">{{ __('Leave empty for ongoing campaigns') }}</p>
                                </div>
                            </div>

                            <!-- Collection Scope -->
                            <div>
                                <label for="collection_scope" class="block text-sm font-medium text-gray-700">{{ __('Collection Scope') }} <span class="text-red-500">*</span></label>
                                <select class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('collection_scope') border-red-300 @enderror"
                                        id="collection_scope"
                                        name="collection_scope"
                                        required>
                                    <option value="">{{ __('Select target church level') }}</option>
                                    @foreach($targetLevels as $level)
                                        <option value="{{ $level->value }}" {{ old('collection_scope') == $level->value ? 'selected' : '' }}>
                                            {{ ucfirst($level->value) }} Level Churches
                                        </option>
                                    @endforeach
                                </select>
                                @error('collection_scope')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">{{ __('Which level of churches should contribute to this campaign') }}</p>
                            </div>

                            <!-- Target Churches (Optional) -->
                            <div>
                                <label for="target_churches" class="block text-sm font-medium text-gray-700">{{ __('Specific Target Churches (Optional)') }}</label>
                                <select class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('target_churches') border-red-300 @enderror"
                                        id="target_churches"
                                        name="target_churches[]"
                                        multiple>
                                    <!-- Options will be populated via JavaScript based on collection scope -->
                                </select>
                                @error('target_churches')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">{{ __('Leave empty to target all churches at the selected level') }}</p>
                            </div>

                            <!-- Campaign Settings -->
                            <div>
                                <div class="flex items-center">
                                    <input class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded @error('is_mandatory') border-red-300 @enderror"
                                           type="checkbox"
                                           id="is_mandatory"
                                           name="is_mandatory"
                                           value="1"
                                           {{ old('is_mandatory') ? 'checked' : '' }}>
                                    <label class="ml-2 block text-sm text-gray-900" for="is_mandatory">
                                        {{ __('This is a mandatory contribution') }}
                                    </label>
                                </div>
                                @error('is_mandatory')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">{{ __('Mandatory contributions are highlighted and prioritized') }}</p>
                            </div>

                            <!-- Instructions -->
                            <div>
                                <label for="instructions" class="block text-sm font-medium text-gray-700">{{ __('Instructions for Contributors') }}</label>
                                <textarea class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('instructions') border-red-300 @enderror"
                                          id="instructions"
                                          name="instructions"
                                          rows="3"
                                          placeholder="{{ __('Provide specific instructions or guidelines for contributors...') }}">{{ old('instructions') }}</textarea>
                                @error('instructions')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Submit Buttons -->
                            <div class="flex justify-end space-x-3">
                                <a href="{{ route('contributions.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    {{ __('Cancel') }}
                                </a>
                                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-save mr-2"></i>{{ __('Create Campaign') }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-1">
                <!-- Campaign Preview -->
                <div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-eye mr-2 text-blue-600"></i>
                            {{ __('Campaign Preview') }}
                        </h3>
                    </div>
                    <div class="px-6 py-6">
                        <div id="campaign-preview">
                            <h4 class="font-semibold text-gray-900" id="preview-name">{{ __('Campaign Name') }}</h4>
                            <p class="text-gray-600 mt-1" id="preview-description">{{ __('Campaign description will appear here...') }}</p>

                            <div class="mt-4 flex flex-wrap gap-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800" id="preview-type">{{ __('Select campaign type') }}</span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 hidden" id="preview-mandatory">{{ __('Mandatory') }}</span>
                            </div>

                            <div class="mt-4 text-center hidden" id="preview-amounts">
                                <div class="text-2xl font-bold text-blue-600" id="preview-target">0 TZS</div>
                                <div class="text-sm text-gray-500">{{ __('Target Amount') }}</div>
                            </div>

                            <div class="mt-4 grid grid-cols-2 gap-4 text-center">
                                <div class="border-r border-gray-200">
                                    <div class="font-semibold text-gray-900" id="preview-start">{{ old('start_date', now()->format('d/m/Y')) }}</div>
                                    <div class="text-sm text-gray-500">{{ __('Starts') }}</div>
                                </div>
                                <div>
                                    <div class="font-semibold text-gray-900" id="preview-end">{{ __('No End Date') }}</div>
                                    <div class="text-sm text-gray-500">{{ __('Ends') }}</div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <div class="text-sm text-gray-600">
                                    <span class="font-medium">{{ __('Target') }}:</span> <span id="preview-scope">{{ __('Select target church level') }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Help Information -->
                <div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-lightbulb mr-2 text-yellow-600"></i>
                            {{ __('Campaign Guidelines') }}
                        </h3>
                    </div>
                    <div class="px-6 py-6">
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <i class="fas fa-check text-green-500 mt-0.5 mr-3"></i>
                                <span class="text-sm text-gray-600">{{ __('Choose descriptive campaign names') }}</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-green-500 mt-0.5 mr-3"></i>
                                <span class="text-sm text-gray-600">{{ __('Set realistic target amounts') }}</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-green-500 mt-0.5 mr-3"></i>
                                <span class="text-sm text-gray-600">{{ __('Provide clear instructions') }}</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-green-500 mt-0.5 mr-3"></i>
                                <span class="text-sm text-gray-600">{{ __('Monitor campaign progress regularly') }}</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Your Church Info -->
                <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-church mr-2 text-blue-600"></i>
                            {{ __('Your Church') }}
                        </h3>
                    </div>
                    <div class="px-6 py-6">
                        <h4 class="font-semibold text-gray-900">{{ $church->name }}</h4>
                        <p class="text-gray-600 text-sm">{{ ucfirst($church->level->value) }} {{ __('Level') }}</p>
                        <p class="text-gray-600 text-sm">{{ $church->location }}</p>

                        <div class="mt-4">
                            <div class="text-sm text-gray-600">
                                <span class="font-medium">{{ __('Can create campaigns for') }}:</span><br>
                                <div class="mt-2 flex flex-wrap gap-1">
                                    @foreach($targetLevels as $level)
                                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">{{ ucfirst($level->value) }}</span>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form elements
    const nameInput = document.getElementById('name');
    const descriptionInput = document.getElementById('description');
    const typeSelect = document.getElementById('type');
    const targetAmountInput = document.getElementById('target_amount');
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const collectionScopeSelect = document.getElementById('collection_scope');
    const mandatoryCheckbox = document.getElementById('is_mandatory');

    // Preview elements
    const previewName = document.getElementById('preview-name');
    const previewDescription = document.getElementById('preview-description');
    const previewType = document.getElementById('preview-type');
    const previewMandatory = document.getElementById('preview-mandatory');
    const previewTarget = document.getElementById('preview-target');
    const previewAmounts = document.getElementById('preview-amounts');
    const previewStart = document.getElementById('preview-start');
    const previewEnd = document.getElementById('preview-end');
    const previewScope = document.getElementById('preview-scope');

    // Update preview
    function updatePreview() {
        previewName.textContent = nameInput.value || '{{ __("Campaign Name") }}';
        previewDescription.textContent = descriptionInput.value || '{{ __("Campaign description will appear here...") }}';
        previewType.textContent = typeSelect.options[typeSelect.selectedIndex]?.text || '{{ __("Type") }}';
        
        if (mandatoryCheckbox.checked) {
            previewMandatory.classList.remove('hidden');
        } else {
            previewMandatory.classList.add('hidden');
        }

        if (targetAmountInput.value) {
            previewTarget.textContent = new Intl.NumberFormat().format(targetAmountInput.value) + ' TZS';
            previewAmounts.classList.remove('hidden');
        } else {
            previewAmounts.classList.add('hidden');
        }

        previewStart.textContent = startDateInput.value ? new Date(startDateInput.value).toLocaleDateString() : '{{ __("Start Date") }}';
        previewEnd.textContent = endDateInput.value ? new Date(endDateInput.value).toLocaleDateString() : '{{ __("No End Date") }}';
        previewScope.textContent = collectionScopeSelect.options[collectionScopeSelect.selectedIndex]?.text || '{{ __("Select scope") }}';
    }

    // Add event listeners
    [nameInput, descriptionInput, typeSelect, targetAmountInput, startDateInput, endDateInput, collectionScopeSelect, mandatoryCheckbox].forEach(element => {
        element.addEventListener('input', updatePreview);
        element.addEventListener('change', updatePreview);
    });

    // Initial preview update
    updatePreview();

    // Target churches population (simplified - would need AJAX in real implementation)
    collectionScopeSelect.addEventListener('change', function() {
        const targetChurchesSelect = document.getElementById('target_churches');
        targetChurchesSelect.innerHTML = '<option value="">{{ __("Loading churches...") }}</option>';
        
        // In a real implementation, you would make an AJAX call here to fetch churches
        // For now, we'll just clear the options
        setTimeout(() => {
            targetChurchesSelect.innerHTML = '<option value="">{{ __("All churches at selected level") }}</option>';
        }, 500);
    });
});
</script>
@endpush
@endsection
