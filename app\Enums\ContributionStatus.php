<?php

namespace App\Enums;

enum ContributionStatus: string
{
    case ACTIVE = 'active';
    case COMPLETED = 'completed';
    case CANCELLED = 'cancelled';
    case EXPIRED = 'expired';

    public function getLabel(): string
    {
        return match ($this) {
            self::ACTIVE => 'Active',
            self::COMPLETED => 'Completed',
            self::CANCELLED => 'Cancelled',
            self::EXPIRED => 'Expired',
        };
    }

    public function getColor(): string
    {
        return match ($this) {
            self::ACTIVE => 'success',
            self::COMPLETED => 'primary',
            self::CANCELLED => 'danger',
            self::EXPIRED => 'warning',
        };
    }

    public function canReceiveContributions(): bool
    {
        return $this === self::ACTIVE;
    }
}
