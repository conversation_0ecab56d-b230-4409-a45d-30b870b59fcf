@extends('layouts.app')

@section('title', __('users.user_details'))
@section('page-title', __('users.user_details'))

@section('breadcrumbs')
    <li>
        <span class="mx-2">/</span>
        <a href="{{ route('users.index') }}" class="hover:text-gray-700">{{ __('users.users') }}</a>
    </li>
    <li>
        <span class="mx-2">/</span>
        <span class="font-medium text-gray-900">{{ $user->full_name }}</span>
    </li>
@endsection

@section('page-actions')
    <div class="flex items-center space-x-3">
        <!-- Language Switcher -->
        <x-language-switcher position="bottom-right" size="normal" />

        <a href="{{ route('users.edit', $user) }}"
           class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <i class="fas fa-edit mr-2"></i>
            {{ __('common.edit') }}
        </a>
        <a href="{{ route('users.index') }}"
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <i class="fas fa-arrow-left mr-2"></i>
            {{ __('common.back') }}
        </a>
    </div>
@endsection

@section('content')
    <div class="max-w-6xl mx-auto space-y-6">
        <!-- Quick Language Switcher -->
        <div class="flex justify-end">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-2">
                <div class="flex items-center space-x-2 text-sm">
                    <i class="fas fa-globe text-gray-400"></i>
                    <span class="text-gray-600">{{ __('common.language') }}:</span>
                    <div class="flex space-x-1">
                        @foreach(config('app.locale_names') as $locale => $info)
                            <button onclick="switchLanguage('{{ $locale }}')"
                                    class="px-2 py-1 rounded text-xs font-medium transition-colors duration-200 {{ app()->getLocale() === $locale ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100' }}">
                                {{ $info['flag'] }} {{ $info['native'] }}
                            </button>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
        <!-- Profile Header -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-8">
                <div class="flex items-center space-x-6">
                    <!-- Profile Picture -->
                    <div class="flex-shrink-0">
                        @if($user->profile_picture)
                            <img class="h-24 w-24 rounded-full border-4 border-white shadow-lg object-cover"
                                 src="{{ asset('storage/' . $user->profile_picture) }}"
                                 alt="{{ $user->full_name }}">
                        @else
                            <div class="h-24 w-24 rounded-full border-4 border-white shadow-lg bg-gray-300 flex items-center justify-center">
                                <span class="text-2xl font-bold text-gray-600">
                                    {{ strtoupper(substr($user->full_name, 0, 1)) }}
                                </span>
                            </div>
                        @endif
                    </div>

                    <!-- Profile Info -->
                    <div class="flex-1 min-w-0">
                        <h1 class="text-2xl font-bold text-white truncate">{{ $user->full_name }}</h1>
                        <p class="text-blue-100 text-lg">{{ $user->email }}</p>
                        <div class="flex items-center mt-2 space-x-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $user->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                <i class="fas fa-circle text-xs mr-2"></i>
                                {{ $user->is_active ? __('common.active') : __('common.inactive') }}
                            </span>
                            @if($user->is_first_login)
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-exclamation-triangle text-xs mr-2"></i>
                                    {{ __('users.first_login_pending') }}
                                </span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-church text-blue-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">{{ __('users.church') }}</p>
                        <p class="text-lg font-semibold text-gray-900">{{ $user->church->name ?? __('common.not_assigned') }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-user-tag text-green-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">{{ __('users.role') }}</p>
                        <p class="text-lg font-semibold text-gray-900">{{ $user->role }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-envelope text-purple-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">{{ __('common.unread_messages') }}</p>
                        <p class="text-lg font-semibold text-gray-900">{{ $user->unread_messages_count ?? 0 }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-clock text-orange-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">{{ __('common.pending_approvals') }}</p>
                        <p class="text-lg font-semibold text-gray-900">{{ $user->pending_approvals_count ?? 0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Information -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Personal Information -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-user mr-2 text-blue-600"></i>
                        {{ __('users.personal_information') }}
                    </h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-600">{{ __('users.full_name') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $user->full_name }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">{{ __('users.email') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $user->email }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">{{ __('users.phone_number') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $user->phone_number ?? __('common.not_provided') }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">{{ __('users.date_of_birth') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $user->date_of_birth ? $user->date_of_birth->format('M d, Y') : __('common.not_provided') }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">{{ __('users.gender') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $user->gender ?? __('common.not_provided') }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">{{ __('users.locale') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $user->locale === 'sw' ? 'Kiswahili' : 'English' }}</p>
                        </div>
                    </div>
                    @if($user->address)
                        <div>
                            <label class="block text-sm font-medium text-gray-600">{{ __('users.address') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $user->address }}</p>
                        </div>
                    @endif
                    @if($user->bio)
                        <div>
                            <label class="block text-sm font-medium text-gray-600">{{ __('users.bio') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $user->bio }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Church & Role Information -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-church mr-2 text-green-600"></i>
                        {{ __('users.church_role_information') }}
                    </h3>
                </div>
                <div class="p-6 space-y-4">
                    @if($user->church)
                        <div>
                            <label class="block text-sm font-medium text-gray-600">{{ __('users.church') }}</label>
                            <div class="mt-1 flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ $user->church->name }}</p>
                                    <p class="text-xs text-gray-500">{{ __('common.' . strtolower($user->church->level->value)) }} - {{ $user->church->location }}</p>
                                </div>
                                <a href="{{ route('churches.show', $user->church) }}"
                                   class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    {{ __('common.view') }} →
                                </a>
                            </div>
                        </div>
                    @endif

                    <div>
                        <label class="block text-sm font-medium text-gray-600">{{ __('users.role') }}</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $user->role }}</p>
                    </div>

                    @if($user->roles->count() > 0)
                        <div>
                            <label class="block text-sm font-medium text-gray-600">{{ __('users.permissions') }}</label>
                            <div class="mt-2 flex flex-wrap gap-2">
                                @foreach($user->roles as $role)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ $role->name }}
                                    </span>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <div class="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
                        <div>
                            <label class="block text-sm font-medium text-gray-600">{{ __('users.account_created') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $user->created_at->format('M d, Y') }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">{{ __('users.last_updated') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $user->updated_at->format('M d, Y') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Emergency Contact Information -->
        @if($user->emergency_contact_name || $user->emergency_contact_phone)
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-phone-alt mr-2 text-red-600"></i>
                        {{ __('users.emergency_contact') }}
                    </h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        @if($user->emergency_contact_name)
                            <div>
                                <label class="block text-sm font-medium text-gray-600">{{ __('users.emergency_contact_name') }}</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $user->emergency_contact_name }}</p>
                            </div>
                        @endif
                        @if($user->emergency_contact_phone)
                            <div>
                                <label class="block text-sm font-medium text-gray-600">{{ __('users.emergency_contact_phone') }}</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $user->emergency_contact_phone }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @endif
    </div>
@endsection