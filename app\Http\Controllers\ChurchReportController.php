<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Church;
use App\Models\User;
use App\Enums\ChurchLevel;
use App\Services\ChurchHierarchyService;
use Barryvdh\DomPDF\Facade\Pdf;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ChurchReportExport;

class ChurchReportController extends Controller
{
    protected ChurchHierarchyService $hierarchyService;

    public function __construct(ChurchHierarchyService $hierarchyService)
    {
        $this->hierarchyService = $hierarchyService;
        $this->middleware('auth');
        $this->middleware('permission:view-church-reports');
    }

    /**
     * Display the church reports index page
     */
    public function index()
    {
        $user = Auth::user();
        $accessibleChurches = $this->hierarchyService->getChurchesUserCanAccess($user);

        // Get available levels for reporting (only levels user can access)
        $availableLevels = [];
        foreach (ChurchLevel::cases() as $level) {
            $count = $accessibleChurches->where('level', $level)->count();
            if ($count > 0 && $this->canGenerateReportsForLevel($user, $level)) {
                $availableLevels[] = [
                    'level' => $level,
                    'name' => $level->value,
                    'count' => $count
                ];
            }
        }

        return view('churches.reports.index', compact('availableLevels'));
    }

    /**
     * Generate a report for a specific church level
     */
    public function generateLevelReport(Request $request)
    {
        $request->validate([
            'level' => 'required|string',
            'format' => 'required|in:view,pdf,excel',
            'include_demographics' => 'boolean',
            'include_leadership' => 'boolean',
            'include_statistics' => 'boolean',
        ]);

        $user = Auth::user();
        $level = ChurchLevel::tryFrom($request->level);

        if (!$level) {
            return redirect()->back()->with('error', 'Invalid church level selected.');
        }

        // Check if user can generate reports for this level
        if (!$this->canGenerateReportsForLevel($user, $level)) {
            return redirect()->back()->with('error', 'You do not have permission to generate reports for this level.');
        }

        $accessibleChurches = $this->hierarchyService->getChurchesUserCanAccess($user);
        $churches = $accessibleChurches->where('level', $level);

        if ($churches->isEmpty()) {
            return redirect()->back()->with('error', 'No churches found for the selected level.');
        }

        // Load additional data based on options
        $churches = $churches->load([
            'parentChurch',
            'childChurches',
            'users' => function ($query) {
                $query->where('is_active', true)->with('roles');
            }
        ]);

        if ($request->boolean('include_leadership')) {
            $churches->load(['leaders.user']);
        }

        // Load contact information for all churches
        $churches = $churches->map(function ($church) {
            // Get leaders from user roles for each church
            $church->role_based_leaders = $church->getLeadersFromRoles();
            return $church;
        });

        $reportData = [
            'level' => $level,
            'churches' => $churches,
            'generated_by' => $user,
            'generated_at' => now(),
            'options' => [
                'include_demographics' => $request->boolean('include_demographics'),
                'include_leadership' => $request->boolean('include_leadership'),
                'include_statistics' => $request->boolean('include_statistics'),
                'include_contact_info' => $request->boolean('include_contact_info', true), // Default to true
                'include_user_details' => $request->boolean('include_user_details'),
                'include_establishment_info' => $request->boolean('include_establishment_info'),
            ],
            'summary' => $this->generateSummaryData($churches, $request)
        ];

        switch ($request->format) {
            case 'pdf':
                return $this->generatePdfReport($reportData);
            case 'excel':
                return $this->generateExcelReport($reportData);
            default:
                return view('churches.reports.level-report', $reportData);
        }
    }

    /**
     * Generate summary data for the report
     */
    protected function generateSummaryData($churches, $request)
    {
        $summary = [
            'total_churches' => $churches->count(),
            'total_users' => $churches->sum(function ($church) {
                return $church->users->count();
            }),
            'active_users' => $churches->sum(function ($church) {
                return $church->users->where('is_active', true)->count();
            }),
        ];

        if ($request->boolean('include_demographics')) {
            // Calculate demographics based on actual user data
            $allUsers = collect();
            foreach ($churches as $church) {
                $allUsers = $allUsers->merge($church->users);
            }

            $summary['demographics'] = [
                'total_users' => $allUsers->count(),
                'active_users' => $allUsers->where('is_active', true)->count(),
                'inactive_users' => $allUsers->where('is_active', false)->count(),
                'users_with_roles' => $allUsers->filter(function($user) {
                    return $user->roles->count() > 0;
                })->count(),
            ];
        }

        if ($request->boolean('include_leadership')) {
            $summary['leadership'] = [
                'total_leaders' => $churches->sum(function ($church) {
                    return $church->role_based_leaders ? $church->role_based_leaders->count() : 0;
                }),
                'leadership_positions' => $churches->flatMap(function ($church) {
                    return $church->role_based_leaders ? $church->role_based_leaders->pluck('role') : collect();
                })->unique()->values()->toArray(),
            ];
        }

        if ($request->boolean('include_contact_info')) {
            $summary['contact_summary'] = [
                'churches_with_email' => $churches->whereNotNull('email')->count(),
                'churches_with_phone' => $churches->whereNotNull('phone_number')->count(),
                'churches_with_address' => $churches->whereNotNull('address')->count(),
                'total_contact_points' => $churches->sum(function ($church) {
                    $contacts = 0;
                    if ($church->email) $contacts++;
                    if ($church->phone_number) $contacts++;
                    if ($church->address) $contacts++;
                    return $contacts;
                }),
            ];
        }

        if ($request->boolean('include_establishment_info')) {
            $summary['establishment_info'] = [
                'churches_with_establishment_date' => $churches->whereNotNull('date_established')->count(),
                'oldest_church' => $churches->whereNotNull('date_established')->sortBy('date_established')->first(),
                'newest_church' => $churches->whereNotNull('date_established')->sortByDesc('date_established')->first(),
                'average_age_years' => round($churches->whereNotNull('date_established')->avg(function ($church) {
                    return $church->getAgeInYears();
                })),
            ];
        }

        if ($request->boolean('include_user_details')) {
            $summary['user_details'] = [
                'users_with_phone' => $churches->sum(function ($church) {
                    return $church->users->whereNotNull('phone_number')->count();
                }),
                'users_with_complete_profile' => $churches->sum(function ($church) {
                    return $church->users->where('is_first_login', false)->count();
                }),
                'role_distribution' => $churches->flatMap(function ($church) {
                    return $church->users->flatMap(function ($user) {
                        return $user->roles->pluck('name');
                    });
                })->countBy()->toArray(),
            ];
        }

        return $summary;
    }

    /**
     * Generate PDF report
     */
    protected function generatePdfReport($reportData)
    {
        $pdf = Pdf::loadView('churches.reports.pdf.level-report', $reportData);
        $filename = 'church-report-' . strtolower($reportData['level']->value) . '-' . now()->format('Y-m-d') . '.pdf';

        return $pdf->download($filename);
    }

    /**
     * Generate Excel report
     */
    protected function generateExcelReport($reportData)
    {
        $filename = 'church-report-' . strtolower($reportData['level']->value) . '-' . now()->format('Y-m-d') . '.xlsx';

        return Excel::download(new ChurchReportExport($reportData), $filename);
    }

    /**
     * Get church statistics for AJAX requests
     */
    public function getStatistics(Request $request)
    {
        $user = Auth::user();
        $level = $request->get('level');

        if ($level) {
            $levelEnum = ChurchLevel::tryFrom($level);
            if (!$levelEnum) {
                return response()->json(['error' => 'Invalid level'], 400);
            }

            $accessibleChurches = $this->hierarchyService->getChurchesUserCanAccess($user);
            $churches = $accessibleChurches->where('level', $levelEnum);
        } else {
            $churches = $this->hierarchyService->getChurchesUserCanAccess($user);
        }

        $statistics = [
            'total_churches' => $churches->count(),
            'total_users' => $churches->sum(function ($church) {
                return $church->users()->count();
            }),
            'user_statistics' => [
                'active_users' => $churches->sum(function ($church) {
                    return $church->users()->where('is_active', true)->count();
                }),
                'inactive_users' => $churches->sum(function ($church) {
                    return $church->users()->where('is_active', false)->count();
                }),
                'users_with_roles' => $churches->sum(function ($church) {
                    return $church->users()->whereHas('roles')->count();
                }),
            ],
            'by_level' => []
        ];

        // Group statistics by level
        foreach (ChurchLevel::cases() as $churchLevel) {
            $levelChurches = $churches->where('level', $churchLevel);
            if ($levelChurches->count() > 0) {
                $statistics['by_level'][$churchLevel->value] = [
                    'count' => $levelChurches->count(),
                    'users' => $levelChurches->sum(function ($church) {
                        return $church->users()->count();
                    }),
                ];
            }
        }

        return response()->json($statistics);
    }

    /**
     * Check if user can generate reports for a specific level
     */
    protected function canGenerateReportsForLevel($user, ChurchLevel $targetLevel): bool
    {
        // Get user's church level
        $userChurch = $user->church;
        if (!$userChurch) {
            return false;
        }

        $userLevel = $userChurch->level;
        $userRoles = $user->getRoleNames();

        // National IT is super admin - can access all levels
        if ($userRoles->contains('National IT')) {
            return true;
        }

        // IT roles can only generate reports for their level and below
        if ($userRoles->contains('Regional IT')) {
            return in_array($targetLevel, [
                ChurchLevel::REGIONAL,
                ChurchLevel::LOCAL,
                ChurchLevel::PARISH,
                ChurchLevel::BRANCH
            ]);
        }

        if ($userRoles->contains('Local IT')) {
            return in_array($targetLevel, [
                ChurchLevel::LOCAL,
                ChurchLevel::PARISH,
                ChurchLevel::BRANCH
            ]);
        }

        if ($userRoles->contains('Parish IT')) {
            return in_array($targetLevel, [
                ChurchLevel::PARISH,
                ChurchLevel::BRANCH
            ]);
        }

        if ($userRoles->contains('Branch IT')) {
            return $targetLevel === ChurchLevel::BRANCH;
        }

        // For non-IT roles, check if they can access the level through hierarchy
        return $userLevel->getLevel() <= $targetLevel->getLevel();
    }
}
