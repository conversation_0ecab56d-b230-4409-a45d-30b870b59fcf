# FPCT Church Management System (CMS) User Manual

## Table of Contents

1. [Getting Started](#getting-started)
2. [User Profile Management](#user-profile-management)
3. [Church Management](#church-management)
4. [Communication Features](#communication-features)
5. [Revenue Management](#revenue-management)
6. [Request System](#request-system)
7. [Notifications](#notifications)
8. [Troubleshooting](#troubleshooting)

## Getting Started

### First Login

1. **Access the System**
   - Open your web browser and navigate to the FPCT Church Management System (CMS) URL
   - Enter your email address and temporary password provided by your administrator

2. **Complete Your Profile**
   - On first login, you'll be prompted to complete your profile
   - Upload a profile picture (optional but recommended)
   - Verify your phone number for SMS notifications
   - Set your language preference (English/Swahili)

3. **Change Your Password**
   - For security, change your temporary password immediately
   - Use a strong password with at least 8 characters
   - Include uppercase, lowercase, numbers, and special characters

### Dashboard Overview

Your dashboard provides:
- **Quick Statistics**: Overview of your church's key metrics
- **Recent Activities**: Latest transactions, messages, and requests
- **Notifications**: Important alerts and updates
- **Quick Actions**: Shortcuts to common tasks

## User Profile Management

### Updating Your Profile

1. Click on your profile picture in the top navigation
2. Select "Profile Settings"
3. Update your information:
   - Personal details (name, phone, address)
   - Profile picture
   - Language preference
   - Notification settings

### Notification Preferences

Configure how you receive notifications:
- **Email Notifications**: System updates, transaction confirmations
- **SMS Notifications**: Important alerts, transaction confirmations
- **Push Notifications**: Real-time updates in the system

## Church Management

### Viewing Church Hierarchy

1. Navigate to **Churches** → **Hierarchy**
2. View the organizational structure from National to Branch level
3. Click on any church to view details and statistics

### Managing Your Church

If you have administrative permissions:

1. **Update Church Information**
   - Go to **Churches** → **My Church**
   - Edit church details, contact information, and settings

2. **View Church Statistics**
   - Member count and growth metrics
   - Financial summaries
   - Activity reports

### Adding New Churches

For users with appropriate permissions:

1. Navigate to **Churches** → **Create Church**
2. Select the church level (Regional, Local, Parish, Branch)
3. Choose the parent church
4. Fill in church details:
   - Name and location
   - Date established
   - Contact information
5. Assign church leaders

## Communication Features

### Internal Messaging

#### Sending Messages

1. Go to **Messages** → **Compose**
2. Select recipients:
   - Individual users
   - Church groups
   - All users at your level or below
3. Write your message
4. Choose message priority (Normal/High)
5. Send the message

#### Managing Messages

- **Inbox**: View received messages
- **Sent**: View messages you've sent
- **Announcements**: Official church announcements

### SMS Integration

The system automatically sends SMS notifications for:
- **Welcome messages** for new users
- **Transaction confirmations** with complete details
- **Important announcements**
- **Password reset codes**

SMS messages include:
- Transaction type and amount
- Source and destination information
- Purpose/description
- Reference numbers

## Revenue Management

### Understanding the Financial Hierarchy

Revenue flows upward through the church hierarchy:
- Branch → Parish → Local → Regional → National

### Creating Transactions

#### Step-by-Step Process

1. **Navigate to Revenue Section**
   - Go to **Revenue** → **New Transaction**

2. **Select Transaction Type**
   - **Revenue Collection**: Regular upward revenue flow
   - **Contribution**: Special fundraising campaigns
   - **Transfer**: General transfers between accounts

3. **Enter Transaction Details**
   - Amount (in Tanzanian Shillings)
   - Description/Purpose
   - Select contribution campaign (if applicable)

4. **Choose Payment Method**
   - **Mobile Money**: M-Pesa, Tigo Pesa, Airtel Money
   - **Bank Transfer**: Direct bank transfers
   - **Cash**: Manual cash transactions

5. **Provide Payment Details**
   - For mobile money: Phone number and provider
   - For bank transfer: Account details
   - For cash: Record keeping information

6. **Submit Transaction**
   - Review all details carefully
   - Submit for processing

### Transaction Notifications

After successful transaction completion, you'll receive:

1. **Real-time Notification** in the system
2. **Email Confirmation** with detailed transaction information
3. **SMS Notification** containing:
   - Transaction type and amount
   - Source church (where money came from)
   - Destination church (where money is going)
   - Purpose/description of the transaction
   - Reference number and transaction ID
   - Date and time of completion

### Viewing Financial Information

#### Transaction History
- Go to **Revenue** → **Transactions**
- Filter by date, type, or status
- Export reports for record keeping

#### Financial Dashboard
- View current church balance
- See incoming and outgoing revenue
- Monitor contribution progress
- Generate financial reports

#### Receipts
- All transactions automatically generate receipts
- Download receipts from transaction details
- Print receipts for record keeping

### Payment Methods Details

#### Mobile Money (via AzamPay)
- Supports all major Tanzanian mobile money providers
- Real-time processing and confirmation
- Automatic SMS confirmations

#### Bank Transfers
- Direct bank-to-bank transfers
- Secure processing through AzamPay
- Bank confirmation and tracking

#### Cash Transactions
- Manual recording in the system
- Immediate completion
- Requires proper documentation

## Request System

### Submitting Requests

1. **Navigate to Requests**
   - Go to **Requests** → **New Request**

2. **Select Request Type**
   - Church upgrade requests
   - Administrative requests
   - Financial requests
   - Other church-related requests

3. **Provide Details**
   - Clear description of the request
   - Supporting documentation
   - Justification and reasoning

4. **Submit for Approval**
   - Requests are automatically routed to appropriate approvers
   - Track progress through the approval workflow

### Tracking Requests

- View all your submitted requests
- Monitor approval status
- Receive notifications on status changes
- Communicate with approvers through the system

## Notifications

### Types of Notifications

1. **System Notifications**
   - Account activation/deactivation
   - Password resets
   - System maintenance alerts

2. **Transaction Notifications**
   - Payment confirmations
   - Receipt generation
   - Balance updates

3. **Communication Notifications**
   - New messages
   - Announcements
   - Request updates

### Managing Notifications

- Configure notification preferences in your profile
- Choose delivery methods (email, SMS, push)
- Set notification frequency and types

## Troubleshooting

### Common Issues

#### Login Problems
- **Forgot Password**: Use the "Forgot Password" link
- **Account Locked**: Contact your church administrator
- **Invalid Credentials**: Verify email and password

#### Transaction Issues
- **Payment Failed**: Check payment details and try again
- **Transaction Pending**: Wait for processing or contact support
- **Missing Receipt**: Check transaction history or regenerate

#### SMS Not Received
- Verify phone number in your profile
- Check SMS balance with administrator
- Ensure phone has network coverage

#### Notification Problems
- Check notification preferences
- Verify email address and phone number
- Clear browser cache and cookies

### Getting Help

#### Self-Service Options
1. Check this user manual
2. Review system help tooltips
3. Check FAQ section in the system

#### Contact Support
1. **Church Administrator**: First point of contact
2. **Regional Support**: For technical issues
3. **System Support**: For critical problems

#### Reporting Issues
When reporting issues, include:
- Your user ID and church affiliation
- Description of the problem
- Steps to reproduce the issue
- Screenshots if applicable
- Error messages received

### Best Practices

#### Security
- Never share your login credentials
- Log out when using shared computers
- Report suspicious activities immediately
- Keep your contact information updated

#### Financial Transactions
- Double-check all transaction details before submitting
- Keep records of all financial activities
- Report discrepancies immediately
- Maintain proper documentation

#### Communication
- Use clear and professional language
- Respect church hierarchy in communications
- Keep messages relevant and concise
- Follow church communication policies

---

**For additional support, contact your church administrator or the FPCT Church Management System (CMS) support team.**
