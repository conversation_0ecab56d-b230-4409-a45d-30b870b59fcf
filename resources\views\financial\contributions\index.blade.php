@extends('layouts.app')

@section('title', __('Contributions'))

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ __('Contribution Campaigns') }}</h1>
                    <p class="mt-2 text-gray-600">{{ __('Manage fundraising campaigns and track contributions') }}</p>
                </div>
                <div class="flex space-x-3">
                    @can('create-contributions')
                        <a href="{{ route('contributions.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-plus mr-2"></i>{{ __('Create Contribution') }}
                        </a>
                    @endcan
                    <a href="{{ route('financial.dashboard') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-chart-line mr-2"></i>{{ __('Dashboard') }}
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Contributions -->
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-white bg-opacity-30 rounded-md flex items-center justify-center">
                                <i class="fas fa-hand-holding-usd text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-blue-100 truncate uppercase tracking-wide">{{ __('Total Contributions') }}</dt>
                                <dd class="text-lg font-medium text-white">{{ $contributions->total() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Campaigns -->
            <div class="bg-gradient-to-r from-green-500 to-green-600 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-white bg-opacity-30 rounded-md flex items-center justify-center">
                                <i class="fas fa-play-circle text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-green-100 truncate uppercase tracking-wide">{{ __('Active Campaigns') }}</dt>
                                <dd class="text-lg font-medium text-white">{{ $contributions->where('status', 'active')->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Target -->
            <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-white bg-opacity-30 rounded-md flex items-center justify-center">
                                <i class="fas fa-bullseye text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-indigo-100 truncate uppercase tracking-wide">{{ __('Total Target') }}</dt>
                                <dd class="text-lg font-medium text-white">{{ number_format($contributions->sum('target_amount'), 0) }} TZS</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Collected -->
            <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-white bg-opacity-30 rounded-md flex items-center justify-center">
                                <i class="fas fa-coins text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-yellow-100 truncate uppercase tracking-wide">{{ __('Total Collected') }}</dt>
                                <dd class="text-lg font-medium text-white">{{ number_format($contributions->sum('collected_amount'), 0) }} TZS</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Card -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-filter mr-2 text-blue-600"></i>
                    {{ __('Filters') }}
                </h3>
            </div>
            <div class="p-6">
                <form method="GET" action="{{ route('contributions.index') }}" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">{{ __('Status') }}</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="status" name="status">
                                <option value="">{{ __('All Statuses') }}</option>
                                <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>{{ __('Active') }}</option>
                                <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>{{ __('Completed') }}</option>
                                <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>{{ __('Cancelled') }}</option>
                                <option value="expired" {{ request('status') == 'expired' ? 'selected' : '' }}>{{ __('Expired') }}</option>
                            </select>
                        </div>

                        <div>
                            <label for="type" class="block text-sm font-medium text-gray-700 mb-2">{{ __('Type') }}</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="type" name="type">
                                <option value="">{{ __('All Types') }}</option>
                                <option value="general" {{ request('type') == 'general' ? 'selected' : '' }}>{{ __('General') }}</option>
                                <option value="special" {{ request('type') == 'special' ? 'selected' : '' }}>{{ __('Special') }}</option>
                                <option value="emergency" {{ request('type') == 'emergency' ? 'selected' : '' }}>{{ __('Emergency') }}</option>
                                <option value="project" {{ request('type') == 'project' ? 'selected' : '' }}>{{ __('Project') }}</option>
                            </select>
                        </div>

                        <div class="md:col-span-2">
                            <label for="search" class="block text-sm font-medium text-gray-700 mb-2">{{ __('Search') }}</label>
                            <div class="flex">
                                <input type="text" class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="search" name="search" value="{{ request('search') }}" placeholder="{{ __('Contribution name or description...') }}">
                                <button type="submit" class="px-4 py-2 bg-blue-600 border border-blue-600 rounded-r-md text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Contributions Grid -->
        @if($contributions->count() > 0)
            <div class="grid gap-6
                {{ $contributions->count() == 1 ? 'grid-cols-1' : '' }}
                {{ $contributions->count() == 2 ? 'grid-cols-1 md:grid-cols-2' : '' }}
                {{ $contributions->count() >= 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : '' }}">
                @foreach($contributions as $contribution)
                    <div class="bg-white shadow-sm rounded-lg border border-gray-200 hover:shadow-lg transition-shadow duration-200 flex flex-col
                        {{ $contributions->count() == 1 ? 'max-w-4xl mx-auto' : '' }}">
                        <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900 truncate">{{ $contribution->name }}</h3>
                            <div class="relative" x-data="{ open: false }">
                                <button @click="open = !open" class="inline-flex items-center p-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                                    <div class="py-1">
                                        <a href="{{ route('contributions.show', $contribution) }}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-eye mr-2"></i>{{ __('View Details') }}
                                        </a>
                                        @can('edit-contributions')
                                            @if($contribution->created_by_church_id === $church->id)
                                                <a href="{{ route('contributions.edit', $contribution) }}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i class="fas fa-edit mr-2"></i>{{ __('Edit') }}
                                                </a>
                                            @endif
                                        @endcan
                                        @can('manage-contributions')
                                            @if($contribution->created_by_church_id === $church->id)
                                                <div class="border-t border-gray-100"></div>
                                                <a href="#" onclick="deleteContribution({{ $contribution->id }})" class="flex items-center px-4 py-2 text-sm text-red-700 hover:bg-red-50">
                                                    <i class="fas fa-trash mr-2"></i>{{ __('Delete') }}
                                                </a>
                                            @endif
                                        @endcan
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="px-6 py-4 flex-1">
                            @if($contributions->count() == 1)
                                <!-- Single card horizontal layout -->
                                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                                    <!-- Left column: Basic info -->
                                    <div class="lg:col-span-2">
                                        <div class="flex flex-wrap gap-2 mb-4">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-{{ $contribution->status->getColor() }}-100 text-{{ $contribution->status->getColor() }}-800">
                                                {{ $contribution->status->getLabel() }}
                                            </span>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                {{ ucfirst($contribution->type) }}
                                            </span>
                                            @if($contribution->is_mandatory)
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    {{ __('Mandatory') }}
                                                </span>
                                            @endif
                                        </div>

                                        @if($contribution->description)
                                            <p class="text-gray-600 text-sm mb-4">{{ $contribution->description }}</p>
                                        @endif

                                        @if($contribution->target_amount)
                                            <div class="mb-4">
                                                <div class="flex justify-between items-center mb-2">
                                                    <span class="text-sm font-medium text-gray-700">{{ __('Progress') }}</span>
                                                    <span class="text-lg font-bold text-green-600">{{ number_format($contribution->getProgressPercentage(), 1) }}%</span>
                                                </div>
                                                <div class="w-full bg-gray-200 rounded-full h-3 mb-3">
                                                    <div class="bg-gradient-to-r from-green-400 to-green-600 h-3 rounded-full transition-all duration-300"
                                                         style="width: {{ $contribution->getProgressPercentage() }}%">
                                                    </div>
                                                </div>
                                                <div class="flex justify-between text-sm text-gray-600">
                                                    <span>{{ __('Collected') }}: <span class="font-semibold text-green-600">{{ number_format($contribution->collected_amount, 0) }} TZS</span></span>
                                                    <span>{{ __('Target') }}: <span class="font-semibold text-blue-600">{{ number_format($contribution->target_amount, 0) }} TZS</span></span>
                                                </div>
                                            </div>
                                        @else
                                            <div class="mb-4">
                                                <div class="text-3xl font-bold text-green-600">{{ number_format($contribution->collected_amount, 0) }} TZS</div>
                                                <div class="text-sm text-gray-600">{{ __('Total Collected') }}</div>
                                            </div>
                                        @endif
                                    </div>

                                    <!-- Right column: Stats and dates -->
                                    <div class="lg:col-span-1">
                                        <div class="space-y-4">
                                            <div class="bg-blue-50 p-4 rounded-lg border border-blue-200 text-center">
                                                <div class="text-2xl font-bold text-blue-600">{{ $contribution->start_date->format('M j') }}</div>
                                                <div class="text-sm text-gray-600">{{ __('Start Date') }}</div>
                                            </div>
                                            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 text-center">
                                                <div class="text-2xl font-bold text-gray-900">
                                                    @if($contribution->end_date)
                                                        {{ $contribution->end_date->format('M j') }}
                                                    @else
                                                        {{ __('Ongoing') }}
                                                    @endif
                                                </div>
                                                <div class="text-sm text-gray-600">{{ __('End Date') }}</div>
                                            </div>
                                            @if($contribution->target_amount)
                                                <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200 text-center">
                                                    <div class="text-2xl font-bold text-yellow-600">{{ number_format($contribution->getRemainingAmount(), 0) }}</div>
                                                    <div class="text-sm text-gray-600">{{ __('Remaining') }} TZS</div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @else
                                <!-- Multiple cards vertical layout -->
                                <div class="flex flex-wrap gap-2 mb-4">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-{{ $contribution->status->getColor() }}-100 text-{{ $contribution->status->getColor() }}-800">
                                        {{ $contribution->status->getLabel() }}
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        {{ ucfirst($contribution->type) }}
                                    </span>
                                    @if($contribution->is_mandatory)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            {{ __('Mandatory') }}
                                        </span>
                                    @endif
                                </div>

                                @if($contribution->description)
                                    <p class="text-gray-600 text-sm mb-4">{{ Str::limit($contribution->description, 100) }}</p>
                                @endif

                                @if($contribution->target_amount)
                                    <div class="mb-4">
                                        <div class="flex justify-between items-center mb-2">
                                            <span class="text-sm font-medium text-gray-700">{{ __('Progress') }}</span>
                                            <span class="text-sm font-bold text-green-600">{{ number_format($contribution->getProgressPercentage(), 1) }}%</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
                                            <div class="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full transition-all duration-300"
                                                 style="width: {{ $contribution->getProgressPercentage() }}%">
                                            </div>
                                        </div>
                                        <div class="flex justify-between text-xs text-gray-600">
                                            <span>{{ __('Collected') }}: {{ number_format($contribution->collected_amount, 0) }} TZS</span>
                                            <span>{{ __('Target') }}: {{ number_format($contribution->target_amount, 0) }} TZS</span>
                                        </div>
                                    </div>
                                @else
                                    <div class="mb-4">
                                        <div class="text-2xl font-bold text-green-600">{{ number_format($contribution->collected_amount, 0) }} TZS</div>
                                        <div class="text-sm text-gray-600">{{ __('Total Collected') }}</div>
                                    </div>
                                @endif

                                <div class="grid grid-cols-2 gap-4 text-center">
                                    <div class="bg-gray-50 p-3 rounded-lg border-r border-gray-200">
                                        <div class="font-semibold text-gray-900">{{ $contribution->start_date->format('M j') }}</div>
                                        <div class="text-xs text-gray-600">{{ __('Start Date') }}</div>
                                    </div>
                                    <div class="bg-gray-50 p-3 rounded-lg">
                                        <div class="font-semibold text-gray-900">
                                            @if($contribution->end_date)
                                                {{ $contribution->end_date->format('M j') }}
                                            @else
                                                {{ __('No End Date') }}
                                            @endif
                                        </div>
                                        <div class="text-xs text-gray-600">{{ __('End Date') }}</div>
                                    </div>
                                </div>
                            @endif
                        </div>
                        <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                            <div class="flex justify-between items-center">
                                <div class="text-sm text-gray-600">
                                    {{ __('By') }}: {{ $contribution->createdByChurch->name }}
                                </div>
                                <a href="{{ route('contributions.show', $contribution) }}" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                                    {{ __('View Details') }}
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="flex justify-between items-center mt-6">
                <div class="text-sm text-gray-700">
                    {{ __('Showing') }} {{ $contributions->firstItem() }} {{ __('to') }} {{ $contributions->lastItem() }} {{ __('of') }} {{ $contributions->total() }} {{ __('results') }}
                </div>
                <div>
                    {{ $contributions->appends(request()->query())->links() }}
                </div>
            </div>
        @else
            <div class="bg-white shadow-sm rounded-lg border border-gray-200">
                <div class="text-center py-16">
                    <div class="mx-auto h-24 w-24 text-gray-300 mb-4">
                        <i class="fas fa-hand-holding-usd text-6xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">{{ __('No contributions found') }}</h3>
                    <p class="text-gray-500 mb-6">{{ __('No contribution campaigns match your current filters.') }}</p>
                    @can('create-contributions')
                        <a href="{{ route('contributions.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i>{{ __('Create First Contribution') }}
                        </a>
                    @endcan
                </div>
            </div>
        @endif
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="delete-modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onclick="closeDeleteModal()"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-trash text-red-600"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900" id="delete-modal-title">{{ __('Delete Contribution') }}</h3>
                        <div class="mt-4">
                            <p class="text-sm text-gray-600 mb-4">{{ __('Are you sure you want to delete this contribution? This action cannot be undone.') }}</p>
                            <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-yellow-700">{{ __('Note: You can only delete contributions that have no associated transactions.') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <form id="deleteForm" method="POST" class="inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                        {{ __('Delete') }}
                    </button>
                </form>
                <button type="button" onclick="closeDeleteModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    {{ __('Cancel') }}
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function deleteContribution(contributionId) {
    const form = document.getElementById('deleteForm');
    form.action = `/contributions/${contributionId}`;
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}

// Close modal when pressing Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeDeleteModal();
    }
});
</script>
@endpush
@endsection
