@extends('layouts.app')

@section('title', 'Create Permission')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-3xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Create New Permission</h1>
                    <p class="mt-2 text-sm text-gray-600">Define a new permission for the system</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="{{ route('permissions.index') }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Permissions
                    </a>
                </div>
            </div>
        </div>

        <form method="POST" action="{{ route('permissions.store') }}" class="space-y-8">
            @csrf
            
            <!-- Permission Information -->
            <div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-key mr-3 text-green-600"></i>
                        Permission Information
                    </h3>
                    <p class="mt-1 text-sm text-gray-600">Basic details about the permission</p>
                </div>
                
                <div class="p-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            Permission Name <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="text" 
                                   id="name"
                                   name="name" 
                                   value="{{ old('name') }}" 
                                   required
                                   class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('name') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror"
                                   placeholder="e.g., manage-reports, view-analytics">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i class="fas fa-tag text-gray-400"></i>
                            </div>
                        </div>
                        @error('name') 
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <i class="fas fa-exclamation-circle mr-1"></i>
                                {{ $message }}
                            </p>
                        @enderror
                        <div class="mt-2 text-sm text-gray-600">
                            <p class="mb-2">Use a descriptive name that follows the pattern: <code class="bg-gray-100 px-1 rounded">action-resource</code></p>
                            <div class="grid grid-cols-2 gap-4 text-xs">
                                <div>
                                    <strong>Examples:</strong>
                                    <ul class="list-disc list-inside mt-1 space-y-1">
                                        <li>create-reports</li>
                                        <li>view-analytics</li>
                                        <li>manage-settings</li>
                                    </ul>
                                </div>
                                <div>
                                    <strong>Common Actions:</strong>
                                    <ul class="list-disc list-inside mt-1 space-y-1">
                                        <li>create, edit, delete, view</li>
                                        <li>manage, approve, reject</li>
                                        <li>send, receive, broadcast</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Role Assignment -->
            <div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden" x-data="{ selectAll: false, selectedRoles: [] }">
                <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                <i class="fas fa-user-shield mr-3 text-blue-600"></i>
                                Assign to Roles
                            </h3>
                            <p class="mt-1 text-sm text-gray-600">Select which roles should have this permission</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <label class="inline-flex items-center">
                                <input type="checkbox" 
                                       x-model="selectAll"
                                       @change="selectAll ? selectedRoles = {{ $roles->pluck('name')->toJson() }} : selectedRoles = []"
                                       class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">Select All</span>
                            </label>
                            <span class="text-sm text-gray-500" x-text="`${selectedRoles.length} selected`"></span>
                        </div>
                    </div>
                </div>
                
                <div class="p-6">
                    @if($roles->count() > 0)
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            @foreach($roles as $role)
                                <label class="inline-flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors duration-200"
                                       :class="selectedRoles.includes('{{ $role->name }}') ? 'border-green-300 bg-green-50' : ''">
                                    <input type="checkbox" 
                                           name="roles[]" 
                                           value="{{ $role->name }}"
                                           x-model="selectedRoles"
                                           {{ in_array($role->name, old('roles', [])) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50">
                                    <div class="ml-3">
                                        <span class="text-sm font-medium text-gray-700">{{ $role->name }}</span>
                                        <p class="text-xs text-gray-500">{{ $role->users_count ?? 0 }} users</p>
                                    </div>
                                </label>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <i class="fas fa-user-shield text-4xl text-gray-400 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No roles available</h3>
                            <p class="text-gray-600 mb-4">Create roles first before assigning permissions.</p>
                            <a href="{{ route('roles.create') }}" 
                               class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <i class="fas fa-plus mr-2"></i>
                                Create Role
                            </a>
                        </div>
                    @endif
                    
                    @error('roles') 
                        <p class="mt-4 text-sm text-red-600 flex items-center">
                            <i class="fas fa-exclamation-circle mr-1"></i>
                            {{ $message }}
                        </p>
                    @enderror
                    
                    <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle text-blue-400"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-blue-800">Permission Assignment</h3>
                                <div class="mt-2 text-sm text-blue-700">
                                    <p>You can assign this permission to multiple roles. Users with these roles will automatically receive this permission.</p>
                                    <p class="mt-1">You can also modify role assignments later from the permission details page.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-between pt-6">
                <div class="flex items-center text-sm text-gray-500">
                    <i class="fas fa-info-circle mr-2"></i>
                    Fields marked with <span class="text-red-500 font-medium">*</span> are required
                </div>
                
                <div class="flex items-center space-x-4">
                    <a href="{{ route('permissions.index') }}" 
                       class="inline-flex items-center px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                        <i class="fas fa-times mr-2"></i>
                        Cancel
                    </a>
                    <button type="submit" 
                            class="inline-flex items-center px-8 py-3 border border-transparent rounded-lg text-sm font-medium text-white bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5">
                        <i class="fas fa-save mr-2"></i>
                        Create Permission
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection
