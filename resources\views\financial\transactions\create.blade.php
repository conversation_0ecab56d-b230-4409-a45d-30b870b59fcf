@extends('layouts.app')

@section('title', __('Send Revenue'))

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ __('Send Revenue') }}</h1>
                    <p class="mt-2 text-gray-600">{{ __('Send revenue from') }} {{ $church->name }} {{ __('to') }} {{ $targetChurch->name }}</p>
                </div>
                <a href="{{ route('financial.dashboard') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-arrow-left mr-2"></i>{{ __('Back to Dashboard') }}
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-2">
                <!-- Revenue Collection Form -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">{{ __('Revenue Collection Details') }}</h3>
                    </div>
                    <div class="px-6 py-6">
                        <form method="POST" action="{{ route('transactions.store') }}" id="revenueForm" class="space-y-6">
                            @csrf

                            <!-- Amount -->
                            <div>
                                <label for="amount" class="block text-sm font-medium text-gray-700">{{ __('Amount (TZS)') }} <span class="text-red-500">*</span></label>
                                <div class="mt-1 relative rounded-md shadow-sm">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">TZS</span>
                                    </div>
                                    <input type="number"
                                           class="block w-full pl-12 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('amount') border-red-300 @enderror"
                                           id="amount"
                                           name="amount"
                                           value="{{ old('amount') }}"
                                           min="1"
                                           step="0.01"
                                           required>
                                    @error('amount')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <!-- Contribution (Optional) -->
                            <div>
                                <label for="contribution_id" class="block text-sm font-medium text-gray-700">{{ __('Contribution (Optional)') }}</label>
                                <select class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('contribution_id') border-red-300 @enderror"
                                        id="contribution_id"
                                        name="contribution_id">
                                    <option value="">{{ __('Select a contribution (optional)') }}</option>
                                    @foreach($contributions as $contribution)
                                        <option value="{{ $contribution->id }}"
                                                {{ old('contribution_id') == $contribution->id ? 'selected' : '' }}>
                                            {{ $contribution->name }}
                                            @if($contribution->target_amount)
                                                ({{ number_format($contribution->getProgressPercentage(), 1) }}% complete)
                                            @endif
                                        </option>
                                    @endforeach
                                </select>
                                @error('contribution_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Payment Method -->
                            <div>
                                <label for="payment_method" class="block text-sm font-medium text-gray-700">{{ __('Payment Method') }} <span class="text-red-500">*</span></label>
                                <select class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @error('payment_method') border-red-300 @enderror"
                                        id="payment_method"
                                        name="payment_method"
                                        required>
                                    <option value="">{{ __('Select payment method') }}</option>
                                    @if(count($paymentMethods) > 0)
                                        @foreach($paymentMethods as $method)
                                            <option value="{{ $method['type'] }}"
                                                    {{ old('payment_method') == $method['type'] ? 'selected' : '' }}>
                                                {{ $method['label'] }}
                                            </option>
                                        @endforeach
                                    @endif
                                    <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>
                                        {{ __('Cash') }}
                                    </option>
                                </select>
                                @error('payment_method')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                        <!-- Payment Details (Dynamic based on method) -->
                        <div id="payment-details">
                            <!-- Bank Transfer Details -->
                            <div id="bank-details" class="payment-method-details" style="display: none;">
                                <h6 class="text-primary mb-3">{{ __('Bank Transfer Details') }}</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="bank_name">{{ __('Bank Name') }}</label>
                                            <input type="text" class="form-control" id="bank_name" name="payment_details[bank_name]" value="{{ old('payment_details.bank_name') }}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="account_number">{{ __('Account Number') }}</label>
                                            <input type="text" class="form-control" id="account_number" name="payment_details[account_number]" value="{{ old('payment_details.account_number') }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="account_name">{{ __('Account Name') }}</label>
                                    <input type="text" class="form-control" id="account_name" name="payment_details[account_name]" value="{{ old('payment_details.account_name') }}">
                                </div>
                            </div>

                            <!-- Mobile Money Details -->
                            <div id="mobile-details" class="payment-method-details" style="display: none;">
                                <h6 class="text-primary mb-3">{{ __('Mobile Money Details') }}</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="mobile_provider">{{ __('Provider') }}</label>
                                            <select class="form-control" id="mobile_provider" name="payment_details[provider]">
                                                <option value="">{{ __('Select provider') }}</option>
                                                <option value="Vodacom M-Pesa" {{ old('payment_details.provider') == 'Vodacom M-Pesa' ? 'selected' : '' }}>Vodacom M-Pesa</option>
                                                <option value="Airtel Money" {{ old('payment_details.provider') == 'Airtel Money' ? 'selected' : '' }}>Airtel Money</option>
                                                <option value="Tigo Pesa" {{ old('payment_details.provider') == 'Tigo Pesa' ? 'selected' : '' }}>Tigo Pesa</option>
                                                <option value="Halotel" {{ old('payment_details.provider') == 'Halotel' ? 'selected' : '' }}>Halotel</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="phone_number">{{ __('Phone Number') }}</label>
                                            <input type="text" class="form-control" id="phone_number" name="payment_details[phone_number]" value="{{ old('payment_details.phone_number') }}" placeholder="255XXXXXXXXX">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Cash Details -->
                            <div id="cash-details" class="payment-method-details" style="display: none;">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    {{ __('Cash transactions will be recorded immediately and marked as completed.') }}
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="form-group">
                            <label for="description" class="form-label">{{ __('Description (Optional)') }}</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" 
                                      name="description" 
                                      rows="3" 
                                      placeholder="{{ __('Enter transaction description...') }}">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                            <!-- Submit Button -->
                            <div class="flex justify-end space-x-3">
                                <a href="{{ route('financial.dashboard') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    {{ __('Cancel') }}
                                </a>
                                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-paper-plane mr-2"></i>{{ __('Send Revenue') }}
                                </button>
                            </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Target Church Info -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{ __('Target Church') }}</h6>
                </div>
                <div class="card-body">
                    <h5 class="card-title">{{ $targetChurch->name }}</h5>
                    <p class="card-text">
                        <strong>{{ __('Level') }}:</strong> {{ $targetChurch->level->value }}<br>
                        <strong>{{ __('Location') }}:</strong> {{ $targetChurch->location }}
                    </p>
                    
                    @if($targetChurch->hasCompleteFinancialInfo())
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            {{ __('Financial information complete') }}
                        </div>
                    @else
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            {{ __('Incomplete financial information') }}
                        </div>
                    @endif

                    <!-- Available Payment Methods -->
                    @if(count($paymentMethods) > 0)
                        <h6 class="mt-3">{{ __('Available Payment Methods') }}:</h6>
                        <ul class="list-unstyled">
                            @foreach($paymentMethods as $method)
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>{{ $method['label'] }}</strong>
                                    @if($method['type'] === 'bank_transfer')
                                        <br><small class="text-muted">{{ $method['details']['bank_name'] }} - {{ $method['details']['account_number'] }}</small>
                                    @elseif($method['type'] === 'mobile_money')
                                        <br><small class="text-muted">{{ $method['details']['provider'] }} - {{ $method['details']['number'] }}</small>
                                    @endif
                                </li>
                            @endforeach
                        </ul>
                    @endif
                </div>
            </div>

            <!-- Current Balance -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{ __('Your Current Balance') }}</h6>
                </div>
                <div class="card-body">
                    <h4 class="text-success">{{ number_format($church->getFinancialBalance()->available_balance, 2) }} TZS</h4>
                    <p class="text-muted mb-0">{{ __('Available for transactions') }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethodSelect = document.getElementById('payment_method');
    const paymentDetails = document.querySelectorAll('.payment-method-details');

    function showPaymentDetails() {
        // Hide all payment details
        paymentDetails.forEach(detail => {
            detail.style.display = 'none';
        });

        // Show relevant payment details
        const selectedMethod = paymentMethodSelect.value;
        if (selectedMethod === 'bank_transfer') {
            document.getElementById('bank-details').style.display = 'block';
        } else if (selectedMethod === 'mobile_money') {
            document.getElementById('mobile-details').style.display = 'block';
        } else if (selectedMethod === 'cash') {
            document.getElementById('cash-details').style.display = 'block';
        }
    }

    paymentMethodSelect.addEventListener('change', showPaymentDetails);
    
    // Show initial payment details if method is pre-selected
    showPaymentDetails();
});
</script>
@endpush
@endsection
