@extends('layouts.app')

@section('title', 'Session Timeout Test')

@section('content')
    <div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                <!-- Header -->
                <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
                    <h1 class="text-2xl font-semibold text-white flex items-center">
                        <i class="fas fa-clock mr-3"></i>
                        Session Timeout Test
                    </h1>
                </div>

                <!-- Content -->
                <div class="p-6">
                    <div class="mb-6">
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <i class="fas fa-info-circle text-yellow-600 mr-3"></i>
                                <div>
                                    <h3 class="text-sm font-medium text-yellow-800">Test Information</h3>
                                    <p class="text-sm text-yellow-700 mt-1">
                                        This page is for testing the session timeout functionality. 
                                        Your session will expire after 5 minutes of inactivity.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Session Status -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="text-lg font-medium text-gray-900 mb-3">Session Status</h3>
                            <div id="session-status" class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Status:</span>
                                    <span id="auth-status" class="text-sm font-medium text-green-600">Active</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Time Remaining:</span>
                                    <span id="time-remaining" class="text-sm font-medium text-blue-600">5:00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Last Check:</span>
                                    <span id="last-check" class="text-sm font-medium text-gray-600">--</span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="text-lg font-medium text-gray-900 mb-3">Actions</h3>
                            <div class="space-y-3">
                                <button id="check-session" 
                                        class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                    <i class="fas fa-sync mr-2"></i>
                                    Check Session
                                </button>
                                <button id="extend-session" 
                                        class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                    <i class="fas fa-clock mr-2"></i>
                                    Extend Session
                                </button>
                                <button id="simulate-inactivity" 
                                        class="w-full px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
                                    <i class="fas fa-pause mr-2"></i>
                                    Simulate Inactivity
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Instructions -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h3 class="text-lg font-medium text-blue-900 mb-3">How to Test</h3>
                        <ol class="list-decimal list-inside space-y-2 text-sm text-blue-800">
                            <li>Stay on this page without any activity for 4 minutes</li>
                            <li>You should see a warning modal when 1 minute remains</li>
                            <li>If you don't extend the session, you'll be automatically logged out</li>
                            <li>Try using the back button after logout - it should redirect to login</li>
                            <li>Use "Simulate Inactivity" to speed up the test</li>
                        </ol>
                    </div>

                    <!-- Activity Log -->
                    <div class="mt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-3">Activity Log</h3>
                        <div id="activity-log" class="bg-gray-50 rounded-lg p-4 h-32 overflow-y-auto">
                            <div class="text-sm text-gray-600">Session monitoring started...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const statusElement = document.getElementById('auth-status');
            const timeElement = document.getElementById('time-remaining');
            const lastCheckElement = document.getElementById('last-check');
            const logElement = document.getElementById('activity-log');

            function addLog(message) {
                const time = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.className = 'text-sm text-gray-600 mb-1';
                logEntry.textContent = `[${time}] ${message}`;
                logElement.appendChild(logEntry);
                logElement.scrollTop = logElement.scrollHeight;
            }

            function formatTime(seconds) {
                const minutes = Math.floor(seconds / 60);
                const secs = seconds % 60;
                return `${minutes}:${secs.toString().padStart(2, '0')}`;
            }

            async function checkSession() {
                try {
                    const response = await fetch('/session/check');
                    const data = await response.json();
                    
                    lastCheckElement.textContent = new Date().toLocaleTimeString();
                    
                    if (data.authenticated) {
                        statusElement.textContent = 'Active';
                        statusElement.className = 'text-sm font-medium text-green-600';
                        timeElement.textContent = formatTime(data.remaining_time);
                        
                        if (data.timeout_warning) {
                            addLog('⚠️ Session warning: ' + data.warning_message);
                        } else {
                            addLog('✅ Session active, ' + formatTime(data.remaining_time) + ' remaining');
                        }
                    } else {
                        statusElement.textContent = 'Expired';
                        statusElement.className = 'text-sm font-medium text-red-600';
                        timeElement.textContent = '0:00';
                        addLog('❌ Session expired');
                    }
                } catch (error) {
                    addLog('❌ Error checking session: ' + error.message);
                }
            }

            async function extendSession() {
                try {
                    const response = await fetch('/session/extend', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        addLog('✅ Session extended successfully');
                        checkSession();
                    } else {
                        addLog('❌ Failed to extend session');
                    }
                } catch (error) {
                    addLog('❌ Error extending session: ' + error.message);
                }
            }

            // Event listeners
            document.getElementById('check-session').addEventListener('click', checkSession);
            document.getElementById('extend-session').addEventListener('click', extendSession);
            
            document.getElementById('simulate-inactivity').addEventListener('click', function() {
                // Temporarily disable the session manager to simulate inactivity
                if (window.sessionManager) {
                    window.sessionManager.destroy();
                    addLog('🔄 Simulating inactivity - session manager disabled');
                }
            });

            // Auto-check every 10 seconds
            setInterval(checkSession, 10000);
            
            // Initial check
            checkSession();
            addLog('🚀 Session test page loaded');
        });
    </script>
@endsection
