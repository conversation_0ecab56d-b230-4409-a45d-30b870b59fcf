<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->string('custom_id')->unique()->nullable()->after('id');
            $table->index('custom_id');
        });

        // Generate custom IDs for existing transactions
        $transactions = DB::table('transactions')->whereNull('custom_id')->get();
        
        foreach ($transactions as $transaction) {
            $timestamp = now()->format('ymd');
            $random = strtoupper(Str::random(6));
            
            do {
                $customId = "TXN-{$timestamp}-{$random}";
                $exists = DB::table('transactions')->where('custom_id', $customId)->exists();
                if ($exists) {
                    $random = strtoupper(Str::random(6));
                }
            } while ($exists);
            
            DB::table('transactions')->where('id', $transaction->id)->update(['custom_id' => $customId]);
        }

        // Make custom_id not nullable after populating
        Schema::table('transactions', function (Blueprint $table) {
            $table->string('custom_id')->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropIndex(['custom_id']);
            $table->dropColumn('custom_id');
        });
    }
};
