<x-layout>
    <div>
        <h2 class="text-center text-2xl font-bold text-gray-900 mb-6">
            {{ __('Reset Password') }}
        </h2>
        <p class="text-center text-sm text-gray-600 mb-6">
            {{ __('Enter the verification code sent to your email/phone and your new password.') }}
        </p>

        @if(session('success'))
            <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                {{ session('success') }}
            </div>
        @endif

        <form method="POST" action="{{ route('password.reset.submit') }}" class="space-y-6">
            @csrf
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700">
                    {{ __('Email Address') }}
                </label>
                <div class="mt-1">
                    <input id="email" name="email" type="email" value="{{ old('email', $email ?? '') }}" required
                           class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                </div>
                @error('email')
                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="otp" class="block text-sm font-medium text-gray-700">
                    {{ __('Verification Code (OTP)') }}
                </label>
                <div class="mt-1">
                    <input id="otp" name="otp" type="text" value="{{ old('otp') }}" required maxlength="6"
                           class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                           placeholder="Enter 6-digit code">
                </div>
                @error('otp')
                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="password" class="block text-sm font-medium text-gray-700">
                    {{ __('New Password') }}
                </label>
                <div class="mt-1 relative">
                    <input id="password" name="password" type="password" required
                           class="appearance-none block w-full px-3 pr-10 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <button type="button" onclick="togglePassword('password')" class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 transition-colors duration-200">
                            <i class="fas fa-eye" id="password_icon"></i>
                        </button>
                    </div>
                </div>
                @error('password')
                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="password_confirmation" class="block text-sm font-medium text-gray-700">
                    {{ __('Confirm New Password') }}
                </label>
                <div class="mt-1 relative">
                    <input id="password_confirmation" name="password_confirmation" type="password" required
                           class="appearance-none block w-full px-3 pr-10 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <button type="button" onclick="togglePassword('password_confirmation')" class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 transition-colors duration-200">
                            <i class="fas fa-eye" id="password_confirmation_icon"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div>
                <button type="submit"
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-key mr-2"></i>
                    {{ __('Reset Password') }}
                </button>
            </div>
        </form>

        <div class="mt-6 text-center space-x-4">
            <a href="{{ route('password.request') }}" class="text-sm text-blue-600 hover:text-blue-500">
                {{ __('Request New Code') }}
            </a>
            <span class="text-gray-300">|</span>
            <a href="{{ route('login') }}" class="text-sm text-blue-600 hover:text-blue-500">
                {{ __('Back to Login') }}
            </a>
        </div>
    </div>

    <!-- JavaScript for Password Visibility Toggle -->
    <script>
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const icon = document.getElementById(fieldId + '_icon');

            if (field.type === 'password') {
                field.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }
    </script>
</x-layout>