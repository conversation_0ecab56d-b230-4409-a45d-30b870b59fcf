@extends('layouts.app')

@section('title', __('churches.church_details'))
@section('page-title', __('churches.church_details'))

@section('breadcrumbs')
    <li>
        <span class="mx-2">/</span>
        <a href="{{ route('churches.index') }}" class="hover:text-gray-700">{{ __('churches.churches') }}</a>
    </li>
    <li>
        <span class="mx-2">/</span>
        <span class="font-medium text-gray-900">{{ $church->name }}</span>
    </li>
@endsection

@section('page-actions')
    <div class="flex items-center space-x-3">
        <!-- Language Switcher -->
        <x-language-switcher position="bottom-right" size="normal" />

        <a href="{{ route('churches.edit', $church) }}"
           class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <i class="fas fa-edit mr-2"></i>
            {{ __('common.edit') }}
        </a>
        <a href="{{ route('churches.index') }}"
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <i class="fas fa-arrow-left mr-2"></i>
            {{ __('common.back') }}
        </a>
    </div>
@endsection

@section('content')
    <div class="max-w-7xl mx-auto space-y-6">
        <!-- Quick Language Switcher -->
        <div class="flex justify-end">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-2">
                <div class="flex items-center space-x-2 text-sm">
                    <i class="fas fa-globe text-gray-400"></i>
                    <span class="text-gray-600">{{ __('common.language') }}:</span>
                    <div class="flex space-x-1">
                        @foreach(config('app.locale_names') as $locale => $info)
                            <button onclick="switchLanguage('{{ $locale }}')"
                                    class="px-2 py-1 rounded text-xs font-medium transition-colors duration-200 {{ app()->getLocale() === $locale ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100' }}">
                                {{ $info['flag'] }} {{ $info['native'] }}
                            </button>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
        <!-- Church Header -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="bg-gradient-to-r from-green-600 to-green-700 px-6 py-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-white">{{ $church->name }}</h1>
                        <div class="flex items-center mt-2 space-x-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                <i class="fas fa-layer-group text-xs mr-2"></i>
                                {{ __('common.' . strtolower($church->level->value)) }}
                            </span>
                            <span class="text-green-100 flex items-center">
                                <i class="fas fa-map-marker-alt mr-2"></i>
                                {{ $church->location }}
                            </span>
                            @if($church->date_established)
                                <span class="text-green-100 flex items-center">
                                    <i class="fas fa-calendar mr-2"></i>
                                    {{ __('churches.established') }} {{ $church->date_established->format('Y') }}
                                </span>
                            @endif
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-green-100 text-sm">{{ __('churches.church_id') }}</div>
                        <div class="text-white text-2xl font-bold">{{ $church->custom_id }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-users text-blue-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">{{ __('churches.total_members') }}</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $stats['total_users'] }}</p>
                        <p class="text-xs text-gray-500">{{ $stats['active_users'] }} {{ __('common.active') }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-crown text-green-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">{{ __('churches.leaders') }}</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $stats['total_leaders'] }}</p>
                        <p class="text-xs text-gray-500">{{ __('churches.leadership_positions') }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-sitemap text-purple-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">{{ __('churches.child_churches') }}</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $stats['child_churches'] }}</p>
                        <p class="text-xs text-gray-500">{{ __('churches.under_supervision') }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-calendar-alt text-orange-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">{{ __('churches.established') }}</p>
                        <p class="text-lg font-bold text-gray-900">
                            {{ $church->date_established ? $church->date_established->format('M Y') : __('common.unknown') }}
                        </p>
                        <p class="text-xs text-gray-500">
                            {{ $church->date_established ? $church->date_established->diffForHumans() : '' }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Demographic Statistics -->
        @if($church->youth_count || $church->young_adults_count || $church->children_count || $church->elders_count)
        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-chart-pie mr-2 text-indigo-600"></i>
                    {{ __('churches.demographic_statistics') }}
                </h3>
                <p class="mt-1 text-sm text-gray-600">{{ __('churches.age_group_breakdown') }}</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <div class="text-2xl font-bold text-blue-600">{{ $church->children_count ?? 0 }}</div>
                        <div class="text-sm font-medium text-blue-800">{{ __('churches.children') }}</div>
                        <div class="text-xs text-blue-600">{{ __('churches.age_0_12') }}</div>
                    </div>
                    <div class="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                        <div class="text-2xl font-bold text-green-600">{{ $church->youth_count ?? 0 }}</div>
                        <div class="text-sm font-medium text-green-800">{{ __('churches.youth') }}</div>
                        <div class="text-xs text-green-600">{{ __('churches.age_13_17') }}</div>
                    </div>
                    <div class="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
                        <div class="text-2xl font-bold text-purple-600">{{ $church->young_adults_count ?? 0 }}</div>
                        <div class="text-sm font-medium text-purple-800">{{ __('churches.young_adults') }}</div>
                        <div class="text-xs text-purple-600">{{ __('churches.age_18_35') }}</div>
                    </div>
                    <div class="text-center p-4 bg-orange-50 rounded-lg border border-orange-200">
                        <div class="text-2xl font-bold text-orange-600">{{ $church->elders_count ?? 0 }}</div>
                        <div class="text-sm font-medium text-orange-800">{{ __('churches.elders') }}</div>
                        <div class="text-xs text-orange-600">{{ __('churches.age_36_plus') }}</div>
                    </div>
                </div>

                @php
                    $totalDemographic = ($church->children_count ?? 0) + ($church->youth_count ?? 0) + ($church->young_adults_count ?? 0) + ($church->elders_count ?? 0);
                @endphp

                @if($totalDemographic > 0)
                <div class="mt-6 pt-4 border-t border-gray-200">
                    <div class="flex items-center justify-between text-sm">
                        <span class="font-medium text-gray-700">{{ __('churches.total_demographic_count') }}:</span>
                        <span class="font-bold text-gray-900">{{ $totalDemographic }} {{ __('churches.members') }}</span>
                    </div>
                </div>
                @endif
            </div>
        </div>
        @endif

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Church Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-info-circle mr-2 text-blue-600"></i>
                            {{ __('churches.basic_information') }}
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-600">{{ __('churches.church_name') }}</label>
                                <p class="mt-1 text-lg font-semibold text-gray-900">{{ $church->name }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600">{{ __('churches.church_level') }}</label>
                                <p class="mt-1 text-lg text-gray-900">{{ __('common.' . strtolower($church->level->value)) }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600">{{ __('churches.location') }}</label>
                                <p class="mt-1 text-lg text-gray-900">{{ $church->location }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600">{{ __('churches.date_established') }}</label>
                                <p class="mt-1 text-lg text-gray-900">
                                    {{ $church->date_established ? $church->date_established->format('F d, Y') : __('common.not_specified') }}
                                </p>
                            </div>
                        </div>

                        <!-- Contact Information Section -->
                        <div class="mt-8 pt-6 border-t border-gray-200">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <i class="fas fa-address-book mr-2 text-green-600"></i>
                                {{ __('common.contact_information') }}
                            </h4>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                                @if($church->phone_number)
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">{{ __('churches.phone_number') }}</label>
                                    <p class="mt-1 text-lg text-gray-900 flex items-center">
                                        <i class="fas fa-phone mr-2 text-blue-500"></i>
                                        <a href="tel:{{ $church->phone_number }}" class="hover:text-blue-600">{{ $church->phone_number }}</a>
                                    </p>
                                </div>
                                @endif

                                @if($church->email)
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">{{ __('churches.email') }}</label>
                                    <p class="mt-1 text-lg text-gray-900 flex items-center">
                                        <i class="fas fa-envelope mr-2 text-green-500"></i>
                                        <a href="mailto:{{ $church->email }}" class="hover:text-green-600">{{ $church->email }}</a>
                                    </p>
                                </div>
                                @endif

                                @if($church->district)
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">{{ __('churches.district') }}</label>
                                    <p class="mt-1 text-lg text-gray-900 flex items-center">
                                        <i class="fas fa-map mr-2 text-purple-500"></i>
                                        {{ $church->district }}
                                    </p>
                                </div>
                                @endif

                                @if($church->region)
                                <div>
                                    <label class="block text-sm font-medium text-gray-600">{{ __('churches.region') }}</label>
                                    <p class="mt-1 text-lg text-gray-900 flex items-center">
                                        <i class="fas fa-globe-africa mr-2 text-orange-500"></i>
                                        {{ $church->region }}
                                    </p>
                                </div>
                                @endif

                                @if($church->address)
                                <div class="sm:col-span-2">
                                    <label class="block text-sm font-medium text-gray-600">{{ __('churches.address') }}</label>
                                    <p class="mt-1 text-lg text-gray-900 flex items-start">
                                        <i class="fas fa-map-marked-alt mr-2 text-red-500 mt-1"></i>
                                        <span class="whitespace-pre-line">{{ $church->address }}</span>
                                    </p>
                                </div>
                                @endif
                            </div>
                        </div>

                        @if($church->parentChurch)
                            <div class="mt-6 pt-6 border-t border-gray-200">
                                <label class="block text-sm font-medium text-gray-600">{{ __('churches.parent_church') }}</label>
                                <div class="mt-2 flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                    <div>
                                        <p class="font-medium text-gray-900">{{ $church->parentChurch->name }}</p>
                                        <p class="text-sm text-gray-600">{{ __('common.' . strtolower($church->parentChurch->level->value)) }} - {{ $church->parentChurch->location }}</p>
                                    </div>
                                    <a href="{{ route('churches.show', $church->parentChurch) }}"
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        {{ __('common.view') }} →
                                    </a>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Leaders Section -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-crown mr-2 text-green-600"></i>
                            {{ __('churches.church_leaders') }}
                            <span class="ml-2 bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                {{ $leaders->count() }}
                            </span>
                        </h3>
                    </div>
                    <div class="p-6">
                        @if($leaders->count() > 0)
                            <div class="space-y-4">
                                @foreach($leaders as $leader)
                                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                @if($leader->user->profile_picture)
                                                    <img class="h-12 w-12 rounded-full object-cover"
                                                         src="{{ asset('storage/' . $leader->user->profile_picture) }}"
                                                         alt="{{ $leader->user->full_name }}">
                                                @else
                                                    <div class="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center">
                                                        <span class="text-sm font-medium text-green-600">
                                                            {{ strtoupper(substr($leader->user->full_name, 0, 1)) }}
                                                        </span>
                                                    </div>
                                                @endif
                                            </div>
                                            <div class="ml-4">
                                                <p class="text-sm font-medium text-gray-900">{{ $leader->user->full_name }}</p>
                                                <p class="text-sm text-gray-600">{{ $leader->role }}</p>
                                                <p class="text-xs text-gray-500">{{ $leader->user->email }}</p>
                                                @if($leader->user->phone_number)
                                                <p class="text-xs text-gray-500">
                                                    <i class="fas fa-phone mr-1"></i>
                                                    {{ $leader->user->phone_number }}
                                                </p>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                {{ __('churches.leader') }}
                                            </span>
                                            <a href="{{ route('users.show', $leader->user) }}"
                                               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                                {{ __('common.view') }}
                                            </a>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-8">
                                <i class="fas fa-crown text-gray-300 text-4xl mb-4"></i>
                                <p class="text-gray-500 text-lg">{{ __('churches.no_leaders_assigned') }}</p>
                                <p class="text-gray-400 text-sm">{{ __('churches.leaders_auto_assigned') }}</p>
                                <p class="text-gray-400 text-xs mt-2">{{ __('churches.leaders_based_on_roles') }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Church Members -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-users mr-2 text-blue-600"></i>
                            {{ __('churches.church_members') }}
                            <span class="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                {{ $church->users->count() }}
                            </span>
                        </h3>
                    </div>
                    <div class="p-6">
                        @if($church->users->count() > 0)
                            <div class="space-y-3 max-h-96 overflow-y-auto">
                                @foreach($church->users->take(10) as $user)
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                @if($user->profile_picture)
                                                    <img class="h-8 w-8 rounded-full object-cover"
                                                         src="{{ asset('storage/' . $user->profile_picture) }}"
                                                         alt="{{ $user->full_name }}">
                                                @else
                                                    <div class="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                                                        <span class="text-xs font-medium text-blue-600">
                                                            {{ strtoupper(substr($user->full_name, 0, 1)) }}
                                                        </span>
                                                    </div>
                                                @endif
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900">{{ $user->full_name }}</p>
                                                <p class="text-xs text-gray-500">{{ $user->role }}</p>
                                            </div>
                                        </div>
                                        <a href="{{ route('users.show', $user) }}"
                                           class="text-blue-600 hover:text-blue-800 text-xs font-medium">
                                            {{ __('common.view') }}
                                        </a>
                                    </div>
                                @endforeach
                                @if($church->users->count() > 10)
                                    <div class="text-center pt-3 border-t border-gray-200">
                                        <p class="text-sm text-gray-500">
                                            {{ __('churches.and_more_members', ['count' => $church->users->count() - 10]) }}
                                        </p>
                                    </div>
                                @endif
                            </div>
                        @else
                            <div class="text-center py-6">
                                <i class="fas fa-users text-gray-300 text-3xl mb-3"></i>
                                <p class="text-gray-500">{{ __('churches.no_members') }}</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Child Churches -->
                @if($church->childChurches->count() > 0)
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                <i class="fas fa-sitemap mr-2 text-purple-600"></i>
                                {{ __('churches.child_churches') }}
                                <span class="ml-2 bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                    {{ $church->childChurches->count() }}
                                </span>
                            </h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3">
                                @foreach($church->childChurches as $child)
                                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">{{ $child->name }}</p>
                                            <p class="text-xs text-gray-500">{{ __('common.' . strtolower($child->level->value)) }} - {{ $child->location }}</p>
                                        </div>
                                        <a href="{{ route('churches.show', $child) }}"
                                           class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                                            {{ __('common.view') }} →
                                        </a>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection