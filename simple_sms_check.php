<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== SMS Status Check ===\n\n";

// Get recent SMS logs
$logs = \App\Models\SmsLog::latest()->take(5)->get(['phone_number', 'status', 'type', 'created_at', 'error_message']);

foreach ($logs as $log) {
    echo "Phone: {$log->phone_number} | Status: {$log->status} | Type: {$log->type} | Time: {$log->created_at}\n";
    if ($log->error_message) {
        echo "  Error: {$log->error_message}\n";
    }
}

echo "\n=== Summary ===\n";
$total = \App\Models\SmsLog::count();
$sent = \App\Models\SmsLog::where('status', 'sent')->count();
$failed = \App\Models\SmsLog::where('status', 'failed')->count();
$pending = \App\Models\SmsLog::where('status', 'pending')->count();

echo "Total SMS: {$total}\n";
echo "Sent: {$sent}\n";
echo "Failed: {$failed}\n";
echo "Pending: {$pending}\n";

// Test user registration flow
echo "\n=== Testing User Registration SMS ===\n";
$testUser = \App\Models\User::latest()->first();
if ($testUser) {
    echo "Latest user: {$testUser->full_name} ({$testUser->phone_number})\n";
    
    // Check if this user has SMS logs
    $userSms = \App\Models\SmsLog::where('phone_number', $testUser->phone_number)->latest()->first();
    if ($userSms) {
        echo "Latest SMS for this user: {$userSms->status} at {$userSms->created_at}\n";
    } else {
        echo "No SMS found for this user\n";
    }
}
