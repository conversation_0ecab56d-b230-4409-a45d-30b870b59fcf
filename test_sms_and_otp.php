<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// Test SMS and OTP functionality
echo "=== FPCT SMS and OTP Test ===\n\n";

try {
    // Test 1: Check SMS service
    echo "1. Testing SMS Service...\n";
    $smsService = app(\App\Services\HudumaSMSService::class);
    
    $testResult = $smsService->sendSMS(
        '255787504956',
        'Test SMS from FPCT system - ' . date('Y-m-d H:i:s'),
        'test'
    );
    
    echo "SMS Test Result: " . ($testResult ? "SUCCESS" : "FAILED") . "\n\n";
    
    // Test 2: Check recent SMS logs
    echo "2. Recent SMS Logs:\n";
    $recentSms = \App\Models\SmsLog::latest()->take(3)->get(['phone_number', 'message', 'status', 'created_at']);
    foreach ($recentSms as $sms) {
        echo "- {$sms->phone_number}: {$sms->status} at {$sms->created_at}\n";
    }
    echo "\n";
    
    // Test 3: Test OTP generation
    echo "3. Testing OTP Generation...\n";
    $testUser = \App\Models\User::first();
    if ($testUser) {
        $otpService = app(\App\Services\OTPService::class);
        $otp = $otpService->generateOTP($testUser, 'test', 5);
        echo "Generated OTP for user {$testUser->id}: {$otp}\n";
        
        // Test admin resend
        echo "Testing admin OTP resend...\n";
        $adminUser = \App\Models\User::whereHas('roles', function($q) {
            $q->where('name', 'National IT');
        })->first();
        
        if ($adminUser) {
            $newOtp = $otpService->adminResendOTP($testUser, 'test', $adminUser);
            echo "Admin resend OTP: {$newOtp}\n";
        } else {
            echo "No admin user found\n";
        }
    } else {
        echo "No test user found\n";
    }
    echo "\n";
    
    // Test 4: Test notification service
    echo "4. Testing Notification Service...\n";
    $notificationService = app(\App\Services\NotificationService::class);
    
    if ($testUser) {
        echo "Sending test welcome SMS...\n";
        $notificationService->sendWelcomeSMS($testUser, 'TestPass123', '123456');
        echo "Welcome SMS test completed\n";
    }
    echo "\n";
    
    // Test 5: Check environment configuration
    echo "5. Environment Configuration:\n";
    echo "SMS API Token: " . (config('services.hudumasms.api_token') ? "SET" : "NOT SET") . "\n";
    echo "SMS Sender ID: " . config('services.hudumasms.sender_id') . "\n";
    echo "SMS Base URL: " . config('services.hudumasms.base_url') . "\n";
    echo "App Environment: " . config('app.env') . "\n";
    
} catch (\Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== Test Complete ===\n";
