# FPCT Church Management System (CMS) - Admin Login Credentials

## 🔐 Super Administrator Login

**Email:** `<EMAIL>`  
**Password:** `admin123`  
**Role:** Archbishop (Full System Access)  
**Church:** FPCT National Headquarters  

## 🧪 Test Users

### National Assistant
**Email:** `<EMAIL>`  
**Password:** `assistant123`  
**Role:** National Assistant  
**Status:** First login required (will need to change password)

### General Secretary
**Email:** `<EMAIL>`  
**Password:** `secretary123`  
**Role:** General Secretary  
**Status:** First login required (will need to change password)

## 🚀 Getting Started

1. **Start the development server:**
   ```bash
   php artisan serve
   ```

2. **Access the system:**
   - Open your browser and go to: `http://localhost:8000`
   - Use the super admin credentials above to login

3. **First Steps After Login:**
   - Change the default password
   - Set up additional regional churches
   - Create regional administrators
   - Configure SMS settings with real Africa's Talking credentials

## ⚙️ System Management Commands

### Reset Admin Password
```bash
php artisan admin:reset-password
# or with custom password:
php artisan admin:reset-password --password=newpassword123
```

### Check System Status
```bash
php artisan system:status
```

### Test SMS Service
```bash
php artisan sms:test +255712345678 --message="Test message"
```

### Run All Seeders
```bash
php artisan db:seed
```

## 🔧 Configuration

### SMS Service (Africa's Talking)
Update your `.env` file with real credentials:
```env
AFRICASTALKING_USERNAME=your-actual-username
AFRICASTALKING_API_KEY=your-actual-api-key
AFRICASTALKING_FROM=FPCT
```

### Email Configuration
```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="FPCT Church Management System (CMS)"
```

### Broadcasting (for real-time features)
```env
BROADCAST_CONNECTION=pusher
PUSHER_APP_ID=your-app-id
PUSHER_APP_KEY=your-app-key
PUSHER_APP_SECRET=your-app-secret
PUSHER_APP_CLUSTER=your-cluster
```

## 🏢 Church Hierarchy Setup

The system is pre-configured with:
- **National Level:** FPCT National Headquarters (Dar es Salaam)
- **Super Admin:** Archbishop with full system access

Next steps:
1. Create Diocese churches under National HQ
2. Create Local churches under Diocese churches
3. Create Parishes under Local churches
4. Create Branches under Parishes

## 🔒 Security Notes

- **Change default passwords immediately**
- The super admin account has full system access
- Test users have first login flag set (must change password)
- All actions are logged in the audit trail
- SMS and email notifications are enabled by default

## 📱 Features Available

✅ **User Management** - Create, edit, activate/deactivate users  
✅ **Church Hierarchy** - Manage 5-level church structure  
✅ **Request System** - Submit and approve church requests  
✅ **Messaging** - Send messages, announcements, group messages  
✅ **SMS Integration** - Africa's Talking SMS service  
✅ **Role-Based Permissions** - Hierarchical access control  
✅ **Audit Logging** - Complete activity tracking  
✅ **Real-time Notifications** - WebSocket ready  

## 🆘 Support

If you encounter any issues:
1. Check the Laravel logs: `storage/logs/laravel.log`
2. Run system status: `php artisan system:status`
3. Verify database connection and migrations
4. Check SMS service configuration

---

**⚠️ IMPORTANT:** This is a development setup. For production:
- Use strong passwords
- Configure proper SSL certificates
- Set up proper backup procedures
- Configure production-grade database
- Set up monitoring and logging
