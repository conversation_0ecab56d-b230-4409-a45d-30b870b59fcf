<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;
use App\Models\ApprovalWorkflow;
use App\Models\MessageRecipient;
use App\Models\Session;
use App\Traits\HasCustomId;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles, HasCustomId;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
       'full_name', 'email', 'phone_number', 'password', 'role', 'church_id',
        'is_first_login', 'is_active', 'profile_picture', 'date_of_birth',
        'gender', 'address', 'bio', 'emergency_contact_name', 'emergency_contact_phone',
        'locale', 'custom_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }
    protected $casts = [
        'is_first_login' => 'boolean',
        'is_active' => 'boolean',
        'date_of_birth' => 'date',
    ];

    public function church()
    {
        return $this->belongsTo(Church::class);
    }

    public function sentMessages()
    {
        return $this->hasMany(Message::class, 'sender_id');
    }

    public function receivedMessages()
    {
        return $this->belongsToMany(Message::class, 'message_recipient', 'user_id', 'message_id')
                    ->withPivot('read_at')
                    ->withTimestamps();
    }

    public function sessions()
    {
        return $this->hasMany(Session::class);
    }

    public function requests()
    {
        return $this->hasMany(Request::class, 'user_id');
    }

    public function approvals()
    {
        return $this->hasMany(Request::class, 'approved_by');
    }

    public function notificationPreferences()
    {
        return $this->hasOne(NotificationPreference::class);
    }

    public function approvalWorkflows()
    {
        return $this->hasMany(ApprovalWorkflow::class, 'approver_user_id');
    }

    public function auditLogs()
    {
        return $this->hasMany(AuditLog::class);
    }

    public function otps()
    {
        return $this->hasMany(OTP::class);
    }

    // Helper methods
    public function getNotificationPreferences()
    {
        return $this->notificationPreferences ?:
               $this->notificationPreferences()->create(NotificationPreference::getDefaultPreferences());
    }

    public function hasPermissionInChurch(string $permission, ?Church $church = null): bool
    {
        $church = $church ?: $this->church;

        if (!$church) {
            return false;
        }

        // Check if user has the permission and is in the correct church hierarchy
        return $this->hasPermissionTo($permission) &&
               ($this->church_id === $church->id || $church->isAncestorOf($this->church()->first()));
    }

    public function canApproveRequestsFor(Church $church): bool
    {
        return $this->church->canApproveRequestsFor($church) &&
               $this->hasPermissionTo('approve-requests');
    }

    public function isFirstLogin(): bool
    {
        return $this->is_first_login;
    }

    public function markFirstLoginComplete(): void
    {
        $this->update(['is_first_login' => false]);
        AuditLog::log('first_login_completed', $this, [], [], 'User completed first login');
    }

    public function activate(): void
    {
        $this->update(['is_active' => true]);
        AuditLog::log('user_activated', $this, ['is_active' => false], ['is_active' => true]);
    }

    public function deactivate(): void
    {
        $this->update(['is_active' => false]);
        AuditLog::log('user_deactivated', $this, ['is_active' => true], ['is_active' => false]);
    }

    // Query Scopes for Performance
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    public function scopeFirstLogin($query)
    {
        return $query->where('is_first_login', true);
    }

    public function scopeByChurch($query, $churchId)
    {
        return $query->where('church_id', $churchId);
    }

    public function scopeByRole($query, $role)
    {
        return $query->whereHas('roles', function ($q) use ($role) {
            $q->where('name', $role);
        });
    }

    public function scopeWithChurchInfo($query)
    {
        return $query->with(['church:id,name,level,location']);
    }

    public function scopeWithRolesAndPermissions($query)
    {
        return $query->with(['roles.permissions']);
    }

    // Profile Picture Methods
    public function getProfilePictureUrlAttribute()
    {
        if ($this->profile_picture) {
            return asset('storage/' . $this->profile_picture);
        }

        // Return default avatar based on first letter of name
        return $this->getDefaultAvatarUrl();
    }

    public function getDefaultAvatarUrl()
    {
        return "https://ui-avatars.com/api/?name=" . urlencode($this->full_name) . "&background=3B82F6&color=ffffff&size=200";
    }

    public function hasProfilePicture()
    {
        return !empty($this->profile_picture) && file_exists(storage_path('app/public/' . $this->profile_picture));
    }

    public function scopeInChurchHierarchy($query, Church $church)
    {
        $churchIds = collect([$church->id])
            ->merge($church->getAllDescendants()->pluck('id'))
            ->toArray();

        return $query->whereIn('church_id', $churchIds);
    }

    // Approval-related methods
    public function pendingApprovals()
    {
        return ApprovalWorkflow::where('approver_church_id', $this->church_id)
                              ->where('status', 'pending');
    }

    public function unreadMessages()
    {
        return MessageRecipient::where('user_id', $this->id)
                              ->whereNull('read_at');
    }

    // Helper methods for counts
    public function getPendingApprovalsCountAttribute()
    {
        return $this->pendingApprovals()->count();
    }

    public function getUnreadMessagesCountAttribute()
    {
        return $this->unreadMessages()->count();
    }

    /**
     * Get the custom ID prefix for User model
     */
    protected function getCustomIdPrefix(): string
    {
        return 'USR';
    }
}
