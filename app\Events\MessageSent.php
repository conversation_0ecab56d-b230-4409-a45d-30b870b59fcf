<?php

namespace App\Events;

use App\Models\Message;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;

class MessageSent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets;

    public $message;

    public function __construct(Message $message)
    {
        $this->message = $message;
    }

    public function broadcastOn()
    {
        $channels = [];
        foreach ($this->message->recipients as $recipient) {
            $channels[] = new Channel('user.' . $recipient->id);
        }
        return $channels;
    }

    public function broadcastWith()
    {
        return [
            'message' => [
                'id' => $this->message->id,
                'sender' => $this->message->sender->full_name,
                'content' => $this->message->content,
                'created_at' => $this->message->created_at->toDateTimeString(),
                'is_group_message' => $this->message->is_group_message,
                'is_announcement' => $this->message->is_announcement,
            ],
        ];
    }
}