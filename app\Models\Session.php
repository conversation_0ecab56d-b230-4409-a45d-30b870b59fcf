<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Session extends Model
{
    protected $table = 'sessions';
    
    protected $primaryKey = 'id';
    
    public $incrementing = false;
    
    protected $keyType = 'string';
    
    public $timestamps = false;
    
    protected $fillable = [
        'id', 'user_id', 'ip_address', 'user_agent', 'payload', 'last_activity'
    ];
    
    protected $casts = [
        'last_activity' => 'integer',
    ];
    
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    public function isActive()
    {
        // Consider session active if last activity was within 5 minutes
        return $this->last_activity > (time() - 300);
    }
    
    public function getLastActivityAttribute($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : null;
    }
}
