@extends('layouts.app')

@section('title', __('Transactions'))

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ __('Financial Transactions') }}</h1>
                    <p class="mt-2 text-gray-600">{{ __('Manage and track all financial transactions') }}</p>
                </div>
                <div class="flex space-x-3">
                    @can('create-transactions')
                        <a href="{{ route('transactions.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-plus mr-2"></i>{{ __('Send Revenue') }}
                        </a>
                    @endcan
                    <a href="{{ route('financial.dashboard') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-chart-line mr-2"></i>{{ __('Dashboard') }}
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Transactions -->
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-white bg-opacity-30 rounded-md flex items-center justify-center">
                                <i class="fas fa-exchange-alt text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-blue-100 truncate uppercase tracking-wide">{{ __('Total Transactions') }}</dt>
                                <dd class="text-lg font-medium text-white">{{ $transactions->total() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Completed Transactions -->
            <div class="bg-gradient-to-r from-green-500 to-green-600 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-white bg-opacity-30 rounded-md flex items-center justify-center">
                                <i class="fas fa-check-circle text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-green-100 truncate uppercase tracking-wide">{{ __('Completed') }}</dt>
                                <dd class="text-lg font-medium text-white">{{ $transactions->where('status', 'completed')->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pending Transactions -->
            <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-white bg-opacity-30 rounded-md flex items-center justify-center">
                                <i class="fas fa-clock text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-yellow-100 truncate uppercase tracking-wide">{{ __('Pending') }}</dt>
                                <dd class="text-lg font-medium text-white">{{ $transactions->where('status', 'pending')->count() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Amount -->
            <div class="bg-gradient-to-r from-purple-500 to-purple-600 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-white bg-opacity-30 rounded-md flex items-center justify-center">
                                <i class="fas fa-coins text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-purple-100 truncate uppercase tracking-wide">{{ __('Total Amount') }}</dt>
                                <dd class="text-lg font-medium text-white">{{ number_format($transactions->sum('amount'), 2) }} TZS</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Card -->
        <div class="bg-white shadow rounded-lg mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i class="fas fa-filter mr-2 text-blue-600"></i>
                    {{ __('Filters') }}
                </h3>
            </div>
            <div class="px-6 py-6">
                <form method="GET" action="{{ route('transactions.index') }}" class="grid grid-cols-1 md:grid-cols-6 gap-4">
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">{{ __('Status') }}</label>
                        <select class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" id="status" name="status">
                            <option value="">{{ __('All Statuses') }}</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>{{ __('Pending') }}</option>
                            <option value="processing" {{ request('status') == 'processing' ? 'selected' : '' }}>{{ __('Processing') }}</option>
                            <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>{{ __('Completed') }}</option>
                            <option value="failed" {{ request('status') == 'failed' ? 'selected' : '' }}>{{ __('Failed') }}</option>
                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>{{ __('Cancelled') }}</option>
                        </select>
                    </div>

                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-2">{{ __('Type') }}</label>
                        <select class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" id="type" name="type">
                            <option value="">{{ __('All Types') }}</option>
                            <option value="revenue_collection" {{ request('type') == 'revenue_collection' ? 'selected' : '' }}>{{ __('Revenue Collection') }}</option>
                            <option value="contribution" {{ request('type') == 'contribution' ? 'selected' : '' }}>{{ __('Contribution') }}</option>
                            <option value="transfer" {{ request('type') == 'transfer' ? 'selected' : '' }}>{{ __('Transfer') }}</option>
                            <option value="refund" {{ request('type') == 'refund' ? 'selected' : '' }}>{{ __('Refund') }}</option>
                        </select>
                    </div>

                    <div>
                        <label for="date_from" class="block text-sm font-medium text-gray-700 mb-2">{{ __('From Date') }}</label>
                        <input type="date" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" id="date_from" name="date_from" value="{{ request('date_from') }}">
                    </div>

                    <div>
                        <label for="date_to" class="block text-sm font-medium text-gray-700 mb-2">{{ __('To Date') }}</label>
                        <input type="date" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" id="date_to" name="date_to" value="{{ request('date_to') }}">
                    </div>

                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 mb-2">{{ __('Search') }}</label>
                        <input type="text" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" id="search" name="search" value="{{ request('search') }}" placeholder="{{ __('Transaction ID, Reference...') }}">
                    </div>

                    <div class="flex items-end">
                        <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-search mr-2"></i>{{ __('Filter') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Transactions Table -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900 flex items-center">
                        <i class="fas fa-list mr-2 text-blue-600"></i>
                        {{ __('Transactions List') }}
                    </h3>
                    <div class="relative">
                        <button type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="toggleActionsDropdown()">
                            <i class="fas fa-cog mr-2"></i>{{ __('Actions') }}
                            <i class="fas fa-chevron-down ml-2"></i>
                        </button>
                        <div id="actionsDropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                            <div class="py-1">
                                <a href="{{ route('financial-reports.create') }}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-chart-bar mr-2"></i>{{ __('Generate Report') }}
                                </a>
                                <a href="{{ route('receipts.create') }}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-receipt mr-2"></i>{{ __('Generate Receipts') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="overflow-hidden">
                @if($transactions->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Date') }}</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Transaction ID') }}</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Type') }}</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Amount') }}</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('From') }}</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('To') }}</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Status') }}</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Payment Method') }}</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Actions') }}</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($transactions as $transaction)
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $transaction->created_at->format('M j, Y H:i') }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ $transaction->transaction_id }}</div>
                                            @if($transaction->contribution)
                                                <div class="text-sm text-gray-500">{{ $transaction->contribution->name }}</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                {{ $transaction->type->getLabel() }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {{ number_format($transaction->amount, 2) }} TZS
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                @if($transaction->from_church_id === $church->id)
                                                    <i class="fas fa-arrow-up text-red-500 mr-2"></i>
                                                @endif
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">{{ $transaction->fromChurch->name }}</div>
                                                    <div class="text-sm text-gray-500">{{ $transaction->fromChurch->level->value }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                @if($transaction->to_church_id === $church->id)
                                                    <i class="fas fa-arrow-down text-green-500 mr-2"></i>
                                                @endif
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">{{ $transaction->toChurch->name }}</div>
                                                    <div class="text-sm text-gray-500">{{ $transaction->toChurch->level->value }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $transaction->status->value === 'completed' ? 'bg-green-100 text-green-800' : ($transaction->status->value === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                                {{ $transaction->status->getLabel() }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                {{ $transaction->payment_method->getLabel() }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <a href="{{ route('transactions.show', $transaction) }}" class="text-blue-600 hover:text-blue-900" title="{{ __('View Details') }}">
                                                    <i class="fas fa-eye"></i>
                                                </a>

                                                @if($transaction->status->value === 'pending' && $transaction->to_church_id === $church->id)
                                                    @can('approve-transactions')
                                                        <button type="button" class="text-green-600 hover:text-green-900" onclick="approveTransaction({{ $transaction->id }})" title="{{ __('Approve') }}">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button type="button" class="text-red-600 hover:text-red-900" onclick="rejectTransaction({{ $transaction->id }})" title="{{ __('Reject') }}">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    @endcan
                                                @endif

                                                @if($transaction->status->value === 'completed' && !$transaction->receipt)
                                                    @can('generate-receipts')
                                                        <form method="POST" action="{{ route('transactions.generate-receipt', $transaction) }}" style="display: inline;">
                                                            @csrf
                                                            <button type="submit" class="text-indigo-600 hover:text-indigo-900" title="{{ __('Generate Receipt') }}">
                                                                <i class="fas fa-receipt"></i>
                                                            </button>
                                                        </form>
                                                    @endcan
                                                @endif

                                                @if($transaction->receipt)
                                                    <a href="{{ route('receipts.show', $transaction->receipt) }}" class="text-gray-600 hover:text-gray-900" title="{{ __('View Receipt') }}">
                                                        <i class="fas fa-file-pdf"></i>
                                                    </a>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="flex items-center justify-between px-6 py-4 border-t border-gray-200">
                        <div class="text-sm text-gray-700">
                            {{ __('Showing') }} {{ $transactions->firstItem() }} {{ __('to') }} {{ $transactions->lastItem() }} {{ __('of') }} {{ $transactions->total() }} {{ __('results') }}
                    </div>
                    <div>
                        {{ $transactions->appends(request()->query())->links() }}
                    </div>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-exchange-alt fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-600">{{ __('No transactions found') }}</h5>
                    <p class="text-muted">{{ __('No transactions match your current filters.') }}</p>
                    @can('create-transactions')
                        <a href="{{ route('transactions.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> {{ __('Create First Transaction') }}
                        </a>
                    @endcan
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Approval Modal -->
<div class="modal fade" id="approvalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Approve Transaction') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>{{ __('Are you sure you want to approve this transaction?') }}</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                <form id="approvalForm" method="POST" style="display: inline;">
                    @csrf
                    <button type="submit" class="btn btn-success">{{ __('Approve') }}</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Rejection Modal -->
<div class="modal fade" id="rejectionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Reject Transaction') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="rejectionForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="reason">{{ __('Rejection Reason') }} <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" required placeholder="{{ __('Please provide a reason for rejection...') }}"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-danger">{{ __('Reject') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

    </div>
</div>

@push('scripts')
<script>
function toggleActionsDropdown() {
    const dropdown = document.getElementById('actionsDropdown');
    dropdown.classList.toggle('hidden');
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const dropdown = document.getElementById('actionsDropdown');
    const button = event.target.closest('button');

    if (!button || !button.onclick || button.onclick.toString().indexOf('toggleActionsDropdown') === -1) {
        dropdown.classList.add('hidden');
    }
});

function approveTransaction(transactionId) {
    const form = document.getElementById('approvalForm');
    form.action = `/transactions/${transactionId}/approve`;
    new bootstrap.Modal(document.getElementById('approvalModal')).show();
}

function rejectTransaction(transactionId) {
    const form = document.getElementById('rejectionForm');
    form.action = `/transactions/${transactionId}/reject`;
    new bootstrap.Modal(document.getElementById('rejectionModal')).show();
}
</script>
@endpush
@endsection
