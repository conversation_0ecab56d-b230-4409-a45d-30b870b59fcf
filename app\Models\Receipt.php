<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;
use App\Traits\HasCustomId;

class Receipt extends Model
{
    use HasCustomId;

    protected $fillable = [
        'receipt_number',
        'transaction_id',
        'issued_to_church_id',
        'issued_by_church_id',
        'issued_by_user_id',
        'amount',
        'currency',
        'description',
        'receipt_type',
        'pdf_path',
        'is_emailed',
        'emailed_at',
        'custom_id',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'is_emailed' => 'boolean',
        'emailed_at' => 'datetime',
    ];

    // Relationships
    public function transaction(): BelongsTo
    {
        return $this->belongsTo(Transaction::class);
    }

    public function issuedToChurch(): BelongsTo
    {
        return $this->belongsTo(Church::class, 'issued_to_church_id');
    }

    public function issuedByChurch(): BelongsTo
    {
        return $this->belongsTo(Church::class, 'issued_by_church_id');
    }

    public function issuedByUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'issued_by_user_id');
    }

    // Business Logic Methods
    public static function generateReceiptNumber(): string
    {
        return 'RCP-' . strtoupper(Str::random(6)) . '-' . now()->format('Ymd');
    }

    public function markAsEmailed(): void
    {
        $this->update([
            'is_emailed' => true,
            'emailed_at' => now(),
        ]);
    }

    public function getFormattedAmount(): string
    {
        return number_format($this->amount, 2) . ' ' . $this->currency;
    }

    public function getReceiptTypeLabel(): string
    {
        return match ($this->receipt_type) {
            'payment' => 'Payment Receipt',
            'contribution' => 'Contribution Receipt',
            'transfer' => 'Transfer Receipt',
            default => 'Receipt',
        };
    }

    /**
     * Get the custom ID prefix for Receipt model
     */
    protected function getCustomIdPrefix(): string
    {
        return 'RCP';
    }
}
