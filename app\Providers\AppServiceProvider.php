<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Church;
use App\Models\Message;
use App\Models\Request;
use App\Models\Contribution;
use App\Models\Receipt;
use App\Models\AuditLog;
use App\Models\OTP;
use App\Models\SmsLog;
use App\Observers\TransactionObserver;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register model observers
        Transaction::observe(TransactionObserver::class);

        // Configure route model binding for custom IDs
        $this->configureRouteModelBinding();
    }

    /**
     * Configure route model binding to use custom IDs
     */
    private function configureRouteModelBinding(): void
    {
        Route::bind('user', function ($value) {
            return User::where('custom_id', $value)->firstOrFail();
        });

        Route::bind('church', function ($value) {
            return Church::where('custom_id', $value)->firstOrFail();
        });

        Route::bind('message', function ($value) {
            return Message::where('custom_id', $value)->firstOrFail();
        });

        Route::bind('request', function ($value) {
            return Request::where('custom_id', $value)->firstOrFail();
        });

        Route::bind('transaction', function ($value) {
            return Transaction::where('custom_id', $value)->firstOrFail();
        });

        Route::bind('contribution', function ($value) {
            return Contribution::where('custom_id', $value)->firstOrFail();
        });

        Route::bind('receipt', function ($value) {
            return Receipt::where('custom_id', $value)->firstOrFail();
        });

        Route::bind('audit_log', function ($value) {
            return AuditLog::where('custom_id', $value)->firstOrFail();
        });

        Route::bind('otp', function ($value) {
            return OTP::where('custom_id', $value)->firstOrFail();
        });

        Route::bind('sms_log', function ($value) {
            return SmsLog::where('custom_id', $value)->firstOrFail();
        });
    }
}
