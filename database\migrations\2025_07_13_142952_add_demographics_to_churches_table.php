<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('churches', function (Blueprint $table) {
            $table->unsignedInteger('youth_count')->default(0)->after('current_balance');
            $table->unsignedInteger('young_adults_count')->default(0)->after('youth_count');
            $table->unsignedInteger('children_count')->default(0)->after('young_adults_count');
            $table->unsignedInteger('elders_count')->default(0)->after('children_count');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('churches', function (Blueprint $table) {
            $table->dropColumn(['youth_count', 'young_adults_count', 'children_count', 'elders_count']);
        });
    }
};
