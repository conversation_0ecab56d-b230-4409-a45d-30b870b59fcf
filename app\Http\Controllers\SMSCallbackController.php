<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Response;

class SMS<PERSON>allbackController extends Controller
{
    /**
     * Handle SMS delivery reports from Africa's Talking
     */
    public function handleDeliveryReport(Request $request)
    {
        try {
            // Log the incoming delivery report
            Log::info('SMS Delivery Report Received', [
                'data' => $request->all(),
                'headers' => $request->headers->all()
            ]);

            // Extract delivery report data
            $id = $request->input('id');
            $phoneNumber = $request->input('phoneNumber');
            $status = $request->input('status');
            $networkCode = $request->input('networkCode');
            $failureReason = $request->input('failureReason');

            // Log specific delivery information
            Log::info('SMS Delivery Status', [
                'id' => $id,
                'phone' => $phoneNumber,
                'status' => $status,
                'network' => $networkCode,
                'failure_reason' => $failureReason
            ]);

            // You can store this information in database if needed
            // For now, we'll just log it

            return response()->json(['status' => 'success'], 200);

        } catch (\Exception $e) {
            Log::error('SMS Callback Error: ' . $e->getMessage());
            return response()->json(['status' => 'error'], 500);
        }
    }

    /**
     * Handle incoming SMS messages from Africa's Talking
     */
    public function handleIncomingSMS(Request $request)
    {
        try {
            // Log the incoming SMS
            Log::info('Incoming SMS Received', [
                'data' => $request->all(),
                'headers' => $request->headers->all()
            ]);

            // Extract SMS data
            $from = $request->input('from');
            $to = $request->input('to');
            $text = $request->input('text');
            $date = $request->input('date');
            $id = $request->input('id');
            $linkId = $request->input('linkId');

            // Log specific SMS information
            Log::info('Incoming SMS Details', [
                'from' => $from,
                'to' => $to,
                'text' => $text,
                'date' => $date,
                'id' => $id,
                'linkId' => $linkId
            ]);

            // You can process the incoming SMS here
            // For example, auto-reply or store in database

            return response()->json(['status' => 'success'], 200);

        } catch (\Exception $e) {
            Log::error('Incoming SMS Error: ' . $e->getMessage());
            return response()->json(['status' => 'error'], 500);
        }
    }
}
