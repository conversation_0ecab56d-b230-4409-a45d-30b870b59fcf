<?php

namespace App\Events;

use App\Models\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;

class UserNotification implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets;

    public $user;
    public $type;
    public $title;
    public $message;
    public $data;

    public function __construct(User $user, string $type, string $title, string $message, array $data = [])
    {
        $this->user = $user;
        $this->type = $type;
        $this->title = $title;
        $this->message = $message;
        $this->data = $data;
    }

    public function broadcastOn()
    {
        return [
            new Channel('user.' . $this->user->id),
        ];
    }

    public function broadcastWith()
    {
        return [
            'notification' => [
                'type' => $this->type,
                'title' => $this->title,
                'message' => $this->message,
                'data' => $this->data,
                'timestamp' => now()->toDateTimeString(),
            ],
        ];
    }
}
