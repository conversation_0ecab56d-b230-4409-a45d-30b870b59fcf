<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add custom_id to receipts table
        Schema::table('receipts', function (Blueprint $table) {
            $table->string('custom_id')->unique()->nullable()->after('id');
            $table->index('custom_id');
        });

        // Add custom_id to audit_logs table
        Schema::table('audit_logs', function (Blueprint $table) {
            $table->string('custom_id')->unique()->nullable()->after('id');
            $table->index('custom_id');
        });

        // Add custom_id to otps table
        Schema::table('otps', function (Blueprint $table) {
            $table->string('custom_id')->unique()->nullable()->after('id');
            $table->index('custom_id');
        });

        // Add custom_id to sms_logs table
        Schema::table('sms_logs', function (Blueprint $table) {
            $table->string('custom_id')->unique()->nullable()->after('id');
            $table->index('custom_id');
        });

        // Generate custom IDs for existing records
        $this->generateCustomIds('receipts', 'RCP');
        $this->generateCustomIds('audit_logs', 'AUD');
        $this->generateCustomIds('otps', 'OTP');
        $this->generateCustomIds('sms_logs', 'SMS');

        // Make custom_id not nullable after populating
        Schema::table('receipts', function (Blueprint $table) {
            $table->string('custom_id')->nullable(false)->change();
        });

        Schema::table('audit_logs', function (Blueprint $table) {
            $table->string('custom_id')->nullable(false)->change();
        });

        Schema::table('otps', function (Blueprint $table) {
            $table->string('custom_id')->nullable(false)->change();
        });

        Schema::table('sms_logs', function (Blueprint $table) {
            $table->string('custom_id')->nullable(false)->change();
        });
    }

    /**
     * Generate custom IDs for a table
     */
    private function generateCustomIds(string $table, string $prefix): void
    {
        $records = DB::table($table)->whereNull('custom_id')->get();
        
        foreach ($records as $record) {
            $timestamp = now()->format('ymd');
            $random = strtoupper(Str::random(6));
            
            do {
                $customId = "{$prefix}-{$timestamp}-{$random}";
                $exists = DB::table($table)->where('custom_id', $customId)->exists();
                if ($exists) {
                    $random = strtoupper(Str::random(6));
                }
            } while ($exists);
            
            DB::table($table)->where('id', $record->id)->update(['custom_id' => $customId]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $tables = ['receipts', 'audit_logs', 'otps', 'sms_logs'];
        
        foreach ($tables as $table) {
            Schema::table($table, function (Blueprint $table) {
                $table->dropIndex(['custom_id']);
                $table->dropColumn('custom_id');
            });
        }
    }
};
