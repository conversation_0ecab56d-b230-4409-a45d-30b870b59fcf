@extends('layouts.app')

@section('title', 'Send Message')
@section('page-title', 'Send Message')

@section('breadcrumbs')
    <li>
        <span class="mx-2">/</span>
        <a href="{{ route('messages.index') }}" class="hover:text-gray-700">Messages</a>
    </li>
    <li>
        <span class="mx-2">/</span>
        <span class="font-medium text-gray-900">Send</span>
    </li>
@endsection

@section('content')
<div class="min-h-screen bg-gray-50 py-8" x-data="{
    messageType: 'individual',
    recipientType: 'individual',
    selectedRole: '',
    selectedLevel: '',
    selectedChurch: ''
}">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Send Message</h1>
                    <p class="mt-2 text-sm text-gray-600">Send messages, group communications, or announcements</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="{{ route('messages.index') }}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Messages
                    </a>
                </div>
            </div>
        </div>

        <!-- Message Type Selection -->
        <div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden mb-8">
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-paper-plane mr-3 text-blue-600"></i>
                    Message Type
                </h3>
                <p class="mt-1 text-sm text-gray-600">Choose the type of message you want to send</p>
            </div>

            <div class="p-6">
                <!-- Debug info -->
                <div class="mb-4 p-2 bg-gray-100 rounded text-sm">
                    Current selection: <span x-text="messageType"></span>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Individual Message -->
                    <div class="relative">
                        <input type="radio"
                               id="individual"
                               name="message_type_display"
                               value="individual"
                               x-model="messageType"
                               class="sr-only">
                        <label for="individual"
                               class="flex flex-col items-center p-6 border-2 rounded-lg cursor-pointer transition-all duration-200"
                               :class="messageType === 'individual' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'"
                               @click="messageType = 'individual'">
                            <div class="w-12 h-12 rounded-full flex items-center justify-center mb-3"
                                 :class="messageType === 'individual' ? 'bg-blue-100' : 'bg-gray-100'">
                                <i class="fas fa-user text-xl"
                                   :class="messageType === 'individual' ? 'text-blue-600' : 'text-gray-600'"></i>
                            </div>
                            <h4 class="font-medium text-gray-900 mb-1">Individual Message</h4>
                            <p class="text-sm text-gray-600 text-center">Send a message to specific users</p>
                        </label>
                    </div>

                    <!-- Group Message -->
                    <div class="relative">
                        <input type="radio"
                               id="group"
                               name="message_type_display"
                               value="group"
                               x-model="messageType"
                               class="sr-only">
                        <label for="group"
                               class="flex flex-col items-center p-6 border-2 rounded-lg cursor-pointer transition-all duration-200"
                               :class="messageType === 'group' ? 'border-green-500 bg-green-50' : 'border-gray-200 hover:border-gray-300'"
                               @click="messageType = 'group'">
                            <div class="w-12 h-12 rounded-full flex items-center justify-center mb-3"
                                 :class="messageType === 'group' ? 'bg-green-100' : 'bg-gray-100'">
                                <i class="fas fa-users text-xl"
                                   :class="messageType === 'group' ? 'text-green-600' : 'text-gray-600'"></i>
                            </div>
                            <h4 class="font-medium text-gray-900 mb-1">Group Message</h4>
                            <p class="text-sm text-gray-600 text-center">Send to all members of a church</p>
                        </label>
                    </div>

                    <!-- Announcement -->
                    <div class="relative">
                        <input type="radio"
                               id="announcement"
                               name="message_type_display"
                               value="announcement"
                               x-model="messageType"
                               class="sr-only">
                        <label for="announcement"
                               class="flex flex-col items-center p-6 border-2 rounded-lg cursor-pointer transition-all duration-200"
                               :class="messageType === 'announcement' ? 'border-purple-500 bg-purple-50' : 'border-gray-200 hover:border-gray-300'"
                               @click="messageType = 'announcement'">
                            <div class="w-12 h-12 rounded-full flex items-center justify-center mb-3"
                                 :class="messageType === 'announcement' ? 'bg-purple-100' : 'bg-gray-100'">
                                <i class="fas fa-bullhorn text-xl"
                                   :class="messageType === 'announcement' ? 'text-purple-600' : 'text-gray-600'"></i>
                            </div>
                            <h4 class="font-medium text-gray-900 mb-1">Announcement</h4>
                            <p class="text-sm text-gray-600 text-center">Broadcast to church hierarchy</p>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Dynamic Form -->
            <form method="POST"
                  action="{{ route('messages.store') }}"
                  class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden mt-8">
            @csrf

            <!-- Form Header -->
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-edit mr-3"
                       :class="messageType === 'individual' ? 'text-blue-600' :
                               messageType === 'group' ? 'text-green-600' : 'text-purple-600'"></i>
                    <span x-text="messageType === 'individual' ? 'Individual Message' :
                                  messageType === 'group' ? 'Group Message' : 'Announcement'"></span>
                </h3>
                <p class="mt-1 text-sm text-gray-600"
                   x-text="messageType === 'individual' ? 'Send a message to specific users' :
                           messageType === 'group' ? 'Send to all members of a church' :
                           'Broadcast announcement to church hierarchy'"></p>
            </div>

            <div class="p-6 space-y-6">
                <!-- Recipient Type Selection -->
                <div x-show="messageType === 'individual'" x-transition>
                    <label class="block text-sm font-medium text-gray-700 mb-3">
                        Recipient Type <span class="text-red-500">*</span>
                    </label>
                    <div class="grid grid-cols-2 md:grid-cols-5 gap-3">
                        <label class="relative cursor-pointer">
                            <input type="radio" name="recipient_type" value="individual" x-model="recipientType" class="sr-only">
                            <div class="p-3 border-2 rounded-lg text-center transition-all duration-200"
                                 :class="recipientType === 'individual' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'">
                                <i class="fas fa-user text-lg mb-2" :class="recipientType === 'individual' ? 'text-blue-600' : 'text-gray-400'"></i>
                                <div class="text-sm font-medium">Individual</div>
                            </div>
                        </label>
                        <label class="relative cursor-pointer">
                            <input type="radio" name="recipient_type" value="church" x-model="recipientType" class="sr-only">
                            <div class="p-3 border-2 rounded-lg text-center transition-all duration-200"
                                 :class="recipientType === 'church' ? 'border-green-500 bg-green-50' : 'border-gray-200 hover:border-gray-300'">
                                <i class="fas fa-church text-lg mb-2" :class="recipientType === 'church' ? 'text-green-600' : 'text-gray-400'"></i>
                                <div class="text-sm font-medium">Church</div>
                            </div>
                        </label>
                        <label class="relative cursor-pointer">
                            <input type="radio" name="recipient_type" value="role" x-model="recipientType" class="sr-only">
                            <div class="p-3 border-2 rounded-lg text-center transition-all duration-200"
                                 :class="recipientType === 'role' ? 'border-purple-500 bg-purple-50' : 'border-gray-200 hover:border-gray-300'">
                                <i class="fas fa-users-cog text-lg mb-2" :class="recipientType === 'role' ? 'text-purple-600' : 'text-gray-400'"></i>
                                <div class="text-sm font-medium">All Roles</div>
                            </div>
                        </label>
                        <label class="relative cursor-pointer">
                            <input type="radio" name="recipient_type" value="role_at_level" x-model="recipientType" class="sr-only">
                            <div class="p-3 border-2 rounded-lg text-center transition-all duration-200"
                                 :class="recipientType === 'role_at_level' ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:border-gray-300'">
                                <i class="fas fa-user-tag text-lg mb-2" :class="recipientType === 'role_at_level' ? 'text-indigo-600' : 'text-gray-400'"></i>
                                <div class="text-sm font-medium">Role @ Level</div>
                            </div>
                        </label>
                        <label class="relative cursor-pointer">
                            <input type="radio" name="recipient_type" value="level" x-model="recipientType" class="sr-only">
                            <div class="p-3 border-2 rounded-lg text-center transition-all duration-200"
                                 :class="recipientType === 'level' ? 'border-orange-500 bg-orange-50' : 'border-gray-200 hover:border-gray-300'">
                                <i class="fas fa-layer-group text-lg mb-2" :class="recipientType === 'level' ? 'text-orange-600' : 'text-gray-400'"></i>
                                <div class="text-sm font-medium">By Level</div>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- Individual Message Recipients -->
                <div x-show="messageType === 'individual' && recipientType === 'individual'" x-transition>
                    <label for="recipient_ids" class="block text-sm font-medium text-gray-700 mb-2">
                        Select Recipients <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <select name="recipient_ids[]"
                                id="recipient_ids"
                                multiple
                                class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 @error('recipient_ids') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror"
                                size="6">
                            @foreach ($users as $user)
                                <option value="{{ $user->id }}">
                                    {{ $user->full_name }} ({{ $user->church->name ?? 'N/A' }})
                                </option>
                            @endforeach
                        </select>
                        <div class="absolute top-3 right-3 pointer-events-none">
                            <i class="fas fa-users text-gray-400"></i>
                        </div>
                    </div>
                    <p class="mt-2 text-sm text-gray-600">
                        <span class="font-medium">How to select:</span>
                        Click on a name to select it. Hold Ctrl/Cmd and click to select multiple recipients.
                        <span class="text-blue-600">Selected recipients will be highlighted.</span>
                    </p>
                    @error('recipient_ids')
                        <p class="mt-2 text-sm text-red-600 flex items-center">
                            <i class="fas fa-exclamation-circle mr-1"></i>
                            {{ $message }}
                        </p>
                    @enderror
                </div>

                <!-- Church Recipients -->
                <div x-show="messageType === 'individual' && recipientType === 'church'" x-transition>
                    <label for="church_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Select Church <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <select name="church_id" id="church_id_individual" x-model="selectedChurch"
                                class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200">
                            <option value="">Choose a church</option>
                            @foreach ($churches as $church)
                                <option value="{{ $church->id }}">
                                    {{ $church->name }} ({{ $church->level->value ?? 'Unknown' }})
                                </option>
                            @endforeach
                        </select>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="fas fa-church text-gray-400"></i>
                        </div>
                    </div>
                    <p class="mt-2 text-sm text-gray-600">Message will be sent to all members of the selected church</p>
                </div>

                <!-- Role-based Recipients -->
                <div x-show="messageType === 'individual' && recipientType === 'role'" x-transition>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="role_name" class="block text-sm font-medium text-gray-700 mb-2">
                                Select Role <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <select name="role_name" id="role_name" x-model="selectedRole"
                                        class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200">
                                    <option value="">Choose a role</option>
                                    @foreach ($roles as $role)
                                        <option value="{{ $role->name }}">{{ $role->name }}</option>
                                    @endforeach
                                </select>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-users-cog text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label for="level_church_id" class="block text-sm font-medium text-gray-700 mb-2">
                                Scope (Optional)
                            </label>
                            <div class="relative">
                                <select name="level_church_id" id="level_church_id"
                                        class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200">
                                    <option value="">All churches</option>
                                    @foreach ($churches as $church)
                                        <option value="{{ $church->id }}">
                                            {{ $church->name }} & descendants
                                        </option>
                                    @endforeach
                                </select>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-sitemap text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p class="mt-2 text-sm text-gray-600">Message will be sent to all users with the selected role (across all church levels)</p>
                </div>

                <!-- Role at Level Recipients -->
                <div x-show="messageType === 'individual' && recipientType === 'role_at_level'" x-transition>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="role_at_level_name" class="block text-sm font-medium text-gray-700 mb-2">
                                Select Role <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <select name="role_name" id="role_at_level_name" x-model="selectedRole"
                                        class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200">
                                    <option value="">Choose a role</option>
                                    @foreach ($roles as $role)
                                        <option value="{{ $role->name }}">{{ $role->name }}</option>
                                    @endforeach
                                </select>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-user-tag text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label for="role_at_level_church_level" class="block text-sm font-medium text-gray-700 mb-2">
                                At Level <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <select name="church_level" id="role_at_level_church_level" x-model="selectedLevel"
                                        class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200">
                                    <option value="">Choose a level</option>
                                    @foreach ($churchLevels as $level)
                                        <option value="{{ $level }}">{{ $level }}</option>
                                    @endforeach
                                </select>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-layer-group text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label for="role_at_level_scope" class="block text-sm font-medium text-gray-700 mb-2">
                                Scope (Optional)
                            </label>
                            <div class="relative">
                                <select name="level_church_id" id="role_at_level_scope"
                                        class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200">
                                    <option value="">All churches</option>
                                    @foreach ($churches as $church)
                                        <option value="{{ $church->id }}">
                                            Under {{ $church->name }}
                                        </option>
                                    @endforeach
                                </select>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-sitemap text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3 p-3 bg-indigo-50 border border-indigo-200 rounded-lg">
                        <h4 class="text-sm font-medium text-indigo-800 mb-2">Examples:</h4>
                        <ul class="text-sm text-indigo-700 space-y-1">
                            <li class="flex items-center">
                                <i class="fas fa-arrow-right mr-2"></i>
                                All Treasurers at Regional level
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-arrow-right mr-2"></i>
                                All Pastors at Local level
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-arrow-right mr-2"></i>
                                All IT staff at National level
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Level-based Recipients -->
                <div x-show="messageType === 'individual' && recipientType === 'level'" x-transition>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="church_level" class="block text-sm font-medium text-gray-700 mb-2">
                                Select Church Level <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <select name="church_level" id="church_level" x-model="selectedLevel"
                                        class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200">
                                    <option value="">Choose a level</option>
                                    @foreach ($churchLevels as $level)
                                        <option value="{{ $level }}">{{ $level }}</option>
                                    @endforeach
                                </select>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-layer-group text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label for="level_church_scope" class="block text-sm font-medium text-gray-700 mb-2">
                                Scope (Optional)
                            </label>
                            <div class="relative">
                                <select name="level_church_id" id="level_church_scope"
                                        class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200">
                                    <option value="">All churches</option>
                                    @foreach ($churches as $church)
                                        <option value="{{ $church->id }}">
                                            Under {{ $church->name }}
                                        </option>
                                    @endforeach
                                </select>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-sitemap text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p class="mt-2 text-sm text-gray-600">Message will be sent to all users in churches at the selected level</p>
                </div>

                <!-- Recipient Count Display -->
                <div id="recipient-count"></div>

                <!-- Group Message & Announcement Church Selection -->
                <div x-show="messageType === 'group' || messageType === 'announcement'" x-transition>
                    <label for="church_id" class="block text-sm font-medium text-gray-700 mb-2">
                        <span x-show="messageType === 'group'">Select Church</span>
                        <span x-show="messageType === 'announcement'">Select Church (and Hierarchy)</span>
                        <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <select name="church_id"
                                id="church_id"
                                class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 @error('church_id') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror">
                            <option value="">Choose a church</option>
                            @foreach ($churches as $church)
                                <option value="{{ $church->id }}" {{ old('church_id') == $church->id ? 'selected' : '' }}>
                                    {{ $church->name }} ({{ $church->level->value ?? 'Unknown' }})
                                </option>
                            @endforeach
                        </select>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="fas fa-church text-gray-400"></i>
                        </div>
                    </div>
                    <div x-show="messageType === 'group'">
                        <p class="mt-2 text-sm text-gray-600">Message will be sent to all members of the selected church</p>
                    </div>
                    <div x-show="messageType === 'announcement'">
                        <p class="mt-2 text-sm text-gray-600">Announcement will be sent to the selected church and all its sub-churches</p>
                    </div>
                    @error('church_id')
                        <p class="mt-2 text-sm text-red-600 flex items-center">
                            <i class="fas fa-exclamation-circle mr-1"></i>
                            {{ $message }}
                        </p>
                    @enderror
                </div>

                <!-- Message Content -->
                <div>
                    <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                        <span x-show="messageType === 'individual'">Message Content</span>
                        <span x-show="messageType === 'group'">Group Message Content</span>
                        <span x-show="messageType === 'announcement'">Announcement Content</span>
                        <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <textarea name="content"
                                  id="content"
                                  rows="6"
                                  class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 resize-none @error('content') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror"
                                  :placeholder="messageType === 'individual' ? 'Type your message here...' :
                                                messageType === 'group' ? 'Type your group message here...' :
                                                'Type your announcement here...'">{{ old('content') }}</textarea>
                        <div class="absolute bottom-3 right-3 pointer-events-none">
                            <i class="fas fa-comment text-gray-400"></i>
                        </div>
                    </div>
                    <div class="mt-2 flex justify-between items-center">
                        <p class="text-sm text-gray-600">
                            <span x-show="messageType === 'individual'">Write a clear and concise message</span>
                            <span x-show="messageType === 'group'">This message will be visible to all church members</span>
                            <span x-show="messageType === 'announcement'">This announcement will be broadcast widely</span>
                        </p>
                        <span class="text-xs text-gray-500" id="char-count">0 characters</span>
                    </div>
                    @error('content')
                        <p class="mt-2 text-sm text-red-600 flex items-center">
                            <i class="fas fa-exclamation-circle mr-1"></i>
                            {{ $message }}
                        </p>
                    @enderror
                </div>

                <!-- Hidden fields for message type -->
                <input type="hidden" name="is_group_message" :value="messageType === 'group' ? '1' : '0'">
                <input type="hidden" name="is_announcement" :value="messageType === 'announcement' ? '1' : '0'">

                <!-- Hidden field for recipient_type - will be set by JavaScript -->
                <input type="hidden" name="recipient_type" id="recipient_type_hidden" value="">

                <!-- Hidden fields for role-based messaging -->
                <input type="hidden" name="role_name" id="role_name_hidden" value="">
                <input type="hidden" name="church_level" id="church_level_hidden" value="">
                <input type="hidden" name="level_church_id" id="level_church_id_hidden" value="">
            </div>

            <!-- Form Actions -->
            <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center text-sm text-gray-500">
                        <i class="fas fa-info-circle mr-2"></i>
                        <span x-show="messageType === 'individual'">Message will be sent immediately to selected recipients</span>
                        <span x-show="messageType === 'group'">Group message will be sent to all church members</span>
                        <span x-show="messageType === 'announcement'">Announcement will be broadcast to church hierarchy</span>
                    </div>

                    <div class="flex items-center space-x-4">
                        <a href="{{ route('messages.index') }}"
                           class="inline-flex items-center px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                            <i class="fas fa-times mr-2"></i>
                            Cancel
                        </a>
                        <button type="submit" id="sendMessageBtn"
                                class="inline-flex items-center px-8 py-3 border border-transparent rounded-lg text-sm font-medium text-white shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5"
                                :class="messageType === 'individual' ? 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:ring-blue-500' :
                                        messageType === 'group' ? 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 focus:ring-green-500' :
                                        'bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 focus:ring-purple-500'"
                                style="focus:outline-none focus:ring-2 focus:ring-offset-2">
                            <i class="fas fa-paper-plane mr-2" id="sendIcon"></i>
                            <span id="sendText">
                                <span x-show="messageType === 'individual'">Send Message</span>
                                <span x-show="messageType === 'group'">Send Group Message</span>
                                <span x-show="messageType === 'announcement'">Send Announcement</span>
                            </span>
                        </button>
                    </div>
                </div>
            </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Character counter for message content
    document.addEventListener('DOMContentLoaded', function() {
        const textarea = document.getElementById('content');
        const charCount = document.getElementById('char-count');

        if (textarea && charCount) {
            textarea.addEventListener('input', function() {
                const count = this.value.length;
                charCount.textContent = count + ' characters';

                // Change color based on length
                if (count > 500) {
                    charCount.className = 'text-xs text-red-500';
                } else if (count > 300) {
                    charCount.className = 'text-xs text-yellow-500';
                } else {
                    charCount.className = 'text-xs text-gray-500';
                }
            });
        }
    });

    // Role statistics data
    const roleStats = @json($roleStats ?? []);

    // Function to update recipient count display
    function updateRecipientCount() {
        const recipientType = document.querySelector('input[name="recipient_type"]:checked')?.value;
        const countDisplay = document.getElementById('recipient-count');

        if (!countDisplay) return;

        let count = 0;
        let description = '';

        switch (recipientType) {
            case 'role':
                const roleName = document.getElementById('role_name')?.value;
                if (roleName && roleStats[roleName]) {
                    count = roleStats[roleName].total;
                    description = `${count} users with role "${roleName}" across all levels`;
                }
                break;

            case 'role_at_level':
                const roleAtLevelName = document.getElementById('role_at_level_name')?.value;
                const levelName = document.getElementById('role_at_level_church_level')?.value;
                if (roleAtLevelName && levelName && roleStats[roleAtLevelName] && roleStats[roleAtLevelName].levels[levelName]) {
                    count = roleStats[roleAtLevelName].levels[levelName];
                    description = `${count} users with role "${roleAtLevelName}" at ${levelName} level`;
                }
                break;
        }

        if (count > 0) {
            countDisplay.innerHTML = `
                <div class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-users text-blue-600 mr-2"></i>
                        <span class="text-sm font-medium text-blue-800">Recipients: ${description}</span>
                    </div>
                </div>
            `;
        } else {
            countDisplay.innerHTML = '';
        }
    }

    // Add event listeners for recipient count updates
    document.addEventListener('DOMContentLoaded', function() {
        // Listen for recipient type changes
        document.querySelectorAll('input[name="recipient_type"]').forEach(radio => {
            radio.addEventListener('change', updateRecipientCount);
        });

        // Listen for role/level selection changes
        ['role_name', 'role_at_level_name', 'role_at_level_church_level'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', updateRecipientCount);
            }
        });

        // Enhanced form submission feedback
        const messageForm = document.querySelector('form');
        const sendBtn = document.getElementById('sendMessageBtn');
        const sendIcon = document.getElementById('sendIcon');
        const sendText = document.getElementById('sendText');

        if (messageForm && sendBtn) {
            messageForm.addEventListener('submit', function(e) {
                console.log('Form submission started');
                console.log('Form action:', messageForm.action);
                console.log('Form method:', messageForm.method);

                // Check if form has required fields
                const recipientIds = messageForm.querySelector('select[name="recipient_ids[]"]');
                const content = messageForm.querySelector('textarea[name="content"]');

                if (recipientIds) {
                    const selectedValues = Array.from(recipientIds.selectedOptions).map(opt => opt.value);
                    console.log('Selected recipients:', selectedValues);
                    console.log('Selected recipients count:', selectedValues.length);
                    console.log('Recipient select element:', recipientIds);
                    console.log('All options:', Array.from(recipientIds.options).map(opt => ({value: opt.value, text: opt.text, selected: opt.selected})));
                }
                if (content) {
                    console.log('Message content:', content.value);
                }

                // Get current message type from Alpine.js
                const messageTypeElement = document.querySelector('[x-data]');
                let messageType = 'individual'; // default
                if (messageTypeElement && messageTypeElement._x_dataStack) {
                    messageType = messageTypeElement._x_dataStack[0].messageType || 'individual';
                }

                console.log('Current message type:', messageType);

                // Debug: Show all form data that will be submitted
                const formData = new FormData(messageForm);
                console.log('Form data to be submitted:');
                for (let [key, value] of formData.entries()) {
                    console.log(`  ${key}: ${value}`);
                }

                // Specifically check recipient_ids
                const recipientIdsValues = formData.getAll('recipient_ids[]');
                console.log('recipient_ids[] values:', recipientIdsValues);

                // If no recipient_ids are found, manually add them
                if (recipientIds && recipientIds.selectedOptions.length > 0 && recipientIdsValues.length === 0) {
                    console.log('⚠️ No recipient_ids in FormData, manually adding...');
                    Array.from(recipientIds.selectedOptions).forEach(option => {
                        formData.append('recipient_ids[]', option.value);
                    });
                    console.log('Updated recipient_ids:', formData.getAll('recipient_ids[]'));
                }

                // Set recipient_type based on current selections
                const recipientTypeHidden = document.getElementById('recipient_type_hidden');
                const selectedRecipientType = document.querySelector('input[name="recipient_type"]:checked');

                if (selectedRecipientType) {
                    recipientTypeHidden.value = selectedRecipientType.value;
                } else {
                    // Fallback: determine recipient_type based on message type and selections
                    if (messageType === 'individual') {
                        recipientTypeHidden.value = 'individual';
                    } else if (messageType === 'group' || messageType === 'announcement') {
                        recipientTypeHidden.value = 'church';
                    } else {
                        recipientTypeHidden.value = 'individual'; // default
                    }
                }

                console.log('Set recipient_type to:', recipientTypeHidden.value);

                // Set additional hidden fields based on recipient type
                const roleNameHidden = document.getElementById('role_name_hidden');
                const churchLevelHidden = document.getElementById('church_level_hidden');
                const levelChurchIdHidden = document.getElementById('level_church_id_hidden');

                // Clear all hidden fields first
                roleNameHidden.value = '';
                churchLevelHidden.value = '';
                levelChurchIdHidden.value = '';

                // Set values based on recipient type
                if (recipientTypeHidden.value === 'role') {
                    const roleSelect = document.getElementById('role_name');
                    if (roleSelect) roleNameHidden.value = roleSelect.value;
                } else if (recipientTypeHidden.value === 'level') {
                    const levelSelect = document.getElementById('church_level');
                    if (levelSelect) churchLevelHidden.value = levelSelect.value;
                } else if (recipientTypeHidden.value === 'role_at_level') {
                    const roleAtLevelSelect = document.getElementById('role_at_level_name');
                    const levelAtRoleSelect = document.getElementById('role_at_level_church_level');
                    if (roleAtLevelSelect) roleNameHidden.value = roleAtLevelSelect.value;
                    if (levelAtRoleSelect) churchLevelHidden.value = levelAtRoleSelect.value;
                }

                // Validate based on message type and recipient type
                let validationError = null;

                if (messageType === 'individual') {
                    // For individual messages, validate based on recipient type
                    const recipientType = recipientTypeHidden.value;

                    if (recipientType === 'individual') {
                        if (recipientIds && recipientIds.selectedOptions.length === 0) {
                            validationError = '❌ Please select at least one recipient';
                        }
                    } else if (recipientType === 'role') {
                        if (!roleNameHidden.value) {
                            validationError = '❌ Please select a role';
                        }
                    } else if (recipientType === 'level') {
                        if (!churchLevelHidden.value) {
                            validationError = '❌ Please select a church level';
                        }
                    } else if (recipientType === 'role_at_level') {
                        if (!roleNameHidden.value || !churchLevelHidden.value) {
                            validationError = '❌ Please select both role and church level';
                        }
                    }
                } else if (messageType === 'group' || messageType === 'announcement') {
                    // For group/announcement messages, check church selection
                    const churchSelect = messageForm.querySelector('select[name="church_id"]');
                    if (churchSelect && !churchSelect.value) {
                        validationError = '❌ Please select a church';
                    }
                }

                // Always check content
                if (content && content.value.trim() === '') {
                    validationError = '❌ Please enter a message';
                }

                // Ensure recipient_ids are properly set before submission
                if (messageType === 'individual' && recipientIds && recipientIds.selectedOptions.length > 0) {
                    // Remove any existing hidden recipient inputs
                    const existingHiddenInputs = messageForm.querySelectorAll('input[name="recipient_ids[]"][type="hidden"]');
                    existingHiddenInputs.forEach(input => input.remove());

                    // Add hidden inputs for each selected recipient
                    Array.from(recipientIds.selectedOptions).forEach(option => {
                        const hiddenInput = document.createElement('input');
                        hiddenInput.type = 'hidden';
                        hiddenInput.name = 'recipient_ids[]';
                        hiddenInput.value = option.value;
                        messageForm.appendChild(hiddenInput);
                    });

                    console.log('Added hidden inputs for recipients:', Array.from(recipientIds.selectedOptions).map(opt => opt.value));
                }

                // If validation fails, prevent submission
                if (validationError) {
                    e.preventDefault();
                    console.error('Validation failed:', validationError);
                    if (typeof showToast === 'function') {
                        showToast(validationError, 'error');
                    } else {
                        alert(validationError);
                    }
                    resetSendButton();
                    return false;
                }

                // Show loading state
                sendBtn.disabled = true;
                sendBtn.classList.add('opacity-75', 'cursor-not-allowed');
                sendIcon.className = 'fas fa-spinner fa-spin mr-2';
                sendText.innerHTML = '<span>Sending...</span>';

                // Show loading toast
                if (typeof showToast === 'function') {
                    showToast('📤 Sending your message...', 'info');
                }

                // If form validation fails, reset button
                setTimeout(() => {
                    if (messageForm.querySelector('.text-red-500')) {
                        resetSendButton();
                    }
                }, 100);
            });
        }

        function resetSendButton() {
            if (sendBtn) {
                sendBtn.disabled = false;
                sendBtn.classList.remove('opacity-75', 'cursor-not-allowed');
                sendIcon.className = 'fas fa-paper-plane mr-2';
                sendText.innerHTML = `
                    <span x-show="messageType === 'individual'">Send Message</span>
                    <span x-show="messageType === 'group'">Send Group Message</span>
                    <span x-show="messageType === 'announcement'">Send Announcement</span>
                `;
            }
        }

        // Add visual feedback for recipient selection
        const recipientSelect = document.getElementById('recipient_ids');
        if (recipientSelect) {
            recipientSelect.addEventListener('change', function() {
                const selectedCount = this.selectedOptions.length;
                const helpText = this.parentElement.parentElement.querySelector('.text-gray-600');
                if (helpText) {
                    if (selectedCount > 0) {
                        helpText.innerHTML = `
                            <span class="font-medium text-green-600">✓ ${selectedCount} recipient(s) selected</span><br>
                            <span class="text-sm">Hold Ctrl/Cmd and click to select multiple recipients.</span>
                        `;
                    } else {
                        helpText.innerHTML = `
                            <span class="font-medium">How to select:</span>
                            Click on a name to select it. Hold Ctrl/Cmd and click to select multiple recipients.
                            <span class="text-blue-600">Selected recipients will be highlighted.</span>
                        `;
                    }
                }

                // Debug log
                console.log('Recipients selection changed:', Array.from(this.selectedOptions).map(opt => opt.text));
            });
        }
    });
</script>
@endpush
@endsection