<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'hudumasms' => [
        'api_token' => env('HUDUMASMS_API_TOKEN'),
        'sender_id' => env('HUDUMASMS_SENDER_ID', 'FPCT'),
        'base_url' => env('HUDUMASMS_BASE_URL', 'https://sms-api.huduma.cloud/api/v3'),
        'callback_url' => env('HUDUMASMS_CALLBACK_URL'),
    ],

    'azampay' => [
        'base_url' => env('AZAMPAY_BASE_URL', 'https://sandbox.azampay.co.tz'),
        'app_name' => env('AZAMPAY_APP_NAME', 'FPCT'),
        'client_id' => env('AZAMPAY_CLIENT_ID'),
        'client_secret' => env('AZAMPAY_CLIENT_SECRET'),
        'api_key' => env('AZAMPAY_API_KEY'),
        'callback_url' => env('AZAMPAY_CALLBACK_URL'),
    ],

];
