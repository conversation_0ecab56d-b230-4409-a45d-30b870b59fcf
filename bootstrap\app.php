<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        // Register Spatie Permission middleware
        $middleware->alias([
            'role' => \Spatie\Permission\Middleware\RoleMiddleware::class,
            'permission' => \Spatie\Permission\Middleware\PermissionMiddleware::class,
            'role_or_permission' => \Spatie\Permission\Middleware\RoleOrPermissionMiddleware::class,
            'locale' => \App\Http\Middleware\SetLocale::class,
            'hierarchy' => \App\Http\Middleware\CheckHierarchicalPermissions::class,
            'church.hierarchy' => \App\Http\Middleware\CheckChurchHierarchy::class,
            'session.timeout' => \App\Http\Middleware\SessionTimeout::class,
            'prevent.back' => \App\Http\Middleware\PreventBackHistory::class,
            'intended.redirect' => \App\Http\Middleware\HandleIntendedRedirect::class,
            'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        ]);

        // Apply locale and session timeout middleware globally
        $middleware->web(append: [
            \App\Http\Middleware\SetLocale::class,
            \App\Http\Middleware\SessionTimeout::class,
            \App\Http\Middleware\PreventBackHistory::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
