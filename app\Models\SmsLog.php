<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Traits\HasCustomId;

class SmsLog extends Model
{
    use HasCustomId;

    protected $fillable = [
        'user_id',
        'phone_number',
        'message',
        'type',
        'status',
        'provider',
        'provider_message_id',
        'third_party_ref',
        'credits_used',
        'provider_response',
        'sent_at',
        'delivered_at',
        'failed_at',
        'error_message',
        'custom_id',
    ];

    protected $casts = [
        'provider_response' => 'array',
        'sent_at' => 'datetime',
        'delivered_at' => 'datetime',
        'failed_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByPhoneNumber($query, string $phoneNumber)
    {
        return $query->where('phone_number', $phoneNumber);
    }

    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    // Helper methods
    public function markAsSent(?string $providerMessageId = null, ?array $providerResponse = null): void
    {
        $this->update([
            'status' => 'sent',
            'provider_message_id' => $providerMessageId,
            'provider_response' => $providerResponse,
            'sent_at' => now(),
        ]);
    }

    public function markAsDelivered(): void
    {
        $this->update([
            'status' => 'delivered',
            'delivered_at' => now(),
        ]);
    }

    public function markAsFailed(?string $errorMessage = null): void
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
            'failed_at' => now(),
        ]);
    }

    public function isSuccessful(): bool
    {
        return in_array($this->status, ['sent', 'delivered']);
    }

    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function hasFailed(): bool
    {
        return in_array($this->status, ['failed', 'undeliverable']);
    }

    /**
     * Get the custom ID prefix for SmsLog model
     */
    protected function getCustomIdPrefix(): string
    {
        return 'SMS';
    }
}
