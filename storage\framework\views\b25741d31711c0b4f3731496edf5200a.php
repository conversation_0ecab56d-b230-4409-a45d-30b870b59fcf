

<?php $__env->startSection('title', __('common.dashboard') . ' - ' . __('common.fpct_system')); ?>
<?php $__env->startSection('page-title', __('common.dashboard')); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Welcome Header -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900"><?php echo e(__('common.welcome_back')); ?>, <?php echo e(Auth::user()?->full_name ?? 'User'); ?>!</h1>
                    <p class="mt-1 text-sm text-gray-600">
                        <?php echo e(Auth::user()?->church?->name ?? __('common.no_church_assigned')); ?> • <?php echo e(Auth::user()?->church?->level ? __('common.' . strtolower(Auth::user()->church->level->value)) : __('common.unknown')); ?> <?php echo e(__('common.level')); ?>

                        <?php if(Auth::user() && count(Auth::user()->getRoleNames()) > 0): ?>
                            • <?php echo e(Auth::user()->getRoleNames()[0]); ?>

                        <?php endif; ?>
                    </p>
                </div>
                <div class="flex-shrink-0">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-2xl text-blue-600"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Users -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-users text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate"><?php echo e(__('common.total_users')); ?></dt>
                            <dd class="text-lg font-medium text-gray-900"><?php echo e($statistics['total_users'] ?? 0); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Users -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-user-check text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate"><?php echo e(__('common.active_users')); ?></dt>
                            <dd class="text-lg font-medium text-gray-900"><?php echo e($statistics['active_users'] ?? 0); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Requests -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-clock text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate"><?php echo e(__('common.pending_requests')); ?></dt>
                            <dd class="text-lg font-medium text-gray-900"><?php echo e($statistics['pending_requests'] ?? 0); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Unread Messages -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-envelope text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate"><?php echo e(__('common.unread_messages')); ?></dt>
                            <dd class="text-lg font-medium text-gray-900"><?php echo e($statistics['unread_messages'] ?? 0); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Church Information -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                    <i class="fas fa-church mr-2 text-blue-600"></i>
                    <?php echo e(__('common.church_information')); ?>

                </h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500"><?php echo e(__('common.name')); ?>:</span>
                        <span class="text-sm text-gray-900"><?php echo e(Auth::user()->church ? Auth::user()->church->name : __('common.no_church_assigned')); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500"><?php echo e(__('common.level')); ?>:</span>
                        <span class="text-sm text-gray-900">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <?php echo e(Auth::user()->church && Auth::user()->church->level ? __('common.' . strtolower(Auth::user()->church->level->value)) : __('common.unknown')); ?>

                            </span>
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500"><?php echo e(__('common.location')); ?>:</span>
                        <span class="text-sm text-gray-900"><?php echo e(Auth::user()->church ? Auth::user()->church->location : 'N/A'); ?></span>
                    </div>
                    <?php if(Auth::user()->church && (Auth::user()->church->district || Auth::user()->church->region)): ?>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500"><?php echo e(__('churches.district')); ?>/<?php echo e(__('churches.region')); ?>:</span>
                        <span class="text-sm text-gray-900">
                            <?php if(Auth::user()->church->district): ?><?php echo e(Auth::user()->church->district); ?><?php endif; ?>
                            <?php if(Auth::user()->church->district && Auth::user()->church->region): ?>, <?php endif; ?>
                            <?php if(Auth::user()->church->region): ?><?php echo e(Auth::user()->church->region); ?><?php endif; ?>
                        </span>
                    </div>
                    <?php endif; ?>
                    <?php if(Auth::user()->church && Auth::user()->church->phone_number): ?>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500"><?php echo e(__('churches.phone_number')); ?>:</span>
                        <span class="text-sm text-gray-900">
                            <a href="tel:<?php echo e(Auth::user()->church->phone_number); ?>" class="text-blue-600 hover:text-blue-800">
                                <?php echo e(Auth::user()->church->phone_number); ?>

                            </a>
                        </span>
                    </div>
                    <?php endif; ?>
                    <?php if(Auth::user()->church && Auth::user()->church->email): ?>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500"><?php echo e(__('churches.email')); ?>:</span>
                        <span class="text-sm text-gray-900">
                            <a href="mailto:<?php echo e(Auth::user()->church->email); ?>" class="text-blue-600 hover:text-blue-800">
                                <?php echo e(Auth::user()->church->email); ?>

                            </a>
                        </span>
                    </div>
                    <?php endif; ?>
                    <?php if(Auth::user()->church && Auth::user()->church->date_established): ?>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500"><?php echo e(__('common.date_established')); ?>:</span>
                        <span class="text-sm text-gray-900"><?php echo e(Auth::user()->church->date_established->format('M d, Y')); ?></span>
                    </div>
                    <?php endif; ?>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500"><?php echo e(__('common.your_role')); ?>:</span>
                        <span class="text-sm text-gray-900">
                            <?php if(count(Auth::user()->getRoleNames()) > 0): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <?php echo e(Auth::user()->getRoleNames()[0]); ?>

                                </span>
                            <?php else: ?>
                                <span class="text-gray-400"><?php echo e(__('common.no_role_assigned')); ?></span>
                            <?php endif; ?>
                        </span>
                    </div>
                </div>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-churches')): ?>
                <div class="mt-4">
                    <a href="<?php echo e(route('churches.show', Auth::user()->church)); ?>"
                       class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <?php echo e(__('common.view_church_details')); ?>

                        <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                    <i class="fas fa-bolt mr-2 text-yellow-600"></i>
                    <?php echo e(__('common.quick_actions')); ?>

                </h3>
                <div class="grid grid-cols-2 gap-3">
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('send-messages')): ?>
                    <a href="<?php echo e(route('messages.create')); ?>"
                       class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-envelope mr-2"></i>
                        <?php echo e(__('common.send_message')); ?>

                    </a>
                    <?php endif; ?>

                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create-requests')): ?>
                    <a href="<?php echo e(route('requests.create')); ?>"
                       class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        <i class="fas fa-plus mr-2"></i>
                        <?php echo e(__('common.create_request')); ?>

                    </a>
                    <?php endif; ?>

                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create-users')): ?>
                    <a href="<?php echo e(route('users.create')); ?>"
                       class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                        <i class="fas fa-user-plus mr-2"></i>
                        <?php echo e(__('common.add_user')); ?>

                    </a>
                    <?php endif; ?>

                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create-churches')): ?>
                    <a href="<?php echo e(route('churches.create')); ?>"
                       class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <i class="fas fa-church mr-2"></i>
                        <?php echo e(__('common.add_church')); ?>

                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Requests Section -->
    <?php if(isset($pending_requests) && count($pending_requests) > 0): ?>
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
                <i class="fas fa-clock mr-2 text-yellow-600"></i>
                <?php echo e(__('common.pending_approvals')); ?>

            </h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                <?php echo e(__('common.requests_waiting_approval')); ?>

            </p>
        </div>
        <ul class="divide-y divide-gray-200">
            <?php $__currentLoopData = $pending_requests; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $request): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li>
                <div class="px-4 py-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-file-alt text-yellow-600"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">
                                <?php echo e(__('common.' . $request->type)); ?>

                            </div>
                            <div class="text-sm text-gray-500">
                                <?php echo e($request->church->name); ?> • <?php echo e(__('common.requested_by')); ?> <?php echo e($request->user->full_name); ?>

                            </div>
                            <div class="text-xs text-gray-400">
                                <?php echo e($request->created_at->diffForHumans()); ?>

                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <a href="<?php echo e(route('requests.show', $request)); ?>"
                           class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200">
                            <?php echo e(__('common.review')); ?>

                        </a>
                    </div>
                </div>
            </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
        <div class="bg-gray-50 px-4 py-3 text-right">
            <a href="<?php echo e(route('requests.index')); ?>"
               class="text-sm font-medium text-blue-600 hover:text-blue-500">
                <?php echo e(__('common.view_all_requests')); ?>

                <i class="fas fa-arrow-right ml-1"></i>
            </a>
        </div>
    </div>
    <?php endif; ?>

    <!-- Recent Messages Section -->
    <?php if(isset($recent_messages) && count($recent_messages) > 0): ?>
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
                <i class="fas fa-envelope mr-2 text-blue-600"></i>
                <?php echo e(__('common.recent_messages')); ?>

            </h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                <?php echo e(__('common.latest_messages_community')); ?>

            </p>
        </div>
        <ul class="divide-y divide-gray-200">
            <?php $__currentLoopData = $recent_messages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li>
                <div class="px-4 py-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                <span class="text-sm font-medium text-blue-600">
                                    <?php echo e(substr($message->sender->full_name, 0, 1)); ?>

                                </span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">
                                <?php echo e($message->sender->full_name); ?>

                            </div>
                            <div class="text-sm text-gray-500">
                                <?php echo e(Str::limit($message->content, 80)); ?>

                            </div>
                            <div class="text-xs text-gray-400">
                                <?php echo e($message->created_at ? $message->created_at->diffForHumans() : __('common.unknown')); ?>

                            </div>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <?php if($message->is_announcement): ?>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mr-2">
                                <i class="fas fa-bullhorn mr-1"></i>
                                <?php echo e(__('common.announcement')); ?>

                            </span>
                        <?php endif; ?>
                        <a href="<?php echo e(route('messages.show', $message)); ?>"
                           class="text-blue-600 hover:text-blue-500">
                            <i class="fas fa-eye"></i>
                        </a>
                    </div>
                </div>
            </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
        <div class="bg-gray-50 px-4 py-3 text-right">
            <a href="<?php echo e(route('messages.index')); ?>"
               class="text-sm font-medium text-blue-600 hover:text-blue-500">
                <?php echo e(__('common.view_all_messages')); ?>

                <i class="fas fa-arrow-right ml-1"></i>
            </a>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\fpct-system\resources\views/dashboard/index.blade.php ENDPATH**/ ?>