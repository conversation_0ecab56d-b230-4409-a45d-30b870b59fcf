<?php

return [
    // Page titles
    'index_title' => 'Churches - FPCT Church Management System (CMS)',
    'create_title' => 'Add New Church - FPCT Church Management System (CMS)',
    'edit_title' => 'Edit Church - FPCT Church Management System (CMS)',
    'show_title' => 'Church Details - FPCT Church Management System (CMS)',
    'trashed_title' => 'Trashed Churches - FPCT Church Management System (CMS)',
    
    // Page headers
    'church_management' => 'Church Management',
    'add_new_church' => 'Add New Church',
    'edit_church' => 'Edit Church',
    'church_details' => 'Church Details',
    'trashed_churches' => 'Trashed Churches',
    'manage_churches_hierarchy' => 'Manage churches in the FPCT hierarchy',
    
    // Form fields
    'church_name' => 'Church Name',
    'church_level' => 'Church Level',
    'location' => 'Location',
    'phone_number' => 'Phone Number',
    'address' => 'Address',
    'email' => 'Email Address',
    'district' => 'District',
    'region' => 'Region',
    'date_established' => 'Date Established',
    'parent_church' => 'Parent Church',
    'select_parent_church' => 'Select Parent Church',
    'no_parent_church' => 'No Parent Church (Top Level)',
    'loading_parent_churches' => 'Loading parent churches...',
    'error_loading_churches' => 'Error loading churches',
    'select_level' => 'Select Church Level',
    'enter_church_name' => 'Enter church name',
    'enter_location' => 'Enter location',
    'enter_phone_number' => 'Enter phone number',
    'enter_address' => 'Enter address',
    'enter_email' => 'Enter email address',
    'enter_district' => 'Enter district',
    'enter_region' => 'Enter region',
    'select_date' => 'Select date',
    
    // Church levels
    'national' => 'National',
    'regional' => 'Regional',
    'local' => 'Local',
    'parish' => 'Parish',
    'branch' => 'Branch',
    
    // Actions
    'add_church' => 'Add Church',

    // Contextual Creation Labels
    'create_national_church' => 'Create National Church',
    'create_regional_church' => 'Create Regional Church',
    'create_local_church' => 'Create Local Church',
    'create_parish_church' => 'Create Parish Church',
    'create_branch_church' => 'Create Branch Church',
    'create_national' => 'Create National',
    'create_regional' => 'Create Regional',
    'create_local' => 'Create Local',
    'create_parish' => 'Create Parish',
    'create_branch' => 'Create Branch',

    // Demographics
    'demographics' => 'Demographics',
    'church_demographics_description' => 'Enter the number of members in each age group',
    'youth_count' => 'Youth',
    'young_adults_count' => 'Young Adults',
    'children_count' => 'Children',
    'elders_count' => 'Elders',
    'age_0_12' => '0-12 years',
    'age_13_17' => '13-17 years',
    'age_18_35' => '18-35 years',
    'age_36_plus' => '36+ years',
    'demographic_statistics' => 'Demographic Statistics',
    'age_group_breakdown' => 'Age group breakdown of church members',
    'total_demographic_count' => 'Total Demographic Count',
    'members' => 'members',
    'demographic_note_title' => 'Demographic Information',
    'demographic_note_description' => 'These numbers help track the age distribution of church members for better ministry planning and resource allocation.',

    // Church Reports
    'church_reports' => 'Church Reports',
    'generate_reports_description' => 'Generate detailed reports for different church levels',
    'available_levels' => 'Available Church Levels',
    'churches_available' => 'churches available',
    'generate_report' => 'Generate Report',
    'quick_statistics' => 'Quick Statistics',
    'total_demographics' => 'Total Demographics',
    'active_levels' => 'Active Levels',
    'report_format' => 'Report Format',
    'view_online' => 'View Online',
    'download_pdf' => 'Download PDF',
    'download_excel' => 'Download Excel',
    'report_options' => 'Report Options',
    'include_demographics' => 'Include Demographics',
    'include_leadership' => 'Include Leadership',
    'include_statistics' => 'Include Statistics',
    'include_contact_info' => 'Include Contact Information',
    'include_user_details' => 'Include User Details',
    'include_establishment_info' => 'Include Establishment Information',
    'generate' => 'Generate',
    'churches_report' => 'Churches Report',
    'generated_on' => 'Generated on',
    'by' => 'by',
    'back_to_reports' => 'Back to Reports',
    'export' => 'Export',
    'export_pdf' => 'Export as PDF',
    'export_excel' => 'Export as Excel',
    'summary_statistics' => 'Summary Statistics',
    'active_users' => 'Active Users',
    'demographics_breakdown' => 'Demographics Breakdown',
    'detailed_information_churches' => 'Detailed information about churches',
    'hierarchy' => 'Hierarchy',

    // Hierarchical Navigation
    'hierarchical_navigation' => 'Hierarchical Navigation',
    'view_regions' => 'View Regions',
    'view_local' => 'View Local Churches',
    'view_parishes' => 'View Parishes',
    'view_branches' => 'View Branches',
    'view_children' => 'View Children',
    'sub_churches' => 'Sub Churches',
    'leaders_auto_assigned' => 'Leaders are automatically assigned based on user roles',
    'leaders_based_on_roles' => 'Users with leadership roles in this church will appear here automatically',
    'leader' => 'Leader',
    'children' => 'Children',
    'youth' => 'Youth',
    'young_adults' => 'Young Adults',
    'elders' => 'Elders',
    'total_users' => 'Total Users',
    'active_users' => 'Active Users',
    'establishment_info' => 'Establishment Info',
    'age' => 'Age',
    'establishment_date_unknown' => 'Establishment date unknown',
    'contact_summary' => 'Contact Information Summary',
    'with_email' => 'With Email',
    'with_phone' => 'With Phone',
    'with_address' => 'With Address',
    'total_contact_points' => 'Total Contact Points',
    'establishment_summary' => 'Establishment Summary',
    'with_establishment_date' => 'With Establishment Date',
    'oldest_church' => 'Oldest Church',
    'average_age_years' => 'Average Age (Years)',
    'user_details_summary' => 'User Details Summary',
    'users_with_phone' => 'Users with Phone',
    'complete_profiles' => 'Complete Profiles',
    'role_distribution' => 'Role Distribution',
    'save_church' => 'Save Church',
    'update_church' => 'Update Church',
    'delete_church' => 'Delete Church',
    'restore_church' => 'Restore Church',
    'permanently_delete' => 'Permanently Delete',
    'view_details' => 'View Details',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'restore' => 'Restore',
    
    // Table headers
    'name' => 'Name',
    'level' => 'Level',
    'location_header' => 'Location',
    'parent' => 'Parent',
    'established' => 'Established',
    'actions' => 'Actions',
    'deleted_at' => 'Deleted At',
    
    // Statistics
    'total_churches' => 'Total Churches',
    'by_level' => 'By Level',
    'recently_added' => 'Recently Added',
    'church_hierarchy' => 'Church Hierarchy',
    
    // Messages
    'church_created' => 'Church created successfully',
    'church_updated' => 'Church updated successfully',
    'church_deleted' => 'Church deleted successfully',
    'church_restored' => 'Church restored successfully',
    'church_permanently_deleted' => 'Church permanently deleted',
    'no_churches_found' => 'No churches found',
    'no_trashed_churches' => 'No trashed churches found',
    
    // Confirmations
    'confirm_delete' => 'Are you sure you want to delete this church?',
    'confirm_restore' => 'Are you sure you want to restore this church?',
    'confirm_permanent_delete' => 'Are you sure you want to permanently delete this church? This action cannot be undone.',
    'delete_warning' => 'This will move the church to trash. You can restore it later.',
    'permanent_delete_warning' => 'This will permanently delete the church and all associated data.',
    
    // Filters
    'all_churches' => 'All Churches',
    'all_levels' => 'All Levels',
    'all_parents' => 'All Parents',
    'filter_by_level' => 'Filter by Level',
    'filter_by_parent' => 'Filter by Parent',
    'search_churches' => 'Search by name, location, contact info...',
    'show_trashed' => 'Show Trashed',
    'hide_trashed' => 'Hide Trashed',
    
    // Details
    'church_information' => 'Church Information',
    'hierarchy_information' => 'Hierarchy Information',
    'child_churches' => 'Child Churches',
    'no_child_churches' => 'No child churches',
    'members_count' => 'Members',
    'established_on' => 'Established on',
    'under_church' => 'Under',
    'direct_children' => 'Direct Children',
    'total_descendants' => 'Total Descendants',
    
    // Validation
    'name_required' => 'Church name is required',
    'level_required' => 'Church level is required',
    'location_required' => 'Location is required',
    'parent_required' => 'Parent church is required for this level',
    'invalid_parent' => 'Invalid parent church selected',
    'name_unique' => 'A church with this name already exists',
    'date_invalid' => 'Please enter a valid date',
    
    // Breadcrumbs
    'churches' => 'Churches',
    'add' => 'Add',
    'edit_breadcrumb' => 'Edit',
    'details' => 'Details',
    'trash' => 'Trash',

    // Additional translations
    'churches_list' => 'Churches List',
    'total_churches_count' => 'total churches',
    'church' => 'Church',
    'level_hierarchy' => 'Level & Hierarchy',
    'statistics' => 'Statistics',
    'status' => 'Status',
    'est' => 'Est.',
    'under' => 'Under',
    'trashed' => 'Trashed',
    'view_church' => 'View Church',
    'edit_church_action' => 'Edit Church',
    'delete_church_action' => 'Delete Church',
    'no_churches_match_filters' => 'No churches match your current filters.',
    'get_started_first_church' => 'Get started by adding your first church.',

    // Create/Edit specific
    'add_church_description' => 'Add a new church to the FPCT Church Management System (CMS) with leadership details',
    'back_to_churches' => 'Back to Churches',
    'basic_church_details' => 'Basic details about the church',
    'church_leadership' => 'Church Leadership',
    'assign_leaders_positions' => 'Assign leaders and their positions in the church',
    'select_leader' => 'Select Leader',
    'choose_user' => 'Choose a user',
    'position_role' => 'Position/Role',
    'position_placeholder' => 'e.g., Pastor, Secretary, Treasurer',
    'add_another_leader' => 'Add Another Leader',
    'fields_required_note' => 'Fields marked with * are required',
    'create_church' => 'Create Church',
    'remove' => 'Remove',

    // Church Details/Statistics
    'church_id' => 'Church ID',
    'total_members' => 'Total Members',
    'leaders' => 'Leaders',
    'leadership_positions' => 'Leadership Positions',
    'under_supervision' => 'Under Supervision',
    'basic_information' => 'Basic Information',
    'church_leaders' => 'Church Leaders',
    'leaders_auto_assigned' => 'Leaders are automatically assigned based on user roles in this church',
    'leaders_based_on_roles' => 'Leaders will appear here when users are assigned roles in this church',
    'church_members' => 'Church Members',
    'no_leaders_assigned' => 'No leaders assigned',
    'assign_leaders_description' => 'Click edit to assign leaders to this church',
    'assign_leaders' => 'Assign Leaders',
    'no_members' => 'No members yet',
    'and_more_members' => 'and :count more members',
    'click_add_leader_to_start' => 'Click "Add Leader" to start assigning leadership positions',
    'add_first_leader' => 'Add First Leader',
    'editing_church' => 'Editing Church',
    'enter_church_name' => 'Enter church name',
    'enter_location' => 'Enter location',
    'enter_position' => 'Enter position (e.g., Pastor)',
    'church_member' => 'Church Member',
    'add_leader' => 'Add Leader',
    'position' => 'Position',
    'update_church' => 'Update Church',
];
