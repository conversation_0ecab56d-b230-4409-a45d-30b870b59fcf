<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class NotificationPreference extends Model
{
    protected $fillable = [
        'user_id',
        'email_notifications',
        'sms_notifications',
        'push_notifications',
        'message_notifications',
        'announcement_notifications',
        'request_notifications',
        'approval_notifications',
        'system_notifications',
    ];

    protected $casts = [
        'email_notifications' => 'boolean',
        'sms_notifications' => 'boolean',
        'push_notifications' => 'boolean',
        'message_notifications' => 'boolean',
        'announcement_notifications' => 'boolean',
        'request_notifications' => 'boolean',
        'approval_notifications' => 'boolean',
        'system_notifications' => 'boolean',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function shouldSendEmail(string $type): bool
    {
        return $this->email_notifications && $this->shouldSendNotification($type);
    }

    public function shouldSendSMS(string $type): bool
    {
        return $this->sms_notifications && $this->shouldSendNotification($type);
    }

    public function shouldSendPush(string $type): bool
    {
        return $this->push_notifications && $this->shouldSendNotification($type);
    }

    private function shouldSendNotification(string $type): bool
    {
        return match ($type) {
            'message' => $this->message_notifications,
            'announcement' => $this->announcement_notifications,
            'request' => $this->request_notifications,
            'approval' => $this->approval_notifications,
            'system' => $this->system_notifications,
            default => false,
        };
    }

    public static function getDefaultPreferences(): array
    {
        return [
            'email_notifications' => true,
            'sms_notifications' => true,
            'push_notifications' => true,
            'message_notifications' => true,
            'announcement_notifications' => true,
            'request_notifications' => true,
            'approval_notifications' => true,
            'system_notifications' => true,
        ];
    }
}
