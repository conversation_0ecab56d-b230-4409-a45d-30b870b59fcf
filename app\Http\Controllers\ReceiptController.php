<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Models\Receipt;
use App\Models\Transaction;
use App\Services\ReceiptService;
use App\Services\ChurchHierarchyService;

class ReceiptController extends Controller
{
    protected ReceiptService $receiptService;
    protected ChurchHierarchyService $hierarchyService;

    public function __construct(ReceiptService $receiptService, ChurchHierarchyService $hierarchyService)
    {
        $this->receiptService = $receiptService;
        $this->hierarchyService = $hierarchyService;
        $this->middleware('auth');
        $this->middleware('permission:view-receipts', ['only' => ['index', 'show']]);
        $this->middleware('permission:generate-receipts', ['only' => ['create', 'store', 'generateForTransaction']]);
    }

    /**
     * Display a listing of receipts
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $church = $user->church;

        $query = Receipt::where(function ($q) use ($church) {
            $q->where('issued_by_church_id', $church->id)
              ->orWhere('issued_to_church_id', $church->id);
        })->with(['transaction', 'issuedToChurch', 'issuedByChurch', 'issuedByUser']);

        // Apply filters
        if ($request->filled('receipt_type')) {
            $query->where('receipt_type', $request->receipt_type);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('receipt_number', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $receipts = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('financial.receipts.index', compact('receipts', 'church'));
    }

    /**
     * Show the form for creating a new receipt
     */
    public function create(Request $request)
    {
        $user = Auth::user();
        $church = $user->church;

        // Get transactions that don't have receipts yet
        $transactions = Transaction::where(function ($q) use ($church) {
            $q->where('from_church_id', $church->id)
              ->orWhere('to_church_id', $church->id);
        })
        ->where('status', 'completed')
        ->whereDoesntHave('receipt')
        ->with(['fromChurch', 'toChurch', 'contribution'])
        ->orderBy('completed_at', 'desc')
        ->get();

        return view('financial.receipts.create', compact('church', 'transactions'));
    }

    /**
     * Store a newly created receipt
     */
    public function store(Request $request)
    {
        $request->validate([
            'transaction_id' => 'required|exists:transactions,id',
            'description' => 'nullable|string|max:500',
            'email_addresses' => 'nullable|array',
            'email_addresses.*' => 'email',
        ]);

        $user = Auth::user();
        $transaction = Transaction::findOrFail($request->transaction_id);

        // Check if user can generate receipt for this transaction
        if (!$this->canGenerateReceipt($user, $transaction)) {
            abort(403, 'Unauthorized to generate receipt for this transaction.');
        }

        // Check if receipt already exists
        if ($transaction->receipt) {
            return redirect()->back()->with('error', 'Receipt already exists for this transaction.');
        }

        try {
            $receipt = $this->receiptService->generateReceipt($transaction, $user);

            // Email receipt if addresses provided
            if ($request->filled('email_addresses')) {
                $this->receiptService->emailReceipt($receipt, $request->email_addresses);
            }

            return redirect()->route('receipts.show', $receipt)
                ->with('success', 'Receipt generated successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to generate receipt: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified receipt
     */
    public function show(Receipt $receipt)
    {
        $user = Auth::user();

        if (!$this->canViewReceipt($user, $receipt)) {
            abort(403, 'Unauthorized to view this receipt.');
        }

        $receipt->load([
            'transaction.fromChurch',
            'transaction.toChurch',
            'transaction.contribution',
            'issuedToChurch',
            'issuedByChurch',
            'issuedByUser'
        ]);

        return view('financial.receipts.show', compact('receipt'));
    }

    /**
     * Generate receipt for a specific transaction
     */
    public function generateForTransaction(Transaction $transaction)
    {
        $user = Auth::user();

        if (!$this->canGenerateReceipt($user, $transaction)) {
            abort(403, 'Unauthorized to generate receipt for this transaction.');
        }

        if ($transaction->receipt) {
            return redirect()->route('receipts.show', $transaction->receipt)
                ->with('info', 'Receipt already exists for this transaction.');
        }

        try {
            $receipt = $this->receiptService->generateReceipt($transaction, $user);

            return redirect()->route('receipts.show', $receipt)
                ->with('success', 'Receipt generated successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to generate receipt: ' . $e->getMessage());
        }
    }

    /**
     * Download receipt PDF
     */
    public function downloadPdf(Receipt $receipt)
    {
        $user = Auth::user();

        if (!$this->canViewReceipt($user, $receipt)) {
            abort(403, 'Unauthorized to download this receipt.');
        }

        try {
            // Generate PDF if it doesn't exist
            if (!$receipt->pdf_path || !Storage::disk('local')->exists($receipt->pdf_path)) {
                \Log::info('PDF missing for receipt, generating...', ['receipt_id' => $receipt->id]);
                $this->receiptService->generateReceiptPDF($receipt);
                $receipt->refresh(); // Reload the model to get updated pdf_path
            }

            // Check again after generation attempt
            if (!$receipt->pdf_path || !Storage::disk('local')->exists($receipt->pdf_path)) {
                return redirect()->back()->with('error', 'Unable to generate PDF for this receipt. Please try again.');
            }

            $filename = 'Receipt_' . $receipt->receipt_number . '.pdf';
            $filePath = storage_path('app/' . $receipt->pdf_path);

            if (!file_exists($filePath)) {
                return redirect()->back()->with('error', 'PDF file not found.');
            }

            return response()->download($filePath, $filename, [
                'Content-Type' => 'application/pdf',
            ]);

        } catch (\Exception $e) {
            \Log::error('PDF download failed', [
                'receipt_id' => $receipt->id,
                'error' => $e->getMessage()
            ]);

            return redirect()->back()->with('error', 'Failed to download PDF: ' . $e->getMessage());
        }
    }

    /**
     * Email receipt
     */
    public function email(Request $request, Receipt $receipt)
    {
        $request->validate([
            'email_addresses' => 'required|array|min:1',
            'email_addresses.*' => 'required|email',
        ]);

        $user = Auth::user();

        if (!$this->canViewReceipt($user, $receipt)) {
            if ($request->expectsJson()) {
                return response()->json(['success' => false, 'message' => 'Unauthorized to email this receipt.'], 403);
            }
            abort(403, 'Unauthorized to email this receipt.');
        }

        try {
            $success = $this->receiptService->emailReceipt($receipt, $request->email_addresses);

            if ($success) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Receipt emailed successfully to ' . count($request->email_addresses) . ' recipient(s).'
                    ]);
                }
                return redirect()->back()->with('success', 'Receipt emailed successfully to ' . count($request->email_addresses) . ' recipient(s).');
            } else {
                if ($request->expectsJson()) {
                    return response()->json(['success' => false, 'message' => 'Failed to email receipt. Please check email addresses and try again.']);
                }
                return redirect()->back()->with('error', 'Failed to email receipt. Please check email addresses and try again.');
            }

        } catch (\Exception $e) {
            \Log::error('Receipt email error', [
                'receipt_id' => $receipt->id,
                'email_addresses' => $request->email_addresses,
                'error' => $e->getMessage()
            ]);

            if ($request->expectsJson()) {
                return response()->json(['success' => false, 'message' => 'Failed to email receipt: ' . $e->getMessage()]);
            }
            return redirect()->back()
                ->with('error', 'Failed to email receipt: ' . $e->getMessage());
        }
    }

    /**
     * Generate bulk receipts
     */
    public function generateBulk(Request $request)
    {
        $request->validate([
            'transaction_ids' => 'required|array|min:1',
            'transaction_ids.*' => 'exists:transactions,id',
        ]);

        $user = Auth::user();

        // Verify user can generate receipts for all transactions
        $transactions = Transaction::whereIn('id', $request->transaction_ids)->get();
        
        foreach ($transactions as $transaction) {
            if (!$this->canGenerateReceipt($user, $transaction)) {
                return redirect()->back()
                    ->with('error', 'Unauthorized to generate receipt for transaction: ' . $transaction->transaction_id);
            }
        }

        try {
            $result = $this->receiptService->generateBulkReceipts($request->transaction_ids, $user);

            $message = "Generated {$result['success_count']} receipts successfully.";
            if ($result['error_count'] > 0) {
                $message .= " {$result['error_count']} failed.";
            }

            return redirect()->back()->with('success', $message);

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to generate bulk receipts: ' . $e->getMessage());
        }
    }

    /**
     * Check if user can generate receipt for transaction
     */
    protected function canGenerateReceipt($user, Transaction $transaction): bool
    {
        $userChurchId = $user->church_id;
        
        // Can generate if transaction involves user's church
        if ($transaction->from_church_id === $userChurchId || $transaction->to_church_id === $userChurchId) {
            return true;
        }

        // Can generate if user can manage the churches involved
        $managedChurches = $this->hierarchyService->getChurchesUserCanManage($user);
        
        return $managedChurches->contains('id', $transaction->from_church_id) ||
               $managedChurches->contains('id', $transaction->to_church_id);
    }

    /**
     * Check if user can view receipt
     */
    protected function canViewReceipt($user, Receipt $receipt): bool
    {
        $userChurchId = $user->church_id;
        
        // Can view if receipt involves user's church
        if ($receipt->issued_to_church_id === $userChurchId || $receipt->issued_by_church_id === $userChurchId) {
            return true;
        }

        // Can view if user can manage the churches involved
        $managedChurches = $this->hierarchyService->getChurchesUserCanManage($user);
        
        return $managedChurches->contains('id', $receipt->issued_to_church_id) ||
               $managedChurches->contains('id', $receipt->issued_by_church_id);
    }

    /**
     * Get receipt template data for preview
     */
    public function preview(Receipt $receipt)
    {
        $user = Auth::user();

        if (!$this->canViewReceipt($user, $receipt)) {
            abort(403, 'Unauthorized to preview this receipt.');
        }

        $templateData = $this->receiptService->getReceiptTemplateData($receipt);

        return view('financial.receipts.preview', compact('receipt', 'templateData'));
    }
}
