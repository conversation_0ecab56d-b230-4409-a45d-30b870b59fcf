@extends('layouts.app')

@section('title', ucfirst($level->value) . ' Churches Report')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
            <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                <i class="fas fa-chart-bar mr-2 text-blue-600"></i>
                {{ ucfirst($level->value) }} {{ __('churches.churches_report') }}
            </h2>
            <p class="mt-1 text-sm text-gray-500">
                {{ __('churches.generated_on') }} {{ $generated_at->format('F j, Y \a\t g:i A') }}
                {{ __('churches.by') }} {{ $generated_by->full_name }}
            </p>
        </div>
        <div class="mt-4 flex space-x-3 md:mt-0 md:ml-4">
            <a href="{{ route('church-reports.index') }}"
               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <i class="fas fa-arrow-left mr-2"></i>
                {{ __('churches.back_to_reports') }}
            </a>
            
            <!-- Export Options -->
            <div class="relative inline-block text-left" x-data="{ open: false }">
                <button @click="open = !open" type="button" 
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-download mr-2"></i>
                    {{ __('churches.export') }}
                    <i class="fas fa-chevron-down ml-2"></i>
                </button>
                
                <div x-show="open" @click.away="open = false" 
                     class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
                    <div class="py-1">
                        <form method="POST" action="{{ route('church-reports.generate') }}" class="inline">
                            @csrf
                            <input type="hidden" name="level" value="{{ $level->value }}">
                            <input type="hidden" name="format" value="pdf">
                            <input type="hidden" name="include_demographics" value="{{ $options['include_demographics'] ? '1' : '0' }}">
                            <input type="hidden" name="include_leadership" value="{{ $options['include_leadership'] ? '1' : '0' }}">
                            <input type="hidden" name="include_statistics" value="{{ $options['include_statistics'] ? '1' : '0' }}">
                            <input type="hidden" name="include_contact_info" value="{{ $options['include_contact_info'] ? '1' : '0' }}">
                            <input type="hidden" name="include_user_details" value="{{ $options['include_user_details'] ? '1' : '0' }}">
                            <input type="hidden" name="include_establishment_info" value="{{ $options['include_establishment_info'] ? '1' : '0' }}">
                            <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-file-pdf mr-2 text-red-600"></i>
                                {{ __('churches.export_pdf') }}
                            </button>
                        </form>
                        <form method="POST" action="{{ route('church-reports.generate') }}" class="inline">
                            @csrf
                            <input type="hidden" name="level" value="{{ $level->value }}">
                            <input type="hidden" name="format" value="excel">
                            <input type="hidden" name="include_demographics" value="{{ $options['include_demographics'] ? '1' : '0' }}">
                            <input type="hidden" name="include_leadership" value="{{ $options['include_leadership'] ? '1' : '0' }}">
                            <input type="hidden" name="include_statistics" value="{{ $options['include_statistics'] ? '1' : '0' }}">
                            <input type="hidden" name="include_contact_info" value="{{ $options['include_contact_info'] ? '1' : '0' }}">
                            <input type="hidden" name="include_user_details" value="{{ $options['include_user_details'] ? '1' : '0' }}">
                            <input type="hidden" name="include_establishment_info" value="{{ $options['include_establishment_info'] ? '1' : '0' }}">
                            <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-file-excel mr-2 text-green-600"></i>
                                {{ __('churches.export_excel') }}
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                {{ __('churches.summary_statistics') }}
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <div class="text-3xl font-bold text-blue-600">{{ $summary['total_churches'] }}</div>
                    <div class="text-sm text-gray-600">{{ __('churches.total_churches') }}</div>
                </div>
                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <div class="text-3xl font-bold text-green-600">{{ $summary['total_users'] }}</div>
                    <div class="text-sm text-gray-600">{{ __('churches.total_users') }}</div>
                </div>
                <div class="text-center p-4 bg-purple-50 rounded-lg">
                    <div class="text-3xl font-bold text-purple-600">{{ $summary['active_users'] }}</div>
                    <div class="text-sm text-gray-600">{{ __('churches.active_users') }}</div>
                </div>
            </div>

            @if($options['include_demographics'] && isset($summary['demographics']))
            <div class="mt-6">
                <h4 class="text-md font-medium text-gray-900 mb-3">{{ __('churches.demographics_breakdown') }}</h4>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center p-3 bg-yellow-50 rounded-lg">
                        <div class="text-2xl font-bold text-yellow-600">{{ $summary['demographics']['total_children'] }}</div>
                        <div class="text-xs text-gray-600">{{ __('churches.children') }}</div>
                    </div>
                    <div class="text-center p-3 bg-blue-50 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600">{{ $summary['demographics']['total_youth'] }}</div>
                        <div class="text-xs text-gray-600">{{ __('churches.youth') }}</div>
                    </div>
                    <div class="text-center p-3 bg-green-50 rounded-lg">
                        <div class="text-2xl font-bold text-green-600">{{ $summary['demographics']['total_young_adults'] }}</div>
                        <div class="text-xs text-gray-600">{{ __('churches.young_adults') }}</div>
                    </div>
                    <div class="text-center p-3 bg-gray-50 rounded-lg">
                        <div class="text-2xl font-bold text-gray-600">{{ $summary['demographics']['total_elders'] }}</div>
                        <div class="text-xs text-gray-600">{{ __('churches.elders') }}</div>
                    </div>
                </div>
            </div>
            @endif

            @if($options['include_contact_info'] && isset($summary['contact_summary']))
            <div class="mt-6">
                <h4 class="text-md font-medium text-gray-900 mb-3">{{ __('churches.contact_summary') }}</h4>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center p-3 bg-blue-50 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600">{{ $summary['contact_summary']['churches_with_email'] }}</div>
                        <div class="text-xs text-gray-600">{{ __('churches.with_email') }}</div>
                    </div>
                    <div class="text-center p-3 bg-green-50 rounded-lg">
                        <div class="text-2xl font-bold text-green-600">{{ $summary['contact_summary']['churches_with_phone'] }}</div>
                        <div class="text-xs text-gray-600">{{ __('churches.with_phone') }}</div>
                    </div>
                    <div class="text-center p-3 bg-purple-50 rounded-lg">
                        <div class="text-2xl font-bold text-purple-600">{{ $summary['contact_summary']['churches_with_address'] }}</div>
                        <div class="text-xs text-gray-600">{{ __('churches.with_address') }}</div>
                    </div>
                    <div class="text-center p-3 bg-orange-50 rounded-lg">
                        <div class="text-2xl font-bold text-orange-600">{{ $summary['contact_summary']['total_contact_points'] }}</div>
                        <div class="text-xs text-gray-600">{{ __('churches.total_contact_points') }}</div>
                    </div>
                </div>
            </div>
            @endif

            @if($options['include_establishment_info'] && isset($summary['establishment_info']))
            <div class="mt-6">
                <h4 class="text-md font-medium text-gray-900 mb-3">{{ __('churches.establishment_summary') }}</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="text-center p-3 bg-indigo-50 rounded-lg">
                        <div class="text-2xl font-bold text-indigo-600">{{ $summary['establishment_info']['churches_with_establishment_date'] }}</div>
                        <div class="text-xs text-gray-600">{{ __('churches.with_establishment_date') }}</div>
                    </div>
                    @if($summary['establishment_info']['oldest_church'])
                    <div class="text-center p-3 bg-gray-50 rounded-lg">
                        <div class="text-lg font-bold text-gray-600">{{ $summary['establishment_info']['oldest_church']->date_established->format('Y') }}</div>
                        <div class="text-xs text-gray-600">{{ __('churches.oldest_church') }}</div>
                        <div class="text-xs text-gray-500">{{ $summary['establishment_info']['oldest_church']->name }}</div>
                    </div>
                    @endif
                    @if($summary['establishment_info']['average_age_years'])
                    <div class="text-center p-3 bg-teal-50 rounded-lg">
                        <div class="text-2xl font-bold text-teal-600">{{ round($summary['establishment_info']['average_age_years']) }}</div>
                        <div class="text-xs text-gray-600">{{ __('churches.average_age_years') }}</div>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            @if($options['include_user_details'] && isset($summary['user_details']))
            <div class="mt-6">
                <h4 class="text-md font-medium text-gray-900 mb-3">{{ __('churches.user_details_summary') }}</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center p-3 bg-cyan-50 rounded-lg">
                            <div class="text-2xl font-bold text-cyan-600">{{ $summary['user_details']['users_with_phone'] }}</div>
                            <div class="text-xs text-gray-600">{{ __('churches.users_with_phone') }}</div>
                        </div>
                        <div class="text-center p-3 bg-emerald-50 rounded-lg">
                            <div class="text-2xl font-bold text-emerald-600">{{ $summary['user_details']['users_with_complete_profile'] }}</div>
                            <div class="text-xs text-gray-600">{{ __('churches.complete_profiles') }}</div>
                        </div>
                    </div>
                    @if(!empty($summary['user_details']['role_distribution']))
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h5 class="text-sm font-medium text-gray-900 mb-2">{{ __('churches.role_distribution') }}</h5>
                        <div class="space-y-1">
                            @foreach($summary['user_details']['role_distribution'] as $role => $count)
                            <div class="flex justify-between text-xs">
                                <span class="text-gray-600">{{ $role }}</span>
                                <span class="font-medium text-gray-900">{{ $count }}</span>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>
    </div>

    <!-- Churches List -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
                {{ ucfirst($level->value) }} {{ __('churches.churches_list') }}
            </h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                {{ __('churches.detailed_information_churches') }}
            </p>
        </div>

        @if($churches->count() > 0)
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ __('churches.church') }}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ __('churches.location') }}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ __('churches.hierarchy') }}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ __('churches.statistics') }}
                        </th>
                        @if($options['include_contact_info'])
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ __('common.contact_information') }}
                        </th>
                        @endif
                        @if($options['include_establishment_info'])
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ __('churches.establishment_info') }}
                        </th>
                        @endif
                        @if($options['include_demographics'])
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ __('churches.demographics') }}
                        </th>
                        @endif
                        @if($options['include_leadership'])
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ __('churches.leadership') }}
                        </th>
                        @endif
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($churches as $church)
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                        <i class="fas fa-church text-blue-600"></i>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ $church->name }}</div>
                                    <div class="text-sm text-gray-500">
                                        {{ __('churches.established') }}: {{ $church->date_established ? $church->date_established->format('Y') : 'N/A' }}
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ $church->location }}</div>
                            <div class="text-sm text-gray-500">
                                @if($church->district || $church->region)
                                    {{ $church->district }}{{ $church->district && $church->region ? ', ' : '' }}{{ $church->region }}
                                @else
                                    N/A
                                @endif
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                {{ __('churches.parent') }}: {{ $church->parentChurch ? $church->parentChurch->name : 'None' }}
                            </div>
                            <div class="text-sm text-gray-500">
                                {{ __('churches.sub_churches') }}: {{ $church->childChurches->count() }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                {{ __('churches.users') }}: {{ $church->users->count() }}
                            </div>
                            <div class="text-sm text-gray-500">
                                {{ __('churches.active') }}: {{ $church->users->where('is_active', true)->count() }}
                            </div>
                        </td>
                        @if($options['include_contact_info'])
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-xs text-gray-600">
                                @if($church->email)
                                    <div class="flex items-center mb-1">
                                        <i class="fas fa-envelope mr-1 text-gray-400"></i>
                                        <span>{{ $church->email }}</span>
                                    </div>
                                @endif
                                @if($church->phone_number)
                                    <div class="flex items-center mb-1">
                                        <i class="fas fa-phone mr-1 text-gray-400"></i>
                                        <span>{{ $church->phone_number }}</span>
                                    </div>
                                @endif
                                @if($church->address)
                                    <div class="flex items-center">
                                        <i class="fas fa-map-marker-alt mr-1 text-gray-400"></i>
                                        <span class="truncate">{{ Str::limit($church->address, 30) }}</span>
                                    </div>
                                @endif
                                @if(!$church->email && !$church->phone_number && !$church->address)
                                    <span class="text-gray-400 italic">{{ __('common.no_contact_info') }}</span>
                                @endif
                            </div>
                        </td>
                        @endif
                        @if($options['include_establishment_info'])
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-xs text-gray-600">
                                @if($church->date_established)
                                    <div class="mb-1">
                                        <strong>{{ __('churches.established') }}:</strong><br>
                                        {{ $church->date_established->format('M j, Y') }}
                                    </div>
                                    <div class="text-gray-500">
                                        {{ __('churches.age') }}: {{ $church->getAgeInYears() }} {{ __('common.years') }}
                                    </div>
                                @else
                                    <span class="text-gray-400 italic">{{ __('churches.establishment_date_unknown') }}</span>
                                @endif
                            </div>
                        </td>
                        @endif
                        @if($options['include_demographics'])
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-xs text-gray-600">
                                <div>{{ __('churches.children') }}: {{ $church->children_count ?? 0 }}</div>
                                <div>{{ __('churches.youth') }}: {{ $church->youth_count ?? 0 }}</div>
                                <div>{{ __('churches.young_adults') }}: {{ $church->young_adults_count ?? 0 }}</div>
                                <div>{{ __('churches.elders') }}: {{ $church->elders_count ?? 0 }}</div>
                            </div>
                        </td>
                        @endif
                        @if($options['include_leadership'])
                        <td class="px-6 py-4">
                            <div class="text-xs text-gray-600">
                                @if($church->leaders->count() > 0)
                                    @foreach($church->leaders as $leader)
                                        @if($leader->user && $leader->user->full_name && $leader->role)
                                            <div>{{ $leader->user->full_name }} ({{ $leader->role }})</div>
                                        @elseif($leader->user && $leader->user->full_name)
                                            <div>{{ $leader->user->full_name }}</div>
                                        @endif
                                    @endforeach
                                @else
                                    <span class="text-gray-400">{{ __('churches.no_leaders_assigned') }}</span>
                                @endif
                            </div>
                        </td>
                        @endif
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @else
        <div class="text-center py-12">
            <i class="fas fa-search text-4xl text-gray-400 mb-4"></i>
            <p class="text-gray-600">{{ __('churches.no_churches_found') }}</p>
        </div>
        @endif
    </div>
</div>
@endsection
