@extends('layouts.app')

@section('title', __('Receipt Details'))

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ __('Receipt Details') }}</h1>
                    <p class="mt-2 text-gray-600">{{ $receipt->receipt_number }}</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('receipts.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-arrow-left mr-2"></i>{{ __('Back to Receipts') }}
                    </a>
                    <a href="{{ route('receipts.pdf', $receipt) }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        <i class="fas fa-file-pdf mr-2"></i>{{ __('Download PDF') }}
                    </a>
                    <a href="{{ route('receipts.preview', $receipt) }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        <i class="fas fa-eye mr-2"></i>{{ __('Preview & Print') }}
                    </a>
                    <button type="button" onclick="emailReceipt()" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-envelope mr-2"></i>{{ __('Email Receipt') }}
                    </button>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Receipt Information -->
                <div class="bg-white shadow rounded-lg mb-8">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                            <i class="fas fa-receipt mr-2 text-blue-600"></i>
                            {{ __('Receipt Information') }}
                        </h3>
                    </div>
                    <div class="px-6 py-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Receipt Number') }}</label>
                                <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md font-mono">{{ $receipt->receipt_number }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Issue Date') }}</label>
                                <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ $receipt->created_at->format('M j, Y H:i') }}</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Receipt Type') }}</label>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                    {{ $receipt->getReceiptTypeLabel() }}
                                </span>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Amount') }}</label>
                                <p class="text-2xl font-bold text-green-600">{{ $receipt->getFormattedAmount() }}</p>
                            </div>
                        </div>

                        @if($receipt->description)
                            <div class="mt-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Description') }}</label>
                                <p class="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ $receipt->description }}</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Church Information -->
                <div class="bg-white shadow rounded-lg mb-8">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                            <i class="fas fa-church mr-2 text-blue-600"></i>
                            {{ __('Church Information') }}
                        </h3>
                    </div>
                    <div class="px-6 py-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                <h4 class="text-sm font-medium text-red-800 mb-3">{{ __('Issued To') }}</h4>
                                <div class="space-y-2">
                                    <p class="text-lg font-semibold text-gray-900">{{ $receipt->issuedToChurch->name }}</p>
                                    <p class="text-sm text-gray-600">{{ $receipt->issuedToChurch->level->value }} Level</p>
                                    <p class="text-sm text-gray-600">{{ $receipt->issuedToChurch->location }}</p>
                                </div>
                            </div>
                            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                <h4 class="text-sm font-medium text-green-800 mb-3">{{ __('Issued By') }}</h4>
                                <div class="space-y-2">
                                    <p class="text-lg font-semibold text-gray-900">{{ $receipt->issuedByChurch->name }}</p>
                                    <p class="text-sm text-gray-600">{{ $receipt->issuedByChurch->level->value }} Level</p>
                                    <p class="text-sm text-gray-600">{{ __('By') }}: {{ $receipt->issuedByUser->full_name }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Transaction Information -->
                @if($receipt->transaction)
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                                <i class="fas fa-exchange-alt mr-2 text-blue-600"></i>
                                {{ __('Related Transaction') }}
                            </h3>
                        </div>
                        <div class="px-6 py-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Transaction ID') }}</label>
                                    <a href="{{ route('transactions.show', $receipt->transaction) }}" class="text-blue-600 hover:text-blue-800 font-mono text-sm">
                                        {{ $receipt->transaction->transaction_id }}
                                    </a>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Transaction Date') }}</label>
                                    <p class="text-sm text-gray-900">{{ $receipt->transaction->created_at->format('M j, Y H:i') }}</p>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Payment Method') }}</label>
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                                        {{ $receipt->transaction->payment_method->getLabel() }}
                                    </span>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Status') }}</label>
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                        {{ $receipt->transaction->status->getLabel() }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Receipt Status -->
                <div class="bg-white shadow rounded-lg mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                            <i class="fas fa-info-circle mr-2 text-blue-600"></i>
                            {{ __('Receipt Status') }}
                        </h3>
                    </div>
                    <div class="px-6 py-6">
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-check text-green-600 text-sm"></i>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">{{ __('Receipt Generated') }}</p>
                                    <p class="text-sm text-gray-500">{{ $receipt->created_at->format('M j, Y H:i') }}</p>
                                </div>
                            </div>
                            
                            @if($receipt->is_emailed)
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-envelope text-blue-600 text-sm"></i>
                                        </div>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">{{ __('Receipt Emailed') }}</p>
                                        <p class="text-sm text-gray-500">{{ $receipt->emailed_at->format('M j, Y H:i') }}</p>
                                    </div>
                                </div>
                            @else
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-envelope text-gray-400 text-sm"></i>
                                        </div>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-500">{{ __('Not Emailed') }}</p>
                                        <p class="text-sm text-gray-400">{{ __('Receipt has not been sent via email') }}</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                            <i class="fas fa-bolt mr-2 text-blue-600"></i>
                            {{ __('Quick Actions') }}
                        </h3>
                    </div>
                    <div class="px-6 py-6">
                        <div class="space-y-3">
                            <a href="{{ route('receipts.pdf', $receipt) }}" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700">
                                <i class="fas fa-download mr-2"></i>{{ __('Download PDF') }}
                            </a>
                            <button type="button" onclick="emailReceipt()" class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                <i class="fas fa-envelope mr-2"></i>{{ __('Email Receipt') }}
                            </button>
                            <a href="{{ route('receipts.preview', $receipt) }}" class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                <i class="fas fa-eye mr-2"></i>{{ __('Preview Receipt') }}
                            </a>
                            @if($receipt->transaction)
                                <a href="{{ route('transactions.show', $receipt->transaction) }}" class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                    <i class="fas fa-exchange-alt mr-2"></i>{{ __('View Transaction') }}
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Email Receipt Modal -->
<div id="emailModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">{{ __('Email Receipt') }}</h3>
                <button type="button" onclick="closeEmailModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form method="POST" action="{{ route('receipts.email', $receipt) }}">
                @csrf
                <div class="mb-4">
                    <label for="email_addresses" class="block text-sm font-medium text-gray-700 mb-2">{{ __('Email Addresses') }}</label>
                    <textarea id="email_addresses" name="email_addresses[]" rows="3" required class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="{{ __('Enter email addresses separated by commas...') }}"></textarea>
                    <p class="mt-1 text-sm text-gray-500">{{ __('Separate multiple email addresses with commas') }}</p>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeEmailModal()" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                        {{ __('Cancel') }}
                    </button>
                    <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                        <i class="fas fa-envelope mr-2"></i>{{ __('Send Receipt') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
function emailReceipt() {
    document.getElementById('emailModal').classList.remove('hidden');
}

function closeEmailModal() {
    document.getElementById('emailModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('emailModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeEmailModal();
    }
});
</script>
@endpush
@endsection
